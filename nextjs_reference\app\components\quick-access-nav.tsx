"use client"

import Link from "next/link"
import { useState, useEffect } from "react"
import { Play, ShoppingBag, Users, Calendar, Briefcase, ChevronUp, ChevronDown, Home } from "lucide-react"
import { cn } from "@/lib/utils"
import { Button } from "@/components/ui/button"
import { usePathname } from "next/navigation"

type FeatureType = "home" | "streaming" | "shopping" | "community" | "events" | "jobs"

interface QuickAccessNavProps {
  currentFeature?: FeatureType
  className?: string
}

export default function QuickAccessNav({ currentFeature = "community", className }: QuickAccessNavProps) {
  const [isCollapsed, setIsCollapsed] = useState(false)
  const [isScrolled, setIsScrolled] = useState(false)
  const pathname = usePathname()

  // Determine current feature based on pathname if not explicitly provided
  useEffect(() => {
    if (pathname.includes("/streaming")) {
      currentFeature = "streaming"
    } else if (pathname.includes("/shopping")) {
      currentFeature = "shopping"
    } else if (pathname.includes("/community")) {
      currentFeature = "community"
    } else if (pathname.includes("/events")) {
      currentFeature = "events"
    } else if (pathname.includes("/jobs")) {
      currentFeature = "jobs"
    } else if (pathname === "/") {
      currentFeature = "home"
    }
  }, [pathname])

  // Handle scroll events to make the nav sticky
  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 100)
    }

    window.addEventListener("scroll", handleScroll)
    return () => window.removeEventListener("scroll", handleScroll)
  }, [])

  const features = [
    {
      type: "home" as FeatureType,
      name: "Home",
      href: "/",
      icon: <Home className="h-5 w-5" />,
      bgColor: "bg-gray-100 group-hover:bg-gray-200",
      textColor: "text-gray-600",
    },
    {
      type: "streaming" as FeatureType,
      name: "Streaming",
      href: "/streaming",
      icon: <Play className="h-5 w-5" />,
      bgColor: "bg-blue-100 group-hover:bg-blue-200",
      textColor: "text-blue-600",
    },
    {
      type: "shopping" as FeatureType,
      name: "Shopping",
      href: "/shopping",
      icon: <ShoppingBag className="h-5 w-5" />,
      bgColor: "bg-emerald-100 group-hover:bg-emerald-200",
      textColor: "text-emerald-600",
    },
    {
      type: "community" as FeatureType,
      name: "Community",
      href: "/community",
      icon: <Users className="h-5 w-5" />,
      bgColor: "bg-purple-100 group-hover:bg-purple-200",
      textColor: "text-purple-600",
    },
    {
      type: "events" as FeatureType,
      name: "Events",
      href: "/events",
      icon: <Calendar className="h-5 w-5" />,
      bgColor: "bg-amber-100 group-hover:bg-amber-200",
      textColor: "text-amber-600",
    },
    {
      type: "jobs" as FeatureType,
      name: "Jobs",
      href: "/jobs",
      icon: <Briefcase className="h-5 w-5" />,
      bgColor: "bg-red-100 group-hover:bg-red-200",
      textColor: "text-red-600",
    },
  ]

  // Filter out current feature
  const filteredFeatures = features.filter((feature) => feature.type !== currentFeature)

  return (
    <div
      className={cn(
        "fixed right-4 z-40 transition-all duration-300 ease-in-out",
        isScrolled ? "bottom-4" : "bottom-4 md:bottom-8",
        isCollapsed ? "w-14" : "w-14 md:w-auto",
        className,
      )}
    >
      <div className="bg-white rounded-lg shadow-lg border overflow-hidden">
        <div className="p-2 flex justify-between items-center border-b">
          <span className={cn("text-sm font-medium text-gray-700", isCollapsed ? "hidden" : "hidden md:block")}>
            Quick Access
          </span>
          <Button
            variant="ghost"
            size="sm"
            className="h-8 w-8 p-0 rounded-full"
            onClick={() => setIsCollapsed(!isCollapsed)}
          >
            {isCollapsed ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />}
          </Button>
        </div>

        <div className={cn("flex flex-col md:flex-row p-2 gap-2", isCollapsed ? "items-center" : "")}>
          {filteredFeatures.map((feature) => (
            <Link
              key={feature.type}
              href={feature.href}
              className="group flex items-center gap-2 p-2 rounded-md hover:bg-gray-50 transition-colors"
            >
              <div className={cn("h-10 w-10 rounded-full flex items-center justify-center", feature.bgColor)}>
                <span className={feature.textColor}>{feature.icon}</span>
              </div>
              <span className={cn("text-sm font-medium", isCollapsed ? "hidden" : "hidden md:block")}>
                {feature.name}
              </span>
            </Link>
          ))}
        </div>
      </div>
    </div>
  )
}

