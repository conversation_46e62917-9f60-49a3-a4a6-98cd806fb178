"""
Models for jobs app.

Handles job listings, applications, company profiles, and career management for the Kominote platform.
Designed specifically for Seychelles job market with local features and multi-language support.
"""
from django.db import models
from django.contrib.auth import get_user_model
from django.core.validators import MinValueValidator, MaxValueValidator, RegexValidator
from django.utils import timezone
from django.urls import reverse
from decimal import Decimal

User = get_user_model()


class JobCategory(models.Model):
    """
    Categories for organizing jobs (Technology, Tourism, Finance, Healthcare, etc.).
    """
    name = models.CharField(
        max_length=100,
        unique=True,
        help_text="Category name (e.g., Technology, Tourism, Finance)"
    )
    name_fr = models.Char<PERSON>ield(
        max_length=100,
        blank=True,
        help_text="Category name in French"
    )
    name_cr = models.CharField(
        max_length=100,
        blank=True,
        help_text="Category name in Creole"
    )
    slug = models.SlugField(
        max_length=100,
        unique=True,
        help_text="URL-friendly version of the name"
    )
    description = models.TextField(
        blank=True,
        help_text="Description of this job category"
    )
    icon = models.CharField(
        max_length=50,
        blank=True,
        help_text="Icon class name for display"
    )
    color = models.CharField(
        max_length=7,
        default='#ef4444',
        help_text="Hex color code for category display"
    )
    is_active = models.BooleanField(
        default=True,
        help_text="Whether this category is active"
    )
    sort_order = models.PositiveIntegerField(
        default=0,
        help_text="Order for displaying categories"
    )

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'jobs_category'
        verbose_name = 'Job Category'
        verbose_name_plural = 'Job Categories'
        ordering = ['sort_order', 'name']

    def __str__(self):
        return self.name

    def get_absolute_url(self):
        return reverse('jobs:category', kwargs={'slug': self.slug})


class Company(models.Model):
    """
    Company profiles for employers in Seychelles.
    """
    COMPANY_SIZE_CHOICES = [
        ('startup', '1-10 employees'),
        ('small', '11-50 employees'),
        ('medium', '51-200 employees'),
        ('large', '201-1000 employees'),
        ('enterprise', '1000+ employees'),
    ]

    COMPANY_TYPE_CHOICES = [
        ('private', 'Private Company'),
        ('public', 'Public Company'),
        ('government', 'Government Agency'),
        ('nonprofit', 'Non-Profit Organization'),
        ('startup', 'Startup'),
        ('multinational', 'Multinational Corporation'),
    ]

    ISLAND_CHOICES = [
        ('mahe', 'Mahé'),
        ('praslin', 'Praslin'),
        ('la_digue', 'La Digue'),
        ('silhouette', 'Silhouette'),
        ('fregate', 'Fregate'),
        ('bird', 'Bird Island'),
        ('denis', 'Denis Island'),
        ('other', 'Other'),
    ]

    # Basic Information
    name = models.CharField(
        max_length=200,
        help_text="Company name"
    )
    slug = models.SlugField(
        max_length=200,
        unique=True,
        help_text="URL-friendly version of the name"
    )
    description = models.TextField(
        help_text="Company description and overview"
    )
    short_description = models.TextField(
        max_length=500,
        blank=True,
        help_text="Short description for listings"
    )

    # Company Details
    company_type = models.CharField(
        max_length=20,
        choices=COMPANY_TYPE_CHOICES,
        default='private',
        help_text="Type of company"
    )
    company_size = models.CharField(
        max_length=20,
        choices=COMPANY_SIZE_CHOICES,
        default='small',
        help_text="Company size by employee count"
    )
    founded_year = models.PositiveIntegerField(
        null=True,
        blank=True,
        validators=[MinValueValidator(1800), MaxValueValidator(2030)],
        help_text="Year the company was founded"
    )

    # Location
    headquarters = models.TextField(
        help_text="Company headquarters address"
    )
    island = models.CharField(
        max_length=20,
        choices=ISLAND_CHOICES,
        help_text="Primary island location in Seychelles"
    )
    district = models.CharField(
        max_length=100,
        blank=True,
        help_text="District or area"
    )

    # Contact Information
    website = models.URLField(
        blank=True,
        help_text="Company website URL"
    )
    email = models.EmailField(
        help_text="Company contact email"
    )
    phone = models.CharField(
        max_length=20,
        blank=True,
        help_text="Company phone number"
    )

    # Social Media
    linkedin_url = models.URLField(blank=True)
    facebook_url = models.URLField(blank=True)
    twitter_url = models.URLField(blank=True)
    instagram_url = models.URLField(blank=True)

    # Media
    logo = models.ImageField(
        upload_to='companies/logos/',
        null=True,
        blank=True,
        help_text="Company logo"
    )
    cover_image = models.ImageField(
        upload_to='companies/covers/',
        null=True,
        blank=True,
        help_text="Company cover image"
    )

    # Verification and Status
    is_verified = models.BooleanField(
        default=False,
        help_text="Whether this company is verified"
    )
    is_featured = models.BooleanField(
        default=False,
        help_text="Whether this company is featured"
    )
    is_hiring = models.BooleanField(
        default=True,
        help_text="Whether this company is actively hiring"
    )

    # Seychelles-specific fields
    is_local_company = models.BooleanField(
        default=True,
        help_text="Whether this is a local Seychelles company"
    )
    supports_local_talent = models.BooleanField(
        default=False,
        help_text="Whether this company actively supports local talent development"
    )

    # Statistics
    total_jobs = models.PositiveIntegerField(
        default=0,
        help_text="Total number of active job postings"
    )
    total_employees = models.PositiveIntegerField(
        default=0,
        help_text="Total number of employees"
    )
    average_rating = models.DecimalField(
        max_digits=3,
        decimal_places=2,
        default=Decimal('0.00'),
        help_text="Average rating from employee reviews"
    )
    total_reviews = models.PositiveIntegerField(
        default=0,
        help_text="Total number of company reviews"
    )

    # SEO
    meta_title = models.CharField(
        max_length=200,
        blank=True,
        help_text="SEO meta title"
    )
    meta_description = models.TextField(
        max_length=500,
        blank=True,
        help_text="SEO meta description"
    )

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'jobs_company'
        verbose_name = 'Company'
        verbose_name_plural = 'Companies'
        ordering = ['name']
        indexes = [
            models.Index(fields=['island', 'district']),
            models.Index(fields=['company_type']),
            models.Index(fields=['is_verified', 'is_hiring']),
            models.Index(fields=['is_featured', '-created_at']),
        ]

    def __str__(self):
        return self.name

    def get_absolute_url(self):
        return reverse('jobs:company', kwargs={'slug': self.slug})


class JobSkill(models.Model):
    """
    Skills and requirements for jobs.
    """
    SKILL_TYPE_CHOICES = [
        ('technical', 'Technical Skill'),
        ('soft', 'Soft Skill'),
        ('language', 'Language'),
        ('certification', 'Certification'),
        ('education', 'Education'),
    ]

    name = models.CharField(
        max_length=100,
        unique=True,
        help_text="Skill name"
    )
    slug = models.SlugField(
        max_length=100,
        unique=True,
        help_text="URL-friendly version of the name"
    )
    skill_type = models.CharField(
        max_length=20,
        choices=SKILL_TYPE_CHOICES,
        default='technical',
        help_text="Type of skill"
    )
    description = models.TextField(
        blank=True,
        help_text="Skill description"
    )
    is_active = models.BooleanField(
        default=True,
        help_text="Whether this skill is active"
    )

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'jobs_skill'
        verbose_name = 'Job Skill'
        verbose_name_plural = 'Job Skills'
        ordering = ['name']

    def __str__(self):
        return self.name


class Job(models.Model):
    """
    Main job listing model with comprehensive features for Seychelles job market.
    """
    JOB_TYPE_CHOICES = [
        ('full_time', 'Full-time'),
        ('part_time', 'Part-time'),
        ('contract', 'Contract'),
        ('temporary', 'Temporary'),
        ('internship', 'Internship'),
        ('freelance', 'Freelance'),
        ('remote', 'Remote'),
    ]

    JOB_STATUS_CHOICES = [
        ('draft', 'Draft'),
        ('published', 'Published'),
        ('paused', 'Paused'),
        ('closed', 'Closed'),
        ('filled', 'Filled'),
    ]

    EXPERIENCE_LEVEL_CHOICES = [
        ('entry', 'Entry Level'),
        ('junior', 'Junior (1-3 years)'),
        ('mid', 'Mid Level (3-5 years)'),
        ('senior', 'Senior (5-10 years)'),
        ('lead', 'Lead (10+ years)'),
        ('executive', 'Executive'),
    ]

    EDUCATION_LEVEL_CHOICES = [
        ('none', 'No formal education required'),
        ('high_school', 'High School'),
        ('diploma', 'Diploma'),
        ('bachelor', 'Bachelor\'s Degree'),
        ('master', 'Master\'s Degree'),
        ('phd', 'PhD'),
        ('professional', 'Professional Certification'),
    ]

    # Basic Information
    title = models.CharField(
        max_length=200,
        help_text="Job title"
    )
    slug = models.SlugField(
        max_length=200,
        unique=True,
        help_text="URL-friendly version of the title"
    )
    description = models.TextField(
        help_text="Detailed job description"
    )
    summary = models.TextField(
        max_length=500,
        help_text="Brief job summary"
    )

    # Company and Category
    company = models.ForeignKey(
        Company,
        on_delete=models.CASCADE,
        related_name='jobs',
        help_text="Hiring company"
    )
    category = models.ForeignKey(
        JobCategory,
        on_delete=models.PROTECT,
        related_name='jobs',
        help_text="Job category"
    )

    # Job Details
    job_type = models.CharField(
        max_length=20,
        choices=JOB_TYPE_CHOICES,
        default='full_time',
        help_text="Type of employment"
    )
    experience_level = models.CharField(
        max_length=20,
        choices=EXPERIENCE_LEVEL_CHOICES,
        default='mid',
        help_text="Required experience level"
    )
    education_level = models.CharField(
        max_length=20,
        choices=EDUCATION_LEVEL_CHOICES,
        default='bachelor',
        help_text="Required education level"
    )

    # Location
    location = models.CharField(
        max_length=200,
        help_text="Job location"
    )
    island = models.CharField(
        max_length=20,
        choices=Company.ISLAND_CHOICES,
        help_text="Island location in Seychelles"
    )
    district = models.CharField(
        max_length=100,
        blank=True,
        help_text="District or area"
    )
    is_remote = models.BooleanField(
        default=False,
        help_text="Whether this job can be done remotely"
    )

    # Salary and Benefits
    salary_min = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        null=True,
        blank=True,
        help_text="Minimum salary"
    )
    salary_max = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        null=True,
        blank=True,
        help_text="Maximum salary"
    )
    salary_currency = models.CharField(
        max_length=3,
        default='SCR',
        help_text="Currency code (SCR for Seychelles Rupee)"
    )
    salary_period = models.CharField(
        max_length=20,
        choices=[
            ('hourly', 'Per Hour'),
            ('daily', 'Per Day'),
            ('weekly', 'Per Week'),
            ('monthly', 'Per Month'),
            ('yearly', 'Per Year'),
        ],
        default='monthly',
        help_text="Salary payment period"
    )
    benefits = models.TextField(
        blank=True,
        help_text="Job benefits and perks"
    )

    # Requirements
    requirements = models.TextField(
        help_text="Job requirements and qualifications"
    )
    responsibilities = models.TextField(
        help_text="Job responsibilities and duties"
    )
    skills = models.ManyToManyField(
        JobSkill,
        blank=True,
        related_name='jobs',
        help_text="Required skills"
    )

    # Application Details
    application_deadline = models.DateField(
        null=True,
        blank=True,
        help_text="Application deadline"
    )
    application_email = models.EmailField(
        blank=True,
        help_text="Email for applications"
    )
    application_url = models.URLField(
        blank=True,
        help_text="External application URL"
    )
    how_to_apply = models.TextField(
        blank=True,
        help_text="Instructions on how to apply"
    )

    # Status and Visibility
    status = models.CharField(
        max_length=20,
        choices=JOB_STATUS_CHOICES,
        default='draft',
        help_text="Job status"
    )
    is_featured = models.BooleanField(
        default=False,
        help_text="Whether this job is featured"
    )
    is_urgent = models.BooleanField(
        default=False,
        help_text="Whether this is an urgent hiring"
    )

    # Seychelles-specific fields
    is_local_job = models.BooleanField(
        default=True,
        help_text="Whether this is a local Seychelles job"
    )
    supports_local_talent = models.BooleanField(
        default=False,
        help_text="Whether this job prioritizes local talent"
    )
    work_permit_sponsored = models.BooleanField(
        default=False,
        help_text="Whether work permit sponsorship is available"
    )

    # Statistics
    total_applications = models.PositiveIntegerField(
        default=0,
        help_text="Total number of applications"
    )
    total_views = models.PositiveIntegerField(
        default=0,
        help_text="Total number of page views"
    )
    total_saves = models.PositiveIntegerField(
        default=0,
        help_text="Total number of saves/bookmarks"
    )

    # SEO
    meta_title = models.CharField(
        max_length=200,
        blank=True,
        help_text="SEO meta title"
    )
    meta_description = models.TextField(
        max_length=500,
        blank=True,
        help_text="SEO meta description"
    )

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    published_at = models.DateTimeField(
        null=True,
        blank=True,
        help_text="When the job was published"
    )

    class Meta:
        db_table = 'jobs_job'
        verbose_name = 'Job'
        verbose_name_plural = 'Jobs'
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['status', '-created_at']),
            models.Index(fields=['category', '-created_at']),
            models.Index(fields=['company', '-created_at']),
            models.Index(fields=['job_type', '-created_at']),
            models.Index(fields=['island', 'district']),
            models.Index(fields=['is_featured', '-created_at']),
            models.Index(fields=['experience_level']),
        ]

    def __str__(self):
        return f"{self.title} at {self.company.name}"

    def get_absolute_url(self):
        return reverse('jobs:detail', kwargs={'slug': self.slug})

    @property
    def is_active(self):
        """Check if job is active and accepting applications."""
        if self.status != 'published':
            return False
        if self.application_deadline and timezone.now().date() > self.application_deadline:
            return False
        return True

    @property
    def days_since_posted(self):
        """Calculate days since job was posted."""
        if self.published_at:
            return (timezone.now() - self.published_at).days
        return (timezone.now() - self.created_at).days

    @property
    def salary_range_display(self):
        """Display salary range as formatted string."""
        if self.salary_min and self.salary_max:
            return f"{self.salary_currency} {self.salary_min:,.0f} - {self.salary_max:,.0f} {self.salary_period}"
        elif self.salary_min:
            return f"{self.salary_currency} {self.salary_min:,.0f}+ {self.salary_period}"
        elif self.salary_max:
            return f"Up to {self.salary_currency} {self.salary_max:,.0f} {self.salary_period}"
        return "Salary not specified"

    def can_apply(self):
        """Check if applications are still being accepted."""
        return self.is_active and self.status == 'published'


class JobSeekerProfile(models.Model):
    """
    Extended profile for job seekers.
    """
    AVAILABILITY_CHOICES = [
        ('immediately', 'Immediately'),
        ('2_weeks', 'Within 2 weeks'),
        ('1_month', 'Within 1 month'),
        ('3_months', 'Within 3 months'),
        ('not_looking', 'Not actively looking'),
    ]

    user = models.OneToOneField(
        User,
        on_delete=models.CASCADE,
        related_name='job_seeker_profile'
    )

    # Professional Information
    headline = models.CharField(
        max_length=200,
        blank=True,
        help_text="Professional headline or title"
    )
    summary = models.TextField(
        blank=True,
        help_text="Professional summary"
    )
    experience_years = models.PositiveIntegerField(
        null=True,
        blank=True,
        help_text="Years of professional experience"
    )

    # Current Status
    current_position = models.CharField(
        max_length=200,
        blank=True,
        help_text="Current job title"
    )
    current_company = models.CharField(
        max_length=200,
        blank=True,
        help_text="Current company name"
    )
    availability = models.CharField(
        max_length=20,
        choices=AVAILABILITY_CHOICES,
        default='1_month',
        help_text="Availability for new opportunities"
    )

    # Preferences
    desired_job_types = models.CharField(
        max_length=200,
        blank=True,
        help_text="Comma-separated desired job types"
    )
    desired_salary_min = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        null=True,
        blank=True,
        help_text="Minimum desired salary"
    )
    desired_salary_max = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        null=True,
        blank=True,
        help_text="Maximum desired salary"
    )
    preferred_locations = models.CharField(
        max_length=500,
        blank=True,
        help_text="Comma-separated preferred work locations"
    )
    willing_to_relocate = models.BooleanField(
        default=False,
        help_text="Willing to relocate for work"
    )
    open_to_remote = models.BooleanField(
        default=True,
        help_text="Open to remote work opportunities"
    )

    # Skills and Education
    skills = models.ManyToManyField(
        JobSkill,
        blank=True,
        related_name='job_seekers',
        help_text="Professional skills"
    )
    education = models.TextField(
        blank=True,
        help_text="Education background"
    )
    certifications = models.TextField(
        blank=True,
        help_text="Professional certifications"
    )

    # Documents
    resume = models.FileField(
        upload_to='resumes/',
        null=True,
        blank=True,
        help_text="Resume/CV file"
    )
    cover_letter_template = models.TextField(
        blank=True,
        help_text="Default cover letter template"
    )
    portfolio_url = models.URLField(
        blank=True,
        help_text="Portfolio website URL"
    )

    # Privacy Settings
    profile_visibility = models.CharField(
        max_length=20,
        choices=[
            ('public', 'Public'),
            ('employers_only', 'Employers Only'),
            ('private', 'Private'),
        ],
        default='employers_only',
        help_text="Who can see your profile"
    )
    allow_contact = models.BooleanField(
        default=True,
        help_text="Allow employers to contact directly"
    )

    # Statistics
    profile_views = models.PositiveIntegerField(
        default=0,
        help_text="Number of profile views"
    )
    applications_sent = models.PositiveIntegerField(
        default=0,
        help_text="Total applications sent"
    )

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'jobs_jobseekerprofile'
        verbose_name = 'Job Seeker Profile'
        verbose_name_plural = 'Job Seeker Profiles'

    def __str__(self):
        return f"{self.user.get_display_name()}'s Job Profile"

    def get_desired_job_types_list(self):
        """Return desired job types as a list."""
        if self.desired_job_types:
            return [jtype.strip() for jtype in self.desired_job_types.split(',')]
        return []

    def get_preferred_locations_list(self):
        """Return preferred locations as a list."""
        if self.preferred_locations:
            return [loc.strip() for loc in self.preferred_locations.split(',')]
        return []


class JobApplication(models.Model):
    """
    Job applications submitted by users.
    """
    APPLICATION_STATUS_CHOICES = [
        ('submitted', 'Submitted'),
        ('under_review', 'Under Review'),
        ('shortlisted', 'Shortlisted'),
        ('interview_scheduled', 'Interview Scheduled'),
        ('interviewed', 'Interviewed'),
        ('offer_made', 'Offer Made'),
        ('accepted', 'Accepted'),
        ('rejected', 'Rejected'),
        ('withdrawn', 'Withdrawn'),
    ]

    job = models.ForeignKey(
        Job,
        on_delete=models.CASCADE,
        related_name='applications',
        help_text="Job being applied for"
    )
    applicant = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='job_applications',
        help_text="User applying for the job"
    )
    status = models.CharField(
        max_length=20,
        choices=APPLICATION_STATUS_CHOICES,
        default='submitted',
        help_text="Application status"
    )

    # Application Content
    cover_letter = models.TextField(
        help_text="Cover letter content"
    )
    resume = models.FileField(
        upload_to='applications/resumes/',
        help_text="Resume/CV file for this application"
    )
    additional_documents = models.FileField(
        upload_to='applications/documents/',
        null=True,
        blank=True,
        help_text="Additional documents (portfolio, certificates, etc.)"
    )

    # Application Details
    expected_salary = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        null=True,
        blank=True,
        help_text="Expected salary for this position"
    )
    available_start_date = models.DateField(
        null=True,
        blank=True,
        help_text="When can you start working"
    )
    additional_notes = models.TextField(
        blank=True,
        help_text="Additional notes or information"
    )

    # Employer Actions
    employer_notes = models.TextField(
        blank=True,
        help_text="Internal notes from employer"
    )
    reviewed_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='reviewed_applications',
        help_text="Employer who reviewed this application"
    )
    reviewed_at = models.DateTimeField(
        null=True,
        blank=True,
        help_text="When the application was reviewed"
    )

    # Interview Details
    interview_scheduled_date = models.DateTimeField(
        null=True,
        blank=True,
        help_text="Scheduled interview date and time"
    )
    interview_location = models.CharField(
        max_length=200,
        blank=True,
        help_text="Interview location or video call link"
    )
    interview_notes = models.TextField(
        blank=True,
        help_text="Interview notes and feedback"
    )

    # Timestamps
    applied_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'jobs_application'
        verbose_name = 'Job Application'
        verbose_name_plural = 'Job Applications'
        unique_together = ['job', 'applicant']
        ordering = ['-applied_at']
        indexes = [
            models.Index(fields=['job', 'status']),
            models.Index(fields=['applicant', 'status']),
            models.Index(fields=['status', '-applied_at']),
        ]

    def __str__(self):
        return f"{self.applicant.get_display_name()} - {self.job.title}"

    @property
    def days_since_applied(self):
        """Calculate days since application was submitted."""
        return (timezone.now() - self.applied_at).days

    def can_withdraw(self):
        """Check if application can be withdrawn."""
        return self.status in ['submitted', 'under_review', 'shortlisted']


class SavedJob(models.Model):
    """
    Jobs saved/bookmarked by users.
    """
    user = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='saved_jobs',
        help_text="User who saved the job"
    )
    job = models.ForeignKey(
        Job,
        on_delete=models.CASCADE,
        related_name='saved_by_users',
        help_text="Job that was saved"
    )
    notes = models.TextField(
        blank=True,
        help_text="Personal notes about this job"
    )

    # Timestamps
    saved_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        db_table = 'jobs_savedjob'
        verbose_name = 'Saved Job'
        verbose_name_plural = 'Saved Jobs'
        unique_together = ['user', 'job']
        ordering = ['-saved_at']

    def __str__(self):
        return f"{self.user.get_display_name()} saved {self.job.title}"


class CompanyReview(models.Model):
    """
    Reviews and ratings for companies by employees/former employees.
    """
    company = models.ForeignKey(
        Company,
        on_delete=models.CASCADE,
        related_name='reviews',
        help_text="Company being reviewed"
    )
    reviewer = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='company_reviews',
        help_text="User writing the review"
    )

    # Review Content
    overall_rating = models.PositiveIntegerField(
        validators=[MinValueValidator(1), MaxValueValidator(5)],
        help_text="Overall rating from 1 to 5 stars"
    )
    title = models.CharField(
        max_length=200,
        help_text="Review title"
    )
    pros = models.TextField(
        help_text="What you liked about working here"
    )
    cons = models.TextField(
        help_text="What could be improved"
    )
    advice_to_management = models.TextField(
        blank=True,
        help_text="Advice to management"
    )

    # Category Ratings
    work_life_balance_rating = models.PositiveIntegerField(
        validators=[MinValueValidator(1), MaxValueValidator(5)],
        null=True,
        blank=True,
        help_text="Work-life balance rating"
    )
    salary_benefits_rating = models.PositiveIntegerField(
        validators=[MinValueValidator(1), MaxValueValidator(5)],
        null=True,
        blank=True,
        help_text="Salary and benefits rating"
    )
    career_opportunities_rating = models.PositiveIntegerField(
        validators=[MinValueValidator(1), MaxValueValidator(5)],
        null=True,
        blank=True,
        help_text="Career opportunities rating"
    )
    management_rating = models.PositiveIntegerField(
        validators=[MinValueValidator(1), MaxValueValidator(5)],
        null=True,
        blank=True,
        help_text="Management quality rating"
    )
    culture_values_rating = models.PositiveIntegerField(
        validators=[MinValueValidator(1), MaxValueValidator(5)],
        null=True,
        blank=True,
        help_text="Company culture and values rating"
    )

    # Employment Details
    job_title = models.CharField(
        max_length=200,
        help_text="Job title when working at this company"
    )
    employment_status = models.CharField(
        max_length=20,
        choices=[
            ('current', 'Current Employee'),
            ('former', 'Former Employee'),
            ('intern', 'Former Intern'),
        ],
        default='former',
        help_text="Employment status"
    )
    employment_duration = models.CharField(
        max_length=100,
        blank=True,
        help_text="How long you worked there"
    )

    # Verification
    is_verified = models.BooleanField(
        default=False,
        help_text="Whether this review is from a verified employee"
    )

    # Moderation
    is_approved = models.BooleanField(
        default=True,
        help_text="Whether this review is approved"
    )
    moderated_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='moderated_company_reviews',
        help_text="Admin who moderated this review"
    )
    moderation_notes = models.TextField(
        blank=True,
        help_text="Notes from moderation"
    )

    # Engagement
    helpful_votes = models.PositiveIntegerField(
        default=0,
        help_text="Number of helpful votes"
    )

    # Recommendation
    would_recommend = models.BooleanField(
        null=True,
        blank=True,
        help_text="Would recommend this company to a friend"
    )

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'jobs_companyreview'
        verbose_name = 'Company Review'
        verbose_name_plural = 'Company Reviews'
        unique_together = ['company', 'reviewer']
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['company', 'is_approved']),
            models.Index(fields=['overall_rating', '-created_at']),
            models.Index(fields=['employment_status']),
        ]

    def __str__(self):
        return f"{self.reviewer.get_display_name()} - {self.company.name} ({self.overall_rating}★)"

    @property
    def average_category_rating(self):
        """Calculate average of category ratings."""
        ratings = [
            r for r in [
                self.work_life_balance_rating,
                self.salary_benefits_rating,
                self.career_opportunities_rating,
                self.management_rating,
                self.culture_values_rating
            ] if r
        ]
        if ratings:
            return sum(ratings) / len(ratings)
        return self.overall_rating
