"use client"

import Link from "next/link"
import Image from "next/image"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Ta<PERSON>, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import {
  Search,
  Users,
  MessageSquare,
  Bell,
  ChevronDown,
  Heart,
  MessageCircle,
  Share2,
  Bookmark,
  TrendingUp,
  Hash,
  Globe,
  Shield,
  Filter,
  Plus,
  ArrowRight,
  Sparkles,
} from "lucide-react"
import FeatureNavigation from "@/components/feature-navigation"
import { Messenger } from "@/components/messenger"

// Rest of the community page component...
export default function CommunityPage() {
  // Component implementation...

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Community page content */}
      {/* Header */}
      <header className="sticky top-0 z-50 w-full bg-white border-b shadow-sm">
        <div className="container flex h-16 items-center justify-between px-4">
          <div className="flex items-center gap-8">
            <Link href="/" className="flex items-center space-x-2">
              <span className="text-2xl font-bold bg-gradient-to-r from-blue-600 to-teal-500 bg-clip-text text-transparent">
                Kominote
              </span>
            </Link>
            <div className="hidden md:flex relative w-full max-w-md">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <Input
                type="search"
                placeholder="Search discussions, groups, or users..."
                className="pl-10 pr-4 py-2 w-full border-purple-200 focus:border-purple-500"
              />
            </div>
          </div>
          <div className="flex items-center gap-4">
            <Button variant="ghost" size="icon" className="text-gray-600 hover:text-purple-600 relative">
              <Bell className="h-5 w-5" />
              <span className="sr-only">Notifications</span>
              <Badge className="absolute -top-1 -right-1 h-5 w-5 flex items-center justify-center p-0 bg-purple-600">
                5
              </Badge>
            </Button>
            <Button variant="ghost" size="icon" className="text-gray-600 hover:text-purple-600 relative">
              <MessageSquare className="h-5 w-5" />
              <span className="sr-only">Messages</span>
              <Badge className="absolute -top-1 -right-1 h-5 w-5 flex items-center justify-center p-0 bg-purple-600">
                3
              </Badge>
            </Button>
            <div className="flex items-center gap-2">
              <Button variant="ghost" className="text-gray-600 hover:text-purple-600">
                <div className="h-8 w-8 rounded-full bg-purple-100 mr-2 overflow-hidden">
                  <Image
                    src="/placeholder.svg?height=32&width=32"
                    width={32}
                    height={32}
                    alt="Profile"
                    className="object-cover"
                  />
                </div>
                <span className="hidden md:inline">Sarah Johnson</span>
                <ChevronDown className="h-4 w-4 ml-1" />
              </Button>
            </div>
          </div>
        </div>
        <div className="container px-4 py-2 border-t border-gray-100">
          <nav className="flex items-center gap-6 overflow-x-auto pb-2 md:pb-0">
            <Link
              href="/community"
              className="text-sm font-medium whitespace-nowrap text-purple-600 border-b-2 border-purple-600 pb-1"
            >
              Home
            </Link>
            <Link
              href="/community/forums"
              className="text-sm font-medium whitespace-nowrap text-gray-600 hover:text-purple-600"
            >
              Forums
            </Link>
            <Link
              href="/community/groups"
              className="text-sm font-medium whitespace-nowrap text-gray-600 hover:text-purple-600"
            >
              Groups
            </Link>
            <Link
              href="/community/members"
              className="text-sm font-medium whitespace-nowrap text-gray-600 hover:text-purple-600"
            >
              Members
            </Link>
            <Link
              href="/community/activity"
              className="text-sm font-medium whitespace-nowrap text-gray-600 hover:text-purple-600"
            >
              Activity
            </Link>
          </nav>
        </div>
      </header>

      <main>
        {/* Welcome Dashboard */}
        <section className="relative bg-gradient-to-r from-purple-600 to-indigo-500 text-white py-8 md:py-12">
          <div className="container px-4 md:px-6">
            <div className="flex flex-col md:flex-row items-center justify-between gap-6">
              <div className="space-y-2">
                <h1 className="text-2xl md:text-3xl font-bold">Welcome back, Sarah!</h1>
                <p className="text-purple-100">Connect with your community and explore what's new today.</p>
              </div>
              <div className="flex flex-wrap gap-3">
                <Button className="bg-white text-purple-600 hover:bg-purple-50">
                  <Plus className="mr-2 h-4 w-4" /> Create Post
                </Button>
                <Button variant="outline" className="bg-transparent border-white text-white hover:bg-white/10">
                  <Users className="mr-2 h-4 w-4" /> Find Friends
                </Button>
              </div>
            </div>
          </div>
        </section>

        {/* Main Content */}
        <section className="py-8">
          <div className="container px-4 md:px-6">
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
              {/* Left Sidebar */}
              <div className="space-y-8">
                {/* User Profile Card */}
                <div className="bg-white rounded-lg shadow-sm p-6">
                  <div className="flex items-center gap-4 mb-4">
                    <div className="h-16 w-16 rounded-full bg-purple-100 overflow-hidden">
                      <Image
                        src="/placeholder.svg?height=64&width=64"
                        width={64}
                        height={64}
                        alt="Profile"
                        className="object-cover"
                      />
                    </div>
                    <div>
                      <h3 className="font-bold text-lg">Sarah Johnson</h3>
                      <p className="text-sm text-gray-500">@sarahjohnson</p>
                    </div>
                  </div>
                  <div className="grid grid-cols-3 gap-2 mb-4 text-center">
                    <div className="p-2">
                      <p className="font-bold text-purple-600">248</p>
                      <p className="text-xs text-gray-500">Posts</p>
                    </div>
                    <div className="p-2">
                      <p className="font-bold text-purple-600">1.2K</p>
                      <p className="text-xs text-gray-500">Followers</p>
                    </div>
                    <div className="p-2">
                      <p className="font-bold text-purple-600">364</p>
                      <p className="text-xs text-gray-500">Following</p>
                    </div>
                  </div>
                  <Button className="w-full bg-purple-600 hover:bg-purple-700">Edit Profile</Button>
                </div>

                {/* Your Groups */}
                <div className="bg-white rounded-lg shadow-sm p-6">
                  <div className="flex items-center justify-between mb-4">
                    <h3 className="font-bold text-lg">Your Groups</h3>
                    <Link href="/community/groups" className="text-sm text-purple-600 hover:text-purple-700">
                      View All
                    </Link>
                  </div>
                  <div className="space-y-4">
                    {[
                      {
                        name: "Seychelles Photography",
                        unread: 3,
                        icon: <Camera className="h-5 w-5 text-purple-600" />,
                      },
                      {
                        name: "Local Cuisine Lovers",
                        unread: 0,
                        icon: <Utensils className="h-5 w-5 text-purple-600" />,
                      },
                      { name: "Island Adventures", unread: 7, icon: <Map className="h-5 w-5 text-purple-600" /> },
                    ].map((group, index) => (
                      <Link
                        href={`/community/groups/${group.name.toLowerCase().replace(/\s+/g, "-")}`}
                        key={index}
                        className="flex items-center gap-3 hover:bg-purple-50 p-2 rounded-md transition-colors"
                      >
                        <div className="h-10 w-10 rounded-full bg-purple-100 flex items-center justify-center">
                          {group.icon}
                        </div>
                        <div className="flex-1">
                          <h4 className="font-medium text-sm">{group.name}</h4>
                        </div>
                        {group.unread > 0 && <Badge className="bg-purple-600">{group.unread}</Badge>}
                      </Link>
                    ))}
                  </div>
                  <div className="mt-4 pt-4 border-t border-gray-100">
                    <Button variant="outline" className="w-full border-purple-200 text-purple-600 hover:bg-purple-50">
                      <Plus className="mr-2 h-4 w-4" /> Create New Group
                    </Button>
                  </div>
                </div>

                {/* Trending Topics */}
                <div className="bg-white rounded-lg shadow-sm p-6">
                  <div className="flex items-center gap-2 mb-4">
                    <TrendingUp className="h-5 w-5 text-purple-600" />
                    <h3 className="font-bold text-lg">Trending Topics</h3>
                  </div>
                  <div className="space-y-3">
                    {[
                      { tag: "SeychellesFestival", posts: 124 },
                      { tag: "IslandLife", posts: 98 },
                      { tag: "LocalArtisans", posts: 76 },
                      { tag: "BeachCleanup", posts: 65 },
                      { tag: "CreoleCuisine", posts: 54 },
                    ].map((topic, index) => (
                      <Link
                        href={`/community/tag/${topic.tag}`}
                        key={index}
                        className="flex items-center justify-between hover:bg-purple-50 p-2 rounded-md transition-colors"
                      >
                        <div className="flex items-center gap-2">
                          <Hash className="h-4 w-4 text-purple-600" />
                          <span className="text-sm font-medium">{topic.tag}</span>
                        </div>
                        <span className="text-xs text-gray-500">{topic.posts} posts</span>
                      </Link>
                    ))}
                  </div>
                </div>
              </div>

              {/* Main Content */}
              <div className="lg:col-span-2 space-y-8">
                {/* Create Post */}
                <div className="bg-white rounded-lg shadow-sm p-6">
                  <div className="flex items-center gap-3 mb-4">
                    <div className="h-10 w-10 rounded-full bg-purple-100 overflow-hidden">
                      <Image
                        src="/placeholder.svg?height=40&width=40"
                        width={40}
                        height={40}
                        alt="Profile"
                        className="object-cover"
                      />
                    </div>
                    <Input
                      placeholder="Share something with the community..."
                      className="border-gray-200 focus:border-purple-500"
                    />
                  </div>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-4">
                      <Button
                        variant="ghost"
                        size="sm"
                        className="text-gray-600 hover:text-purple-600 flex items-center gap-1"
                      >
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          width="16"
                          height="16"
                          viewBox="0 0 24 24"
                          fill="none"
                          stroke="currentColor"
                          strokeWidth="2"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          className="h-4 w-4"
                        >
                          <rect width="18" height="18" x="3" y="3" rx="2" />
                          <circle cx="9" cy="9" r="2" />
                          <path d="m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21" />
                        </svg>
                        <span>Photo</span>
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        className="text-gray-600 hover:text-purple-600 flex items-center gap-1"
                      >
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          width="16"
                          height="16"
                          viewBox="0 0 24 24"
                          fill="none"
                          stroke="currentColor"
                          strokeWidth="2"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          className="h-4 w-4"
                        >
                          <path d="m15 10-4 4l6 6l4-16l-18 7l4 2l2 6l3-4" />
                        </svg>
                        <span>Poll</span>
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        className="text-gray-600 hover:text-purple-600 flex items-center gap-1"
                      >
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          width="16"
                          height="16"
                          viewBox="0 0 24 24"
                          fill="none"
                          stroke="currentColor"
                          strokeWidth="2"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          className="h-4 w-4"
                        >
                          <path d="M12 22c5.523 0 10-4.477 10-10S17.523 2 12 2 2 6.477 2 12s4.477 10 10 10z" />
                          <path d="m9 12 2 2 4-4" />
                        </svg>
                        <span>Activity</span>
                      </Button>
                    </div>
                    <Button className="bg-purple-600 hover:bg-purple-700">Post</Button>
                  </div>
                </div>

                {/* Content Filter */}
                <div className="bg-white rounded-lg shadow-sm p-4">
                  <Tabs defaultValue="foryou" className="w-full">
                    <TabsList className="grid grid-cols-4 w-full">
                      <TabsTrigger value="foryou">For You</TabsTrigger>
                      <TabsTrigger value="following">Following</TabsTrigger>
                      <TabsTrigger value="popular">Popular</TabsTrigger>
                      <TabsTrigger value="latest">Latest</TabsTrigger>
                    </TabsList>
                  </Tabs>
                </div>

                {/* Highlighted Content */}
                <Card className="border-purple-200 bg-purple-50">
                  <CardHeader className="pb-2">
                    <div className="flex items-center gap-2">
                      <Sparkles className="h-5 w-5 text-purple-600" />
                      <CardTitle className="text-lg">Highlighted for You</CardTitle>
                    </div>
                    <CardDescription>Based on your interests and activity</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="grid gap-4 md:grid-cols-2">
                      <Link href="/community/forums/local-culture-traditions/post/123" className="group">
                        <div className="rounded-lg border bg-white p-3 hover:border-purple-200 transition-colors">
                          <h4 className="font-medium group-hover:text-purple-600">
                            Traditional Seychellois Recipes Collection
                          </h4>
                          <p className="text-sm text-gray-500 mt-1 line-clamp-2">
                            A community-sourced collection of authentic Creole recipes from across the islands.
                          </p>
                          <div className="flex items-center gap-2 mt-2">
                            <Badge className="bg-purple-100 text-purple-600 hover:bg-purple-200">Popular</Badge>
                            <span className="text-xs text-gray-500">32 comments</span>
                          </div>
                        </div>
                      </Link>
                      <Link href="/community/groups/seychelles-photography" className="group">
                        <div className="rounded-lg border bg-white p-3 hover:border-purple-200 transition-colors">
                          <h4 className="font-medium group-hover:text-purple-600">Seychelles Photography Challenge</h4>
                          <p className="text-sm text-gray-500 mt-1 line-clamp-2">
                            Join our monthly photo challenge! This month's theme: "Hidden Beaches"
                          </p>
                          <div className="flex items-center gap-2 mt-2">
                            <Badge className="bg-blue-100 text-blue-600 hover:bg-blue-200">New</Badge>
                            <span className="text-xs text-gray-500">Ends in 5 days</span>
                          </div>
                        </div>
                      </Link>
                    </div>
                  </CardContent>
                </Card>

                {/* Posts */}
                <div className="space-y-6">
                  {/* Post 1 */}
                  <PostCard
                    user={{
                      name: "Michael Thompson",
                      username: "@michaelthompson",
                      avatar: "/placeholder.svg?height=40&width=40",
                    }}
                    content="Just finished a beautiful hike at Morne Seychellois National Park! The views were absolutely breathtaking. Has anyone else explored the trails there recently? I'd love some recommendations for my next adventure! #IslandLife #Hiking #SeychellesNature"
                    image="/placeholder.svg?height=400&width=600"
                    likes={124}
                    comments={32}
                    shares={8}
                    timeAgo="2 hours ago"
                    tags={["IslandLife", "Hiking", "SeychellesNature"]}
                  />

                  {/* Post 2 */}
                  <PostCard
                    user={{
                      name: "Elena Dubois",
                      username: "@elenadubois",
                      avatar: "/placeholder.svg?height=40&width=40",
                    }}
                    content="I'm organizing a beach cleanup this Saturday at Beau Vallon Beach. We need to protect our beautiful shores! Who's joining me? Please comment below if you're interested, and I'll share more details. Let's make a difference together! #BeachCleanup #ProtectOurOceans"
                    likes={87}
                    comments={45}
                    shares={23}
                    timeAgo="5 hours ago"
                    tags={["BeachCleanup", "ProtectOurOceans"]}
                  />

                  {/* Post 3 */}
                  <PostCard
                    user={{
                      name: "Local Cuisine Lovers",
                      username: "@localcuisine",
                      avatar: "/placeholder.svg?height=40&width=40",
                      isGroup: true,
                    }}
                    content="We're excited to announce our monthly Creole cooking workshop! This month, we'll be learning how to make authentic Seychellois fish curry. Limited spots available, so register early through the link in our group. #CreoleCuisine #CookingWorkshop"
                    image="/placeholder.svg?height=400&width=600"
                    likes={156}
                    comments={28}
                    shares={14}
                    timeAgo="1 day ago"
                    tags={["CreoleCuisine", "CookingWorkshop"]}
                  />

                  {/* Post 4 */}
                  <PostCard
                    user={{
                      name: "David Miller",
                      username: "@davidmiller",
                      avatar: "/placeholder.svg?height=40&width=40",
                    }}
                    content="Question for the community: What's your favorite local festival in Seychelles? I'm planning my visit for next year and would love to experience authentic local culture. #SeychellesFestival #TravelPlanning"
                    likes={42}
                    comments={67}
                    shares={3}
                    timeAgo="2 days ago"
                    tags={["SeychellesFestival", "TravelPlanning"]}
                  />
                </div>

                <div className="flex justify-center">
                  <Button variant="outline" className="border-purple-200 text-purple-600 hover:bg-purple-50">
                    Load More
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Forums Section */}
        <section className="py-12 bg-white">
          <div className="container px-4 md:px-6">
            <div className="flex items-center justify-between mb-8">
              <h2 className="text-2xl font-bold">Popular Discussion Forums</h2>
              <Link
                href="/community/forums"
                className="text-purple-600 hover:text-purple-700 text-sm font-medium flex items-center"
              >
                View All Forums <ArrowRight className="ml-1 h-4 w-4" />
              </Link>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {[
                {
                  title: "Local Culture & Traditions",
                  description: "Discuss Seychellois culture, traditions, language, and heritage.",
                  topics: 156,
                  posts: 1243,
                  icon: <Globe className="h-6 w-6 text-purple-600" />,
                },
                {
                  title: "Island Living",
                  description: "Share tips and experiences about daily life in Seychelles.",
                  topics: 124,
                  posts: 987,
                  icon: <Home className="h-6 w-6 text-purple-600" />,
                },
                {
                  title: "Environmental Conservation",
                  description: "Discuss initiatives to protect Seychelles' unique environment.",
                  topics: 98,
                  posts: 876,
                  icon: <Leaf className="h-6 w-6 text-purple-600" />,
                },
              ].map((forum, index) => (
                <ForumCard
                  key={index}
                  title={forum.title}
                  description={forum.description}
                  topics={forum.topics}
                  posts={forum.posts}
                  icon={forum.icon}
                />
              ))}
            </div>
          </div>
        </section>

        {/* Groups Section */}
        <section className="py-12 bg-gray-50">
          <div className="container px-4 md:px-6">
            <div className="flex items-center justify-between mb-8">
              <h2 className="text-2xl font-bold">Discover Groups</h2>
              <div className="flex items-center gap-4">
                <Button
                  variant="outline"
                  size="sm"
                  className="gap-2 border-purple-200 text-purple-600 hover:bg-purple-50"
                >
                  <Filter className="h-4 w-4" /> Filter
                </Button>
                <Link
                  href="/community/groups"
                  className="text-purple-600 hover:text-purple-700 text-sm font-medium flex items-center"
                >
                  View All Groups <ArrowRight className="ml-1 h-4 w-4" />
                </Link>
              </div>
            </div>
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
              {[
                {
                  name: "Seychelles Photography",
                  members: 1243,
                  posts: 4567,
                  image: "/placeholder.svg?height=200&width=400",
                  isPrivate: false,
                },
                {
                  name: "Local Cuisine Lovers",
                  members: 876,
                  posts: 3456,
                  image: "/placeholder.svg?height=200&width=400",
                  isPrivate: false,
                },
                {
                  name: "Island Adventures",
                  members: 654,
                  posts: 2345,
                  image: "/placeholder.svg?height=200&width=400",
                  isPrivate: true,
                },
                {
                  name: "Creole Language",
                  members: 432,
                  posts: 1234,
                  image: "/placeholder.svg?height=200&width=400",
                  isPrivate: false,
                },
              ].map((group, index) => (
                <GroupCard
                  key={index}
                  name={group.name}
                  members={group.members}
                  posts={group.posts}
                  image={group.image}
                  isPrivate={group.isPrivate}
                />
              ))}
            </div>
            <div className="flex justify-center mt-8">
              <Button className="bg-purple-600 hover:bg-purple-700 gap-2">
                <Plus className="h-4 w-4" /> Create New Group
              </Button>
            </div>
          </div>
        </section>

        {/* Community Guidelines */}
        <section className="py-12 bg-purple-50">
          <div className="container px-4 md:px-6">
            <div className="bg-white rounded-lg shadow-sm p-6 md:p-8">
              <div className="flex items-center gap-4 mb-6">
                <div className="h-12 w-12 rounded-full bg-purple-100 flex items-center justify-center">
                  <Shield className="h-6 w-6 text-purple-600" />
                </div>
                <h2 className="text-2xl font-bold">Community Guidelines</h2>
              </div>
              <div className="grid md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <p className="text-gray-600">
                    Our community thrives on respect, inclusivity, and positive interactions. Please take a moment to
                    familiarize yourself with our guidelines to ensure a welcoming environment for everyone.
                  </p>
                  <ul className="space-y-2">
                    <li className="flex items-start gap-2">
                      <div className="h-5 w-5 rounded-full bg-purple-100 flex items-center justify-center mt-0.5">
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          width="24"
                          height="24"
                          viewBox="0 0 24 24"
                          fill="none"
                          stroke="currentColor"
                          strokeWidth="2"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          className="h-3 w-3 text-purple-600"
                        >
                          <polyline points="20 6 9 17 4 12" />
                        </svg>
                      </div>
                      <span className="text-sm">Be respectful and considerate in all interactions</span>
                    </li>
                    <li className="flex items-start gap-2">
                      <div className="h-5 w-5 rounded-full bg-purple-100 flex items-center justify-center mt-0.5">
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          width="24"
                          height="24"
                          viewBox="0 0 24 24"
                          fill="none"
                          stroke="currentColor"
                          strokeWidth="2"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          className="h-3 w-3 text-purple-600"
                        >
                          <polyline points="20 6 9 17 4 12" />
                        </svg>
                      </div>
                      <span className="text-sm">Share authentic and relevant content</span>
                    </li>
                    <li className="flex items-start gap-2">
                      <div className="h-5 w-5 rounded-full bg-purple-100 flex items-center justify-center mt-0.5">
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          width="24"
                          height="24"
                          viewBox="0 0 24 24"
                          fill="none"
                          stroke="currentColor"
                          strokeWidth="2"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          className="h-3 w-3 text-purple-600"
                        >
                          <polyline points="20 6 9 17 4 12" />
                        </svg>
                      </div>
                      <span className="text-sm">Protect your privacy and respect others' privacy</span>
                    </li>
                  </ul>
                </div>
                <div className="space-y-4">
                  <ul className="space-y-2">
                    <li className="flex items-start gap-2">
                      <div className="h-5 w-5 rounded-full bg-purple-100 flex items-center justify-center mt-0.5">
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          width="24"
                          height="24"
                          viewBox="0 0 24 24"
                          fill="none"
                          stroke="currentColor"
                          strokeWidth="2"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          className="h-3 w-3 text-purple-600"
                        >
                          <polyline points="20 6 9 17 4 12" />
                        </svg>
                      </div>
                      <span className="text-sm">No hate speech, harassment, or bullying</span>
                    </li>
                    <li className="flex items-start gap-2">
                      <div className="h-5 w-5 rounded-full bg-purple-100 flex items-center justify-center mt-0.5">
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          width="24"
                          height="24"
                          viewBox="0 0 24 24"
                          fill="none"
                          stroke="currentColor"
                          strokeWidth="2"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          className="h-3 w-3 text-purple-600"
                        >
                          <polyline points="20 6 9 17 4 12" />
                        </svg>
                      </div>
                      <span className="text-sm">No spam, scams, or misleading content</span>
                    </li>
                    <li className="flex items-start gap-2">
                      <div className="h-5 w-5 rounded-full bg-purple-100 flex items-center justify-center mt-0.5">
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          width="24"
                          height="24"
                          viewBox="0 0 24 24"
                          fill="none"
                          stroke="currentColor"
                          strokeWidth="2"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          className="h-3 w-3 text-purple-600"
                        >
                          <polyline points="20 6 9 17 4 12" />
                        </svg>
                      </div>
                      <span className="text-sm">Report any violations to community moderators</span>
                    </li>
                  </ul>
                  <div className="flex justify-end mt-4">
                    <Button className="bg-purple-600 hover:bg-purple-700">Read Full Guidelines</Button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Join Community CTA */}
        <section className="py-12 bg-gradient-to-r from-purple-600 to-indigo-500 text-white">
          <div className="container px-4 md:px-6 text-center">
            <h2 className="text-3xl font-bold mb-4">Join Our Growing Community Today</h2>
            <p className="text-lg text-purple-100 max-w-2xl mx-auto mb-8">
              Connect with fellow Seychellois, share experiences, join discussions, and be part of something special.
            </p>
            <Button className="bg-white text-purple-600 hover:bg-purple-50 text-lg px-8 py-6">Sign Up Now</Button>
          </div>
        </section>
      </main>

      {/* Footer */}
      <footer className="bg-white border-t py-12">
        <div className="container px-4 md:px-6">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            <div className="space-y-4">
              <Link href="/" className="flex items-center space-x-2">
                <span className="text-2xl font-bold bg-gradient-to-r from-blue-600 to-teal-500 bg-clip-text text-transparent">
                  Kominote
                </span>
              </Link>
              <p className="text-sm text-gray-600">
                Seychelles' premier community platform connecting people with shared interests.
              </p>
            </div>
            <div className="space-y-4">
              <h3 className="font-medium">Community</h3>
              <ul className="space-y-2">
                <li>
                  <Link href="#" className="text-sm text-gray-600 hover:text-purple-600">
                    Forums
                  </Link>
                </li>
                <li>
                  <Link href="#" className="text-sm text-gray-600 hover:text-purple-600">
                    Groups
                  </Link>
                </li>
                <li>
                  <Link href="#" className="text-sm text-gray-600 hover:text-purple-600">
                    Members
                  </Link>
                </li>
                <li>
                  <Link href="#" className="text-sm text-gray-600 hover:text-purple-600">
                    Activity
                  </Link>
                </li>
              </ul>
            </div>
            <div className="space-y-4">
              <h3 className="font-medium">Resources</h3>
              <ul className="space-y-2">
                <li>
                  <Link href="#" className="text-sm text-gray-600 hover:text-purple-600">
                    Guidelines
                  </Link>
                </li>
                <li>
                  <Link href="#" className="text-sm text-gray-600 hover:text-purple-600">
                    Help Center
                  </Link>
                </li>
                <li>
                  <Link href="#" className="text-sm text-gray-600 hover:text-purple-600">
                    Safety Tips
                  </Link>
                </li>
                <li>
                  <Link href="#" className="text-sm text-gray-600 hover:text-purple-600">
                    Moderation
                  </Link>
                </li>
              </ul>
            </div>
            <div className="space-y-4">
              <h3 className="font-medium">Legal</h3>
              <ul className="space-y-2">
                <li>
                  <Link href="#" className="text-sm text-gray-600 hover:text-purple-600">
                    Terms of Service
                  </Link>
                </li>
                <li>
                  <Link href="#" className="text-sm text-gray-600 hover:text-purple-600">
                    Privacy Policy
                  </Link>
                </li>
                <li>
                  <Link href="#" className="text-sm text-gray-600 hover:text-purple-600">
                    Cookie Policy
                  </Link>
                </li>
                <li>
                  <Link href="#" className="text-sm text-gray-600 hover:text-purple-600">
                    Community Standards
                  </Link>
                </li>
              </ul>
            </div>
          </div>
          <div className="border-t mt-12 pt-8 flex flex-col md:flex-row justify-between items-center">
            <p className="text-sm text-gray-600">
              &copy; {new Date().getFullYear()} Kominote Community. All rights reserved.
            </p>
            <div className="flex items-center gap-4 mt-4 md:mt-0">
              <Link href="#" className="text-gray-600 hover:text-purple-600">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="24"
                  height="24"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  className="h-5 w-5"
                >
                  <path d="M18 2h-3a5 5 0 0 0-5 5v3H7v4h3v8h4v-8h3l1-4h-4V7a1 1 0 0 1 1-1h3z" />
                </svg>
              </Link>
              <Link href="#" className="text-gray-600 hover:text-purple-600">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="24"
                  height="24"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  className="h-5 w-5"
                >
                  <rect width="20" height="20" x="2" y="2" rx="5" ry="5" />
                  <path d="M16 11.37A4 4 0 1 1 12.63 8 4 4 0 0 1 16 11.37z" />
                  <line x1="17.5" x2="17.51" y1="6.5" y2="6.5" />
                </svg>
              </Link>
              <Link href="#" className="text-gray-600 hover:text-purple-600">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="24"
                  height="24"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  className="h-5 w-5"
                >
                  <path d="M22 4s-.7 2.1-2 3.4c1.6 10-9.4 17.3-18 11.6 2.2.1 4.4-.6 6-2C3 15.5.5 9.6 3 5c2.2 2.6 5.6 4.1 9 4-.9-4.2 4-6.6 7-3.8 1.1 0 3-1.2 3-1.2z" />
                </svg>
              </Link>
            </div>
          </div>
        </div>
      </footer>

      {/* Side Navigation */}
      <FeatureNavigation currentFeature="community" colorScheme="purple" />

      {/* Messenger - with light variant for visibility on light backgrounds */}
      <Messenger variant="light" />
    </div>
  )
}

// Post Card Component
function PostCard({ user, content, image, likes, comments, shares, timeAgo, tags }) {
  return (
    <div className="bg-white rounded-lg shadow-sm overflow-hidden">
      <div className="p-4">
        <div className="flex items-center gap-3 mb-3">
          <div className="h-10 w-10 rounded-full bg-purple-100 overflow-hidden">
            <Image
              src={user.avatar || "/placeholder.svg"}
              width={40}
              height={40}
              alt={user.name}
              className="object-cover"
            />
          </div>
          <div>
            <div className="flex items-center gap-1">
              <h4 className="font-medium text-sm">{user.name}</h4>
              {user.isGroup && (
                <Badge className="h-5 bg-purple-100 text-purple-600 hover:bg-purple-200 px-1">Group</Badge>
              )}
            </div>
            <div className="flex items-center gap-2">
              <p className="text-xs text-gray-500">{user.username}</p>
              <span className="text-xs text-gray-400">•</span>
              <p className="text-xs text-gray-500">{timeAgo}</p>
            </div>
          </div>
        </div>
        <p className="text-sm text-gray-800 mb-3">{content}</p>
        {tags && tags.length > 0 && (
          <div className="flex flex-wrap gap-1 mb-3">
            {tags.map((tag, index) => (
              <Link href={`/community/tag/${tag}`} key={index}>
                <Badge className="bg-purple-50 text-purple-600 hover:bg-purple-100 cursor-pointer">#{tag}</Badge>
              </Link>
            ))}
          </div>
        )}
        {image && (
          <div className="relative h-[300px] rounded-lg overflow-hidden mb-3">
            <Image src={image || "/placeholder.svg"} fill alt="Post image" className="object-cover" />
          </div>
        )}
        <div className="flex items-center justify-between pt-3 border-t border-gray-100">
          <Button variant="ghost" size="sm" className="text-gray-600 hover:text-purple-600 flex items-center gap-1">
            <Heart className="h-4 w-4" />
            <span>{likes}</span>
          </Button>
          <Button variant="ghost" size="sm" className="text-gray-600 hover:text-purple-600 flex items-center gap-1">
            <MessageCircle className="h-4 w-4" />
            <span>{comments}</span>
          </Button>
          <Button variant="ghost" size="sm" className="text-gray-600 hover:text-purple-600 flex items-center gap-1">
            <Share2 className="h-4 w-4" />
            <span>{shares}</span>
          </Button>
          <Button variant="ghost" size="sm" className="text-gray-600 hover:text-purple-600">
            <Bookmark className="h-4 w-4" />
          </Button>
        </div>
      </div>
    </div>
  )
}

// Forum Card Component
function ForumCard({ title, description, topics, posts, icon }) {
  return (
    <Link href={`/community/forums/${title.toLowerCase().replace(/\s+/g, "-")}`} className="group">
      <div className="bg-white rounded-lg shadow-sm p-6 transition-all duration-200 hover:shadow-md border border-transparent hover:border-purple-200">
        <div className="flex items-center gap-3 mb-3">
          <div className="h-12 w-12 rounded-full bg-purple-100 flex items-center justify-center group-hover:bg-purple-200 transition-colors">
            {icon}
          </div>
          <h3 className="font-bold text-lg group-hover:text-purple-600 transition-colors">{title}</h3>
        </div>
        <p className="text-sm text-gray-600 mb-4 line-clamp-2">{description}</p>
        <div className="flex items-center justify-between text-sm text-gray-500">
          <span>{topics} topics</span>
          <span>{posts} posts</span>
        </div>
      </div>
    </Link>
  )
}

// Group Card Component
function GroupCard({ name, members, posts, image, isPrivate }) {
  return (
    <Link href={`/community/groups/${name.toLowerCase().replace(/\s+/g, "-")}`} className="group">
      <div className="bg-white rounded-lg shadow-sm overflow-hidden transition-all duration-200 hover:shadow-md">
        <div className="relative h-32">
          <Image src={image || "/placeholder.svg"} fill alt={name} className="object-cover" />
          <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent" />
          <div className="absolute bottom-3 left-3 right-3">
            <h3 className="font-bold text-white">{name}</h3>
            <div className="flex items-center gap-2">
              <span className="text-xs text-white">{members} members</span>
              {isPrivate && <Badge className="h-5 bg-white/80 text-purple-600 px-1">Private</Badge>}
            </div>
          </div>
        </div>
        <div className="p-3">
          <div className="flex items-center justify-between">
            <span className="text-xs text-gray-500">{posts} posts</span>
            <Button
              variant="outline"
              size="sm"
              className="h-8 text-xs border-purple-200 text-purple-600 hover:bg-purple-50"
            >
              Join Group
            </Button>
          </div>
        </div>
      </div>
    </Link>
  )
}

// Custom Icon Components
function Camera(props) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
      {...props}
    >
      <path d="M14.5 4h-5L7 7H4a2 2 0 0 0-2 2v9a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2V9a2 2 0 0 0-2-2h-3l-2.5-3z" />
      <circle cx="12" cy="13" r="3" />
    </svg>
  )
}

function Utensils(props) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
      {...props}
    >
      <path d="M3 2v7c0 1.1.9 2 2 2h4a2 2 0 0 0 2-2V2" />
      <path d="M7 2v20" />
      <path d="M21 15V2v0a5 5 0 0 0-5 5v6c0 1.1.9 2 2 2h3Zm0 0v7" />
    </svg>
  )
}

function Map(props) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
      {...props}
    >
      <polygon points="3 6 9 3 15 6 21 3 21 18 15 21 9 18 3 21" />
      <line x1="9" x2="9" y1="3" y2="18" />
      <line x1="15" x2="15" y1="6" y2="21" />
    </svg>
  )
}

function Home(props) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
      {...props}
    >
      <path d="m3 9 9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z" />
      <polyline points="9 22 9 12 15 12 15 22" />
    </svg>
  )
}

function Leaf(props) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
      {...props}
    >
      <path d="M11 20A7 7 0 0 1 9.8 6.1C15.5 5 17 4.48 19 2c1 2 2 4.18 2 8 0 5.5-4.78 10-10 10Z" />
      <path d="M2 21c0-3 1.85-5.36 5.08-6C9.5 14.52 12 13 13 12" />
    </svg>
  )
}

