"use client"

import { useState } from "react"
import { Ava<PERSON>, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Button } from "@/components/ui/button"
import { Sheet, <PERSON>et<PERSON>ontent, <PERSON><PERSON><PERSON>eader, <PERSON><PERSON><PERSON><PERSON><PERSON>, SheetTrigger } from "@/components/ui/sheet"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON>bsList, TabsTrigger } from "@/components/ui/tabs"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Badge } from "@/components/ui/badge"
import { MessageSquare, Search, Send, Bot, AlertCircle, ChevronRight, Clock, CheckCircle2 } from "lucide-react"
import { useAuth } from "@/components/auth-provider"
import { cn } from "@/lib/utils"

interface MessengerProps {
  variant?: "light" | "dark"
}

export function Messenger({ variant = "light" }: MessengerProps) {
  const { user, isAuthenticated } = useAuth()
  const [isOpen, setIsOpen] = useState(false)
  const [activeChat, setActiveChat] = useState(null)
  const [activeTab, setActiveTab] = useState("friends")
  const [message, setMessage] = useState("")
  const [aiMessage, setAiMessage] = useState("")
  const [reportIssue, setReportIssue] = useState({
    title: "",
    description: "",
    category: "technical",
  })
  const [reportSubmitted, setReportSubmitted] = useState(false)
  const [aiConversation, setAiConversation] = useState([
    { sender: "ai", message: "Hello! I'm your Kominote assistant. How can I help you today?" },
  ])

  // Mock friends data
  const friends = [
    { id: 1, name: "Marie Dubois", status: "online", avatar: "/placeholder.svg?height=40&width=40", unread: 2 },
    { id: 2, name: "Jean Baptiste", status: "online", avatar: "/placeholder.svg?height=40&width=40" },
    { id: 3, name: "Sophia Laurent", status: "offline", avatar: "/placeholder.svg?height=40&width=40" },
    { id: 4, name: "Michel Renoir", status: "away", avatar: "/placeholder.svg?height=40&width=40" },
    { id: 5, name: "Claire Monet", status: "online", avatar: "/placeholder.svg?height=40&width=40", unread: 1 },
    { id: 6, name: "Antoine Leclerc", status: "online", avatar: "/placeholder.svg?height=40&width=40" },
    { id: 7, name: "Elise Moreau", status: "offline", avatar: "/placeholder.svg?height=40&width=40" },
    { id: 8, name: "Thomas Petit", status: "away", avatar: "/placeholder.svg?height=40&width=40", unread: 3 },
  ]

  // Mock chat history
  const chatHistory = {
    1: [
      { sender: "them", message: "Hey, have you seen the new Seychelles Untold episode?", time: "Yesterday" },
      { sender: "me", message: "Not yet! Is it good?", time: "Yesterday" },
      { sender: "them", message: "It's amazing! You should watch it tonight.", time: "Yesterday" },
      { sender: "them", message: "Let me know what you think when you do!", time: "10:30 AM" },
    ],
    2: [
      { sender: "them", message: "Are you going to the cultural festival this weekend?", time: "2 days ago" },
      { sender: "me", message: "Yes, I'm planning to go on Saturday!", time: "2 days ago" },
      { sender: "them", message: "Great! Let's meet there.", time: "2 days ago" },
    ],
    5: [
      { sender: "them", message: "Did you see the new craft items in the shop?", time: "3 days ago" },
      { sender: "me", message: "Yes, they look beautiful! Are you thinking of buying something?", time: "3 days ago" },
      {
        sender: "them",
        message: "I already ordered the handwoven basket. It's perfect for my living room.",
        time: "3 days ago",
      },
    ],
    8: [
      { sender: "them", message: "Hey, are you interested in the new job posting?", time: "1 week ago" },
      { sender: "me", message: "Which one?", time: "1 week ago" },
      { sender: "them", message: "The marketing position at Seychelles Tourism Board", time: "1 week ago" },
      { sender: "them", message: "I think you'd be perfect for it!", time: "1 week ago" },
      { sender: "them", message: "Let me know if you apply, I can put in a good word", time: "1 week ago" },
    ],
  }

  // Mock tickets
  const tickets = [
    { id: "T-1234", title: "Video playback issue", status: "open", created: "2 days ago" },
    { id: "T-1189", title: "Payment failed", status: "closed", created: "1 week ago" },
    { id: "T-1356", title: "Cannot access community forums", status: "in-progress", created: "1 day ago" },
    { id: "T-1098", title: "Event registration error", status: "open", created: "3 days ago" },
  ]

  const handleSendMessage = () => {
    if (!message.trim()) return

    // Add the message to chat history
    const updatedChatHistory = { ...chatHistory }
    if (!updatedChatHistory[activeChat]) {
      updatedChatHistory[activeChat] = []
    }

    updatedChatHistory[activeChat].push({
      sender: "me",
      message: message,
      time: "Just now",
    })

    // Simulate a response after a short delay
    setTimeout(() => {
      updatedChatHistory[activeChat].push({
        sender: "them",
        message: "Thanks for your message! I'll get back to you soon.",
        time: "Just now",
      })
    }, 1000)

    // Clear the input
    setMessage("")
  }

  const handleSendAiMessage = () => {
    if (!aiMessage.trim()) return

    // Add user message to conversation
    setAiConversation([...aiConversation, { sender: "user", message: aiMessage }])

    // Simulate AI response
    setTimeout(() => {
      setAiConversation((prev) => [
        ...prev,
        {
          sender: "ai",
          message: `I understand your question about "${aiMessage}". Our team is working to provide the best experience. Is there anything specific you'd like to know?`,
        },
      ])
    }, 1000)

    // Clear the input
    setAiMessage("")
  }

  const handleSubmitReport = (e) => {
    e.preventDefault()

    // In a real app, you would send this report to your backend
    console.log("Submitting report:", reportIssue)

    // Show success message
    setReportSubmitted(true)

    // Reset form after a delay
    setTimeout(() => {
      setReportIssue({
        title: "",
        description: "",
        category: "technical",
      })
      setReportSubmitted(false)
      setActiveTab("friends")
    }, 3000)
  }

  const handleOpenChat = (id) => {
    setActiveChat(id)
  }

  const handleCloseChat = () => {
    setActiveChat(null)
  }

  const getStatusColor = (status) => {
    switch (status) {
      case "online":
        return "bg-green-500"
      case "away":
        return "bg-amber-500"
      case "offline":
        return "bg-gray-400"
      default:
        return "bg-gray-400"
    }
  }

  const getTicketStatusColor = (status) => {
    switch (status) {
      case "open":
        return "bg-blue-500"
      case "in-progress":
        return "bg-amber-500"
      case "closed":
        return "bg-gray-400"
      default:
        return "bg-gray-400"
    }
  }

  // Calculate total unread messages
  const totalUnread = friends.reduce((total, friend) => total + (friend.unread || 0), 0)

  return (
    <div className="relative">
      <Sheet open={isOpen} onOpenChange={setIsOpen}>
        <SheetTrigger asChild>
          <Button
            variant={variant === "dark" ? "secondary" : "default"}
            size="icon"
            className={cn(
              "fixed bottom-8 right-8 h-12 w-12 rounded-full shadow-lg z-40",
              variant === "dark"
                ? "bg-white text-gray-900 hover:bg-gray-100"
                : "bg-primary text-primary-foreground hover:bg-primary/90",
            )}
          >
            <MessageSquare className="h-5 w-5" />
            <span className="sr-only">Open messenger</span>
            {totalUnread > 0 && (
              <span className="absolute -top-1 -right-1 flex h-5 w-5 items-center justify-center rounded-full bg-red-500 text-[10px] font-medium text-white">
                {totalUnread}
              </span>
            )}
          </Button>
        </SheetTrigger>
        <SheetContent className="w-full sm:max-w-md p-0 flex flex-col h-full">
          {!activeChat ? (
            <Tabs
              defaultValue="friends"
              value={activeTab}
              onValueChange={setActiveTab}
              className="flex flex-col h-full"
            >
              <SheetHeader className="px-4 py-3 border-b">
                <SheetTitle>Messages</SheetTitle>
              </SheetHeader>
              <TabsList className="grid grid-cols-3 px-4 py-2">
                <TabsTrigger value="friends">Friends</TabsTrigger>
                <TabsTrigger value="help">Help</TabsTrigger>
                <TabsTrigger value="tickets">Tickets</TabsTrigger>
              </TabsList>

              <TabsContent value="friends" className="flex-1 overflow-auto">
                <div className="relative px-4 py-2">
                  <Search className="absolute left-6 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                  <Input placeholder="Search friends..." className="pl-8" />
                </div>
                <div className="px-2">
                  <div className="text-xs font-medium text-muted-foreground px-2 py-2">HELP</div>
                  <button
                    onClick={() => setActiveTab("help")}
                    className="w-full flex items-center gap-3 px-2 py-3 rounded-md hover:bg-accent transition-colors"
                  >
                    <div className="relative flex h-10 w-10 shrink-0 items-center justify-center rounded-full bg-primary/10">
                      <Bot className="h-5 w-5 text-primary" />
                    </div>
                    <div className="flex-1 overflow-hidden">
                      <div className="flex items-center justify-between">
                        <p className="text-sm font-medium">Kominote Assistant</p>
                        <ChevronRight className="h-4 w-4 text-muted-foreground" />
                      </div>
                      <p className="text-xs text-muted-foreground truncate">Get help with your questions</p>
                    </div>
                  </button>

                  <div className="text-xs font-medium text-muted-foreground px-2 py-2 mt-2">FRIENDS</div>
                  {friends.map((friend) => (
                    <button
                      key={friend.id}
                      onClick={() => handleOpenChat(friend.id)}
                      className="w-full flex items-center gap-3 px-2 py-3 rounded-md hover:bg-accent transition-colors"
                    >
                      <div className="relative">
                        <Avatar className="h-10 w-10">
                          <AvatarImage src={friend.avatar} alt={friend.name} />
                          <AvatarFallback>{friend.name.charAt(0)}</AvatarFallback>
                        </Avatar>
                        <span
                          className={`absolute bottom-0 right-0 h-3 w-3 rounded-full border-2 border-background ${getStatusColor(friend.status)}`}
                        />
                      </div>
                      <div className="flex-1 overflow-hidden">
                        <div className="flex items-center justify-between">
                          <p className="text-sm font-medium">{friend.name}</p>
                          {friend.unread && (
                            <Badge variant="destructive" className="rounded-full px-1.5 py-0.5 text-[10px]">
                              {friend.unread}
                            </Badge>
                          )}
                        </div>
                        <p className="text-xs text-muted-foreground truncate">
                          {friend.status === "online" ? "Online" : friend.status === "away" ? "Away" : "Offline"}
                        </p>
                      </div>
                    </button>
                  ))}
                </div>
              </TabsContent>

              <TabsContent value="help" className="flex flex-col h-full">
                <div className="flex-1 overflow-auto p-4">
                  <div className="flex flex-col gap-4">
                    {aiConversation.map((msg, index) => (
                      <div key={index} className={`flex ${msg.sender === "ai" ? "justify-start" : "justify-end"}`}>
                        {msg.sender === "ai" && (
                          <Avatar className="h-8 w-8 mr-2 mt-1">
                            <AvatarFallback className="bg-primary/20 text-primary">AI</AvatarFallback>
                          </Avatar>
                        )}
                        <div>
                          <div
                            className={`max-w-[80%] rounded-lg px-4 py-2 ${
                              msg.sender === "ai"
                                ? "bg-accent text-accent-foreground"
                                : "bg-primary text-primary-foreground"
                            }`}
                          >
                            {msg.message}
                          </div>
                          {msg.sender === "ai" && (
                            <p className="text-xs text-muted-foreground mt-1 ml-1">Kominote AI</p>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
                <div className="p-4 border-t">
                  <div className="flex gap-2">
                    <Input
                      placeholder="Ask for help..."
                      value={aiMessage}
                      onChange={(e) => setAiMessage(e.target.value)}
                      onKeyDown={(e) => e.key === "Enter" && handleSendAiMessage()}
                    />
                    <Button size="icon" onClick={handleSendAiMessage}>
                      <Send className="h-4 w-4" />
                    </Button>
                  </div>
                  <div className="mt-2 text-center">
                    <button onClick={() => setActiveTab("report")} className="text-xs text-primary hover:underline">
                      Report an issue to human support
                    </button>
                  </div>
                </div>
              </TabsContent>

              <TabsContent value="report" className="flex-1 overflow-auto p-4">
                {reportSubmitted ? (
                  <div className="h-full flex flex-col items-center justify-center text-center gap-4">
                    <div className="h-12 w-12 rounded-full bg-green-100 flex items-center justify-center">
                      <CheckCircle2 className="h-6 w-6 text-green-600" />
                    </div>
                    <h3 className="text-lg font-medium">Report Submitted</h3>
                    <p className="text-muted-foreground">Thank you for your report. Our team will review it shortly.</p>
                  </div>
                ) : (
                  <form onSubmit={handleSubmitReport} className="space-y-4">
                    <div>
                      <h3 className="text-lg font-medium mb-4">Report an Issue</h3>
                      <p className="text-sm text-muted-foreground mb-6">
                        Please provide details about the issue you're experiencing. A human agent will review your
                        report.
                      </p>
                    </div>
                    <div className="space-y-2">
                      <label htmlFor="issue-title" className="text-sm font-medium">
                        Issue Title
                      </label>
                      <Input
                        id="issue-title"
                        placeholder="Brief description of the issue"
                        value={reportIssue.title}
                        onChange={(e) => setReportIssue({ ...reportIssue, title: e.target.value })}
                        required
                      />
                    </div>
                    <div className="space-y-2">
                      <label htmlFor="issue-category" className="text-sm font-medium">
                        Category
                      </label>
                      <select
                        id="issue-category"
                        className="w-full rounded-md border border-input bg-background px-3 py-2 text-sm"
                        value={reportIssue.category}
                        onChange={(e) => setReportIssue({ ...reportIssue, category: e.target.value })}
                      >
                        <option value="technical">Technical Issue</option>
                        <option value="account">Account Problem</option>
                        <option value="content">Content Issue</option>
                        <option value="payment">Payment Problem</option>
                        <option value="other">Other</option>
                      </select>
                    </div>
                    <div className="space-y-2">
                      <label htmlFor="issue-description" className="text-sm font-medium">
                        Description
                      </label>
                      <Textarea
                        id="issue-description"
                        placeholder="Please provide details about the issue..."
                        rows={5}
                        value={reportIssue.description}
                        onChange={(e) => setReportIssue({ ...reportIssue, description: e.target.value })}
                        required
                      />
                    </div>
                    <div className="flex gap-2 pt-4">
                      <Button type="button" variant="outline" onClick={() => setActiveTab("help")} className="flex-1">
                        Cancel
                      </Button>
                      <Button type="submit" className="flex-1">
                        Submit Report
                      </Button>
                    </div>
                  </form>
                )}
              </TabsContent>

              <TabsContent value="tickets" className="flex-1 overflow-auto p-4">
                <div className="space-y-4">
                  <div>
                    <h3 className="text-lg font-medium mb-2">Your Support Tickets</h3>
                    <p className="text-sm text-muted-foreground">Track the status of your reported issues</p>
                  </div>

                  {tickets.length > 0 ? (
                    <div className="space-y-3">
                      {tickets.map((ticket) => (
                        <div key={ticket.id} className="border rounded-lg p-4">
                          <div className="flex justify-between items-start">
                            <div>
                              <div className="flex items-center gap-2">
                                <span className="text-sm font-medium">{ticket.id}</span>
                                <Badge
                                  variant={
                                    ticket.status === "open"
                                      ? "default"
                                      : ticket.status === "in-progress"
                                        ? "secondary"
                                        : "outline"
                                  }
                                >
                                  {ticket.status === "in-progress" ? "In Progress" : ticket.status}
                                </Badge>
                              </div>
                              <h4 className="font-medium mt-1">{ticket.title}</h4>
                            </div>
                            <div className="flex items-center text-xs text-muted-foreground">
                              <Clock className="h-3 w-3 mr-1" />
                              {ticket.created}
                            </div>
                          </div>
                          <div className="mt-3 flex justify-end">
                            <Button variant="ghost" size="sm" className="text-xs">
                              View Details
                            </Button>
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="text-center py-8">
                      <div className="mx-auto h-12 w-12 rounded-full bg-muted flex items-center justify-center mb-3">
                        <CheckCircle2 className="h-6 w-6 text-muted-foreground" />
                      </div>
                      <h4 className="text-base font-medium mb-1">No tickets found</h4>
                      <p className="text-sm text-muted-foreground">You haven't submitted any support tickets yet</p>
                    </div>
                  )}

                  <div className="pt-4">
                    <Button onClick={() => setActiveTab("report")} className="w-full">
                      <AlertCircle className="mr-2 h-4 w-4" />
                      Report New Issue
                    </Button>
                  </div>
                </div>
              </TabsContent>
            </Tabs>
          ) : (
            <div className="flex flex-col h-full">
              <div className="px-4 py-3 border-b flex items-center gap-3">
                <Button variant="ghost" size="icon" onClick={handleCloseChat} className="h-8 w-8">
                  <ChevronRight className="h-4 w-4 rotate-180" />
                </Button>
                <div className="flex items-center gap-3 flex-1">
                  <Avatar className="h-8 w-8">
                    <AvatarImage
                      src={friends.find((f) => f.id === activeChat)?.avatar}
                      alt={friends.find((f) => f.id === activeChat)?.name}
                    />
                    <AvatarFallback>{friends.find((f) => f.id === activeChat)?.name.charAt(0)}</AvatarFallback>
                  </Avatar>
                  <div>
                    <p className="text-sm font-medium">{friends.find((f) => f.id === activeChat)?.name}</p>
                    <p className="text-xs text-muted-foreground">
                      {friends.find((f) => f.id === activeChat)?.status === "online"
                        ? "Online"
                        : friends.find((f) => f.id === activeChat)?.status === "away"
                          ? "Away"
                          : "Offline"}
                    </p>
                  </div>
                </div>
              </div>

              <div className="flex-1 overflow-auto p-4">
                <div className="space-y-4">
                  {chatHistory[activeChat]?.map((msg, index) => (
                    <div key={index} className={`flex ${msg.sender === "me" ? "justify-end" : "justify-start"}`}>
                      {msg.sender === "them" && (
                        <Avatar className="h-8 w-8 mr-2 mt-1">
                          <AvatarImage
                            src={friends.find((f) => f.id === activeChat)?.avatar}
                            alt={friends.find((f) => f.id === activeChat)?.name}
                          />
                          <AvatarFallback>{friends.find((f) => f.id === activeChat)?.name.charAt(0)}</AvatarFallback>
                        </Avatar>
                      )}
                      <div>
                        <div
                          className={`max-w-[240px] rounded-lg px-3 py-2 ${
                            msg.sender === "me"
                              ? "bg-primary text-primary-foreground"
                              : "bg-accent text-accent-foreground"
                          }`}
                        >
                          {msg.message}
                        </div>
                        <p className="text-xs text-muted-foreground mt-1 px-1">{msg.time}</p>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              <div className="p-4 border-t">
                <div className="flex gap-2">
                  <Input
                    placeholder="Type a message..."
                    value={message}
                    onChange={(e) => setMessage(e.target.value)}
                    onKeyDown={(e) => e.key === "Enter" && handleSendMessage()}
                  />
                  <Button size="icon" onClick={handleSendMessage}>
                    <Send className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </div>
          )}
        </SheetContent>
      </Sheet>
    </div>
  )
}

