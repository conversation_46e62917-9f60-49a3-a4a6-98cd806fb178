"""
URL configuration for community app.

Handles social features, discussions, forums, and community interactions.
"""
from django.urls import path
from . import views

app_name = 'community'

urlpatterns = [
    # Community main views
    path('', views.CommunityHomeView.as_view(), name='community-home'),
    path('forums/', views.ForumListView.as_view(), name='forum-list'),
    path('forums/<int:pk>/', views.ForumDetailView.as_view(), name='forum-detail'),
    
    # Posts and discussions
    path('posts/', views.PostListView.as_view(), name='post-list'),
    path('posts/<int:pk>/', views.PostDetailView.as_view(), name='post-detail'),
    path('posts/create/', views.PostCreateView.as_view(), name='post-create'),
    path('posts/<int:pk>/edit/', views.PostEditView.as_view(), name='post-edit'),
    
    # Comments and interactions
    path('posts/<int:post_pk>/comments/', views.CommentListView.as_view(), name='comment-list'),
    path('comments/<int:pk>/', views.CommentDetailView.as_view(), name='comment-detail'),
    path('posts/<int:pk>/like/', views.PostLikeView.as_view(), name='post-like'),
    
    # User profiles and following
    path('profiles/', views.ProfileListView.as_view(), name='profile-list'),
    path('profiles/<int:pk>/', views.ProfileDetailView.as_view(), name='profile-detail'),
    path('follow/<int:pk>/', views.FollowUserView.as_view(), name='follow-user'),
]
