"""Views for shopping app."""
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework.permissions import AllowAny, IsAuthenticated

class ShoppingHomeView(APIView):
    permission_classes = [AllowAny]
    def get(self, request): return Response({'message': 'Shopping Home', 'status': 'Phase 2 Complete'})

class ProductListView(APIView):
    permission_classes = [AllowAny]
    def get(self, request): return Response({'message': 'Product List', 'status': 'Coming in Phase 3'})

class ProductDetailView(APIView):
    permission_classes = [AllowAny]
    def get(self, request, pk): return Response({'message': f'Product Detail {pk}', 'status': 'Coming in Phase 3'})

class CategoryListView(APIView):
    permission_classes = [AllowAny]
    def get(self, request): return Response({'message': 'Category List', 'status': 'Coming in Phase 3'})

class CategoryDetailView(APIView):
    permission_classes = [AllowAny]
    def get(self, request, pk): return Response({'message': f'Category Detail {pk}', 'status': 'Coming in Phase 3'})

class CartView(APIView):
    permission_classes = [IsAuthenticated]
    def get(self, request): return Response({'message': 'Cart View', 'status': 'Coming in Phase 3'})

class AddToCartView(APIView):
    permission_classes = [IsAuthenticated]
    def post(self, request): return Response({'message': 'Add to Cart', 'status': 'Coming in Phase 3'})

class RemoveFromCartView(APIView):
    permission_classes = [IsAuthenticated]
    def post(self, request): return Response({'message': 'Remove from Cart', 'status': 'Coming in Phase 3'})

class CheckoutView(APIView):
    permission_classes = [IsAuthenticated]
    def post(self, request): return Response({'message': 'Checkout', 'status': 'Coming in Phase 3'})

class OrderListView(APIView):
    permission_classes = [IsAuthenticated]
    def get(self, request): return Response({'message': 'Order List', 'status': 'Coming in Phase 3'})

class OrderDetailView(APIView):
    permission_classes = [IsAuthenticated]
    def get(self, request, pk): return Response({'message': f'Order Detail {pk}', 'status': 'Coming in Phase 3'})

class VendorListView(APIView):
    permission_classes = [AllowAny]
    def get(self, request): return Response({'message': 'Vendor List', 'status': 'Coming in Phase 3'})

class VendorDetailView(APIView):
    permission_classes = [AllowAny]
    def get(self, request, pk): return Response({'message': f'Vendor Detail {pk}', 'status': 'Coming in Phase 3'})

class VendorDashboardView(APIView):
    permission_classes = [IsAuthenticated]
    def get(self, request): return Response({'message': 'Vendor Dashboard', 'status': 'Coming in Phase 3'})
