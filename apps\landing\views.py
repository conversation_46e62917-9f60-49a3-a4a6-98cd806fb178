"""
Views for landing app.

Handles landing page content, hero sections, features, and public-facing content.
"""
from django.shortcuts import render
from django.http import JsonResponse
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework.permissions import AllowAny


class LandingPageView(APIView):
    """Main landing page view"""
    permission_classes = [AllowAny]

    def get(self, request):
        # TODO: Render landing page template
        return Response({
            'message': 'Kominote Landing Page',
            'description': 'Seychelles All-in-One Digital Hub',
            'features': ['Streaming', 'Shopping', 'Community', 'Events', 'Jobs'],
            'status': 'Phase 2 Complete - Templates coming in Phase 3'
        })


class AboutView(APIView):
    """About page view"""
    permission_classes = [AllowAny]

    def get(self, request):
        return Response({
            'message': 'About Kominote',
            'description': 'Learn more about Seychelles digital platform',
            'status': 'Coming in Phase 3'
        })


class FeaturesView(APIView):
    """Features page view"""
    permission_classes = [AllowAny]

    def get(self, request):
        return Response({
            'message': 'Kominote Features',
            'features': {
                'streaming': 'Local and international content',
                'shopping': 'Local vendors marketplace',
                'community': 'Social features and discussions',
                'events': 'Local events and activities',
                'jobs': 'Employment opportunities'
            },
            'status': 'Coming in Phase 3'
        })


class ContactView(APIView):
    """Contact page view"""
    permission_classes = [AllowAny]

    def get(self, request):
        return Response({
            'message': 'Contact Kominote',
            'status': 'Coming in Phase 3'
        })


class HeroContentAPIView(APIView):
    """Hero content API"""
    permission_classes = [AllowAny]

    def get(self, request):
        return Response({
            'title': 'Welcome to Kominote',
            'subtitle': 'Seychelles All-in-One Digital Hub',
            'description': 'Stream, Shop, Connect, Discover Events, and Find Jobs - All in One Place',
            'cta_text': 'Get Started',
            'status': 'Phase 2 Complete'
        })


class FeaturesAPIView(APIView):
    """Features API"""
    permission_classes = [AllowAny]

    def get(self, request):
        return Response({
            'features': [
                {
                    'title': 'Streaming',
                    'description': 'Local and international content',
                    'icon': 'play-circle'
                },
                {
                    'title': 'Shopping',
                    'description': 'Local vendors marketplace',
                    'icon': 'shopping-bag'
                },
                {
                    'title': 'Community',
                    'description': 'Social features and discussions',
                    'icon': 'users'
                },
                {
                    'title': 'Events',
                    'description': 'Local events and activities',
                    'icon': 'calendar'
                },
                {
                    'title': 'Jobs',
                    'description': 'Employment opportunities',
                    'icon': 'briefcase'
                }
            ],
            'status': 'Phase 2 Complete'
        })


class TestimonialsAPIView(APIView):
    """Testimonials API"""
    permission_classes = [AllowAny]

    def get(self, request):
        return Response({
            'testimonials': [
                {
                    'name': 'Sample User',
                    'text': 'Kominote brings everything together in one platform',
                    'rating': 5
                }
            ],
            'status': 'Sample data - Real testimonials coming in Phase 3'
        })
