"""
Models for streaming app.

Handles video content, categories, playlists, user interactions, and streaming features
for the Kominote platform with focus on Seychelles content.
"""
from django.db import models
from django.contrib.auth import get_user_model
from django.core.validators import MinValueValidator, MaxValueValidator, FileExtensionValidator
from django.utils import timezone
from django.urls import reverse

User = get_user_model()


class VideoCategory(models.Model):
    """
    Categories for video content with Seychelles focus.
    """
    name = models.CharField(
        max_length=100,
        unique=True,
        help_text="Category name"
    )
    slug = models.SlugField(
        max_length=100,
        unique=True,
        help_text="URL-friendly category name"
    )
    description = models.TextField(
        blank=True,
        help_text="Category description"
    )

    # Visual elements
    icon = models.ImageField(
        upload_to='streaming/categories/icons/',
        null=True,
        blank=True,
        help_text="Category icon"
    )
    banner_image = models.ImageField(
        upload_to='streaming/categories/banners/',
        null=True,
        blank=True,
        help_text="Category banner image"
    )

    # Seychelles-specific categories
    is_local_content = models.BooleanField(
        default=False,
        help_text="Whether this category focuses on Seychelles content"
    )

    # Display settings
    is_active = models.BooleanField(
        default=True,
        help_text="Whether this category is active"
    )
    is_featured = models.BooleanField(
        default=False,
        help_text="Show in featured categories"
    )
    display_order = models.PositiveIntegerField(
        default=1,
        help_text="Display order"
    )

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'streaming_videocategory'
        verbose_name = 'Video Category'
        verbose_name_plural = 'Video Categories'
        ordering = ['display_order', 'name']

    def __str__(self):
        return self.name

    def get_absolute_url(self):
        return reverse('streaming:category', kwargs={'slug': self.slug})


class VideoContent(models.Model):
    """
    Main model for all video content (movies, series, documentaries).
    """
    CONTENT_TYPES = [
        ('movie', 'Movie'),
        ('series', 'Series'),
        ('documentary', 'Documentary'),
        ('short', 'Short Film'),
        ('live', 'Live Stream'),
    ]

    QUALITY_CHOICES = [
        ('240p', '240p'),
        ('360p', '360p'),
        ('480p', '480p'),
        ('720p', '720p (HD)'),
        ('1080p', '1080p (Full HD)'),
        ('1440p', '1440p (2K)'),
        ('2160p', '2160p (4K)'),
    ]

    LANGUAGE_CHOICES = [
        ('en', 'English'),
        ('fr', 'French'),
        ('cr', 'Creole'),
        ('multi', 'Multiple Languages'),
    ]

    # Basic Information
    title = models.CharField(
        max_length=200,
        help_text="Content title"
    )
    slug = models.SlugField(
        max_length=200,
        unique=True,
        help_text="URL-friendly title"
    )
    description = models.TextField(
        help_text="Content description"
    )
    short_description = models.CharField(
        max_length=300,
        blank=True,
        help_text="Short description for cards and previews"
    )

    # Content Classification
    content_type = models.CharField(
        max_length=20,
        choices=CONTENT_TYPES,
        help_text="Type of content"
    )
    category = models.ForeignKey(
        VideoCategory,
        on_delete=models.SET_NULL,
        null=True,
        related_name='content'
    )

    # Media Files
    video_file = models.FileField(
        upload_to='streaming/videos/',
        null=True,
        blank=True,
        validators=[FileExtensionValidator(allowed_extensions=['mp4', 'webm', 'mov'])],
        help_text="Main video file"
    )
    trailer_file = models.FileField(
        upload_to='streaming/trailers/',
        null=True,
        blank=True,
        validators=[FileExtensionValidator(allowed_extensions=['mp4', 'webm', 'mov'])],
        help_text="Trailer video file"
    )

    # Images
    thumbnail = models.ImageField(
        upload_to='streaming/thumbnails/',
        null=True,
        blank=True,
        help_text="Content thumbnail"
    )
    poster = models.ImageField(
        upload_to='streaming/posters/',
        null=True,
        blank=True,
        help_text="Content poster"
    )
    banner = models.ImageField(
        upload_to='streaming/banners/',
        null=True,
        blank=True,
        help_text="Content banner for hero sections"
    )

    # Technical Details
    duration_minutes = models.PositiveIntegerField(
        null=True,
        blank=True,
        help_text="Duration in minutes"
    )
    quality = models.CharField(
        max_length=10,
        choices=QUALITY_CHOICES,
        default='720p',
        help_text="Video quality"
    )
    file_size_mb = models.PositiveIntegerField(
        null=True,
        blank=True,
        help_text="File size in MB"
    )

    # Content Details
    language = models.CharField(
        max_length=10,
        choices=LANGUAGE_CHOICES,
        default='en',
        help_text="Primary language"
    )
    has_subtitles = models.BooleanField(
        default=False,
        help_text="Whether subtitles are available"
    )
    subtitle_languages = models.CharField(
        max_length=100,
        blank=True,
        help_text="Available subtitle languages (comma-separated)"
    )

    # Production Information
    director = models.CharField(
        max_length=200,
        blank=True,
        help_text="Director name(s)"
    )
    producer = models.CharField(
        max_length=200,
        blank=True,
        help_text="Producer name(s)"
    )
    cast = models.TextField(
        blank=True,
        help_text="Cast members (comma-separated)"
    )
    production_year = models.PositiveIntegerField(
        null=True,
        blank=True,
        help_text="Year of production"
    )

    # Seychelles Context
    is_local_production = models.BooleanField(
        default=False,
        help_text="Whether this is a Seychelles production"
    )
    filming_locations = models.CharField(
        max_length=300,
        blank=True,
        help_text="Filming locations in Seychelles"
    )
    local_cast = models.TextField(
        blank=True,
        help_text="Local Seychellois cast members"
    )

    # Publishing and Access
    is_published = models.BooleanField(
        default=False,
        help_text="Whether content is published and visible"
    )
    is_featured = models.BooleanField(
        default=False,
        help_text="Show in featured content"
    )
    is_premium = models.BooleanField(
        default=False,
        help_text="Whether this is premium content"
    )
    publish_date = models.DateTimeField(
        default=timezone.now,
        help_text="When content becomes available"
    )

    # Statistics
    view_count = models.PositiveIntegerField(
        default=0,
        help_text="Total view count"
    )
    like_count = models.PositiveIntegerField(
        default=0,
        help_text="Total likes"
    )
    average_rating = models.DecimalField(
        max_digits=3,
        decimal_places=2,
        default=0.00,
        help_text="Average user rating"
    )
    rating_count = models.PositiveIntegerField(
        default=0,
        help_text="Number of ratings"
    )

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    created_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        related_name='created_content'
    )

    class Meta:
        db_table = 'streaming_videocontent'
        verbose_name = 'Video Content'
        verbose_name_plural = 'Video Content'
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['content_type', '-publish_date']),
            models.Index(fields=['category', '-view_count']),
            models.Index(fields=['is_featured', '-publish_date']),
        ]

    def __str__(self):
        return f"{self.title} ({self.get_content_type_display()})"

    def get_absolute_url(self):
        return reverse('streaming:content_detail', kwargs={'slug': self.slug})

    @property
    def duration_display(self):
        """Return formatted duration."""
        if self.duration_minutes:
            hours = self.duration_minutes // 60
            minutes = self.duration_minutes % 60
            if hours > 0:
                return f"{hours}h {minutes}m"
            return f"{minutes}m"
        return "Unknown"

    @property
    def is_available(self):
        """Check if content is available for viewing."""
        return self.is_published and self.publish_date <= timezone.now()


class Season(models.Model):
    """
    Seasons for series content.
    """
    series = models.ForeignKey(
        VideoContent,
        on_delete=models.CASCADE,
        related_name='seasons',
        limit_choices_to={'content_type': 'series'}
    )
    season_number = models.PositiveIntegerField(
        help_text="Season number"
    )
    title = models.CharField(
        max_length=200,
        blank=True,
        help_text="Season title (optional)"
    )
    description = models.TextField(
        blank=True,
        help_text="Season description"
    )

    # Media
    poster = models.ImageField(
        upload_to='streaming/seasons/',
        null=True,
        blank=True,
        help_text="Season poster"
    )

    # Release Information
    release_date = models.DateField(
        null=True,
        blank=True,
        help_text="Season release date"
    )

    # Status
    is_complete = models.BooleanField(
        default=False,
        help_text="Whether all episodes are released"
    )

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'streaming_season'
        verbose_name = 'Season'
        verbose_name_plural = 'Seasons'
        ordering = ['series', 'season_number']
        unique_together = ['series', 'season_number']

    def __str__(self):
        if self.title:
            return f"{self.series.title} - Season {self.season_number}: {self.title}"
        return f"{self.series.title} - Season {self.season_number}"

    @property
    def episode_count(self):
        """Return number of episodes in this season."""
        return self.episodes.count()


class Episode(models.Model):
    """
    Individual episodes within a season.
    """
    season = models.ForeignKey(
        Season,
        on_delete=models.CASCADE,
        related_name='episodes'
    )
    episode_number = models.PositiveIntegerField(
        help_text="Episode number within the season"
    )
    title = models.CharField(
        max_length=200,
        help_text="Episode title"
    )
    slug = models.SlugField(
        max_length=250,
        help_text="URL-friendly episode identifier"
    )
    description = models.TextField(
        blank=True,
        help_text="Episode description"
    )

    # Media Files
    video_file = models.FileField(
        upload_to='streaming/episodes/',
        validators=[FileExtensionValidator(allowed_extensions=['mp4', 'webm', 'mov'])],
        help_text="Episode video file"
    )
    thumbnail = models.ImageField(
        upload_to='streaming/episode_thumbnails/',
        null=True,
        blank=True,
        help_text="Episode thumbnail"
    )

    # Technical Details
    duration_minutes = models.PositiveIntegerField(
        help_text="Episode duration in minutes"
    )
    quality = models.CharField(
        max_length=10,
        choices=VideoContent.QUALITY_CHOICES,
        default='720p',
        help_text="Video quality"
    )

    # Release Information
    air_date = models.DateTimeField(
        help_text="When the episode airs/becomes available"
    )

    # Status
    is_published = models.BooleanField(
        default=False,
        help_text="Whether episode is published"
    )

    # Statistics
    view_count = models.PositiveIntegerField(
        default=0,
        help_text="Episode view count"
    )

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'streaming_episode'
        verbose_name = 'Episode'
        verbose_name_plural = 'Episodes'
        ordering = ['season', 'episode_number']
        unique_together = ['season', 'episode_number']
        indexes = [
            models.Index(fields=['season', 'episode_number']),
            models.Index(fields=['air_date']),
        ]

    def __str__(self):
        return f"{self.season.series.title} S{self.season.season_number}E{self.episode_number}: {self.title}"

    def get_absolute_url(self):
        return reverse('streaming:episode_detail', kwargs={'slug': self.slug})

    @property
    def is_available(self):
        """Check if episode is available for viewing."""
        return self.is_published and self.air_date <= timezone.now()


class Playlist(models.Model):
    """
    User-created playlists for organizing content.
    """
    PRIVACY_CHOICES = [
        ('public', 'Public'),
        ('unlisted', 'Unlisted'),
        ('private', 'Private'),
    ]

    user = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='playlists'
    )
    name = models.CharField(
        max_length=100,
        help_text="Playlist name"
    )
    description = models.TextField(
        blank=True,
        help_text="Playlist description"
    )

    # Content
    content = models.ManyToManyField(
        VideoContent,
        through='PlaylistItem',
        related_name='playlists'
    )

    # Settings
    privacy = models.CharField(
        max_length=10,
        choices=PRIVACY_CHOICES,
        default='private',
        help_text="Playlist privacy setting"
    )

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'streaming_playlist'
        verbose_name = 'Playlist'
        verbose_name_plural = 'Playlists'
        ordering = ['-updated_at']

    def __str__(self):
        return f"{self.user.get_display_name()}'s {self.name}"

    @property
    def item_count(self):
        """Return number of items in playlist."""
        return self.playlist_items.count()


class PlaylistItem(models.Model):
    """
    Items within a playlist with ordering.
    """
    playlist = models.ForeignKey(
        Playlist,
        on_delete=models.CASCADE,
        related_name='playlist_items'
    )
    content = models.ForeignKey(
        VideoContent,
        on_delete=models.CASCADE,
        related_name='playlist_items'
    )
    order = models.PositiveIntegerField(
        default=1,
        help_text="Order within the playlist"
    )
    added_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        db_table = 'streaming_playlistitem'
        verbose_name = 'Playlist Item'
        verbose_name_plural = 'Playlist Items'
        ordering = ['playlist', 'order']
        unique_together = ['playlist', 'content']

    def __str__(self):
        return f"{self.content.title} in {self.playlist.name}"


class WatchHistory(models.Model):
    """
    Track user viewing history and progress.
    """
    user = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='watch_history'
    )
    content = models.ForeignKey(
        VideoContent,
        on_delete=models.CASCADE,
        related_name='watch_history',
        null=True,
        blank=True
    )
    episode = models.ForeignKey(
        Episode,
        on_delete=models.CASCADE,
        related_name='watch_history',
        null=True,
        blank=True
    )

    # Progress tracking
    watch_time_seconds = models.PositiveIntegerField(
        default=0,
        help_text="Time watched in seconds"
    )
    total_duration_seconds = models.PositiveIntegerField(
        help_text="Total content duration in seconds"
    )
    progress_percentage = models.DecimalField(
        max_digits=5,
        decimal_places=2,
        default=0.00,
        help_text="Watch progress as percentage"
    )

    # Status
    is_completed = models.BooleanField(
        default=False,
        help_text="Whether content was watched to completion"
    )

    # Device and session info
    device_type = models.CharField(
        max_length=50,
        blank=True,
        help_text="Device used for watching"
    )
    ip_address = models.GenericIPAddressField(
        null=True,
        blank=True
    )

    # Timestamps
    started_at = models.DateTimeField(auto_now_add=True)
    last_watched_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'streaming_watchhistory'
        verbose_name = 'Watch History'
        verbose_name_plural = 'Watch History'
        ordering = ['-last_watched_at']
        indexes = [
            models.Index(fields=['user', '-last_watched_at']),
            models.Index(fields=['content', '-last_watched_at']),
        ]

    def __str__(self):
        content_title = self.episode.title if self.episode else self.content.title
        return f"{self.user.get_display_name()} watched {content_title} ({self.progress_percentage}%)"

    def save(self, *args, **kwargs):
        """Calculate progress percentage on save."""
        if self.total_duration_seconds > 0:
            self.progress_percentage = (self.watch_time_seconds / self.total_duration_seconds) * 100
            self.is_completed = self.progress_percentage >= 90  # Consider 90%+ as completed
        super().save(*args, **kwargs)


class UserRating(models.Model):
    """
    User ratings and reviews for content.
    """
    user = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='content_ratings'
    )
    content = models.ForeignKey(
        VideoContent,
        on_delete=models.CASCADE,
        related_name='user_ratings'
    )

    # Rating
    rating = models.PositiveIntegerField(
        validators=[MinValueValidator(1), MaxValueValidator(5)],
        help_text="Rating from 1 to 5 stars"
    )

    # Review
    review = models.TextField(
        blank=True,
        help_text="Optional written review"
    )

    # Moderation
    is_approved = models.BooleanField(
        default=True,
        help_text="Whether the review is approved for display"
    )

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'streaming_userrating'
        verbose_name = 'User Rating'
        verbose_name_plural = 'User Ratings'
        ordering = ['-created_at']
        unique_together = ['user', 'content']

    def __str__(self):
        return f"{self.user.get_display_name()} rated {self.content.title}: {self.rating}/5"


class ContentTag(models.Model):
    """
    Tags for content discovery and organization.
    """
    name = models.CharField(
        max_length=50,
        unique=True,
        help_text="Tag name"
    )
    slug = models.SlugField(
        max_length=50,
        unique=True,
        help_text="URL-friendly tag name"
    )
    description = models.TextField(
        blank=True,
        help_text="Tag description"
    )

    # Seychelles context
    is_local_tag = models.BooleanField(
        default=False,
        help_text="Whether this tag relates to Seychelles content"
    )

    # Usage statistics
    usage_count = models.PositiveIntegerField(
        default=0,
        help_text="Number of times this tag is used"
    )

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        db_table = 'streaming_contenttag'
        verbose_name = 'Content Tag'
        verbose_name_plural = 'Content Tags'
        ordering = ['name']

    def __str__(self):
        return self.name


class ContentTagging(models.Model):
    """
    Many-to-many relationship between content and tags.
    """
    content = models.ForeignKey(
        VideoContent,
        on_delete=models.CASCADE,
        related_name='content_tags'
    )
    tag = models.ForeignKey(
        ContentTag,
        on_delete=models.CASCADE,
        related_name='tagged_content'
    )
    added_at = models.DateTimeField(auto_now_add=True)
    added_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        related_name='added_tags'
    )

    class Meta:
        db_table = 'streaming_contenttagging'
        verbose_name = 'Content Tagging'
        verbose_name_plural = 'Content Tagging'
        unique_together = ['content', 'tag']

    def __str__(self):
        return f"{self.content.title} tagged with {self.tag.name}"


class StreamingProfile(models.Model):
    """
    User preferences and settings for streaming.
    """
    QUALITY_PREFERENCES = [
        ('auto', 'Auto (Based on connection)'),
        ('240p', '240p (Data Saver)'),
        ('360p', '360p'),
        ('480p', '480p'),
        ('720p', '720p (HD)'),
        ('1080p', '1080p (Full HD)'),
    ]

    user = models.OneToOneField(
        User,
        on_delete=models.CASCADE,
        related_name='streaming_profile'
    )

    # Viewing Preferences
    preferred_quality = models.CharField(
        max_length=10,
        choices=QUALITY_PREFERENCES,
        default='auto',
        help_text="Preferred video quality"
    )
    auto_play_next = models.BooleanField(
        default=True,
        help_text="Automatically play next episode"
    )
    skip_intro = models.BooleanField(
        default=False,
        help_text="Automatically skip intro sequences"
    )

    # Language Preferences
    preferred_language = models.CharField(
        max_length=10,
        choices=VideoContent.LANGUAGE_CHOICES,
        default='en',
        help_text="Preferred content language"
    )
    subtitle_language = models.CharField(
        max_length=10,
        choices=VideoContent.LANGUAGE_CHOICES,
        default='en',
        help_text="Preferred subtitle language"
    )
    always_show_subtitles = models.BooleanField(
        default=False,
        help_text="Always show subtitles when available"
    )

    # Content Preferences
    favorite_categories = models.ManyToManyField(
        VideoCategory,
        blank=True,
        related_name='favorited_by_users',
        help_text="User's favorite content categories"
    )
    prefer_local_content = models.BooleanField(
        default=True,
        help_text="Prefer Seychelles local content in recommendations"
    )

    # Parental Controls
    content_rating_limit = models.CharField(
        max_length=10,
        choices=[
            ('G', 'General Audiences'),
            ('PG', 'Parental Guidance'),
            ('PG13', 'PG-13'),
            ('R', 'Restricted'),
            ('NC17', 'Adults Only'),
        ],
        default='R',
        help_text="Maximum content rating to show"
    )

    # Data Usage
    limit_mobile_quality = models.BooleanField(
        default=True,
        help_text="Limit video quality on mobile data"
    )
    mobile_quality_limit = models.CharField(
        max_length=10,
        choices=QUALITY_PREFERENCES[:4],  # Only lower qualities
        default='480p',
        help_text="Maximum quality on mobile data"
    )

    # Notifications
    notify_new_episodes = models.BooleanField(
        default=True,
        help_text="Notify when new episodes are available"
    )
    notify_new_local_content = models.BooleanField(
        default=True,
        help_text="Notify about new Seychelles content"
    )

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'streaming_streamingprofile'
        verbose_name = 'Streaming Profile'
        verbose_name_plural = 'Streaming Profiles'

    def __str__(self):
        return f"{self.user.get_display_name()}'s Streaming Profile"
