"""
Views for api app.

Centralized API endpoints and versioning for the Kominote platform.
"""
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework.permissions import AllowAny, IsAuthenticated


class APIRootView(APIView):
    """API root endpoint"""
    permission_classes = [AllowAny]

    def get(self, request):
        return Response({
            'message': 'Kominote API v1.0',
            'description': 'Seychelles All-in-One Digital Hub API',
            'version': '1.0.0',
            'endpoints': {
                'accounts': '/api/v1/accounts/',
                'streaming': '/api/v1/streaming/',
                'shopping': '/api/v1/shopping/',
                'community': '/api/v1/community/',
                'events': '/api/v1/events/',
                'jobs': '/api/v1/jobs/',
                'health': '/api/health/',
                'docs': '/api/docs/',
            },
            'status': 'Phase 2 Complete'
        })


class HealthCheckView(APIView):
    """Health check endpoint"""
    permission_classes = [AllowAny]

    def get(self, request):
        return Response({
            'status': 'healthy',
            'message': 'Kominote API is running',
            'version': '1.0.0'
        })


class APIVersionView(APIView):
    """API version endpoint"""
    permission_classes = [AllowAny]

    def get(self, request):
        return Response({
            'version': '1.0.0',
            'phase': 'Phase 2 Complete',
            'features': ['Apps Created', 'URLs Configured', 'Basic Views']
        })


class GlobalSearchView(APIView):
    """Global search endpoint"""
    permission_classes = [AllowAny]

    def get(self, request):
        return Response({
            'message': 'Global search endpoint',
            'status': 'Coming in Phase 3'
        })


class DashboardDataView(APIView):
    """Dashboard data endpoint"""
    permission_classes = [IsAuthenticated]

    def get(self, request):
        return Response({
            'message': 'Dashboard data endpoint',
            'status': 'Coming in Phase 3'
        })


class NotificationListView(APIView):
    """Notifications endpoint"""
    permission_classes = [IsAuthenticated]

    def get(self, request):
        return Response({
            'message': 'Notifications endpoint',
            'status': 'Coming in Phase 3'
        })


class AnalyticsView(APIView):
    """Analytics endpoint"""
    permission_classes = [IsAuthenticated]

    def get(self, request):
        return Response({
            'message': 'Analytics endpoint',
            'status': 'Coming in Phase 3'
        })


class MobileConfigView(APIView):
    """Mobile app configuration"""
    permission_classes = [AllowAny]

    def get(self, request):
        return Response({
            'app_name': 'Kominote',
            'version': '1.0.0',
            'api_base_url': request.build_absolute_uri('/api/'),
            'features_enabled': {
                'streaming': True,
                'shopping': True,
                'community': True,
                'events': True,
                'jobs': True
            }
        })


class MobileSyncView(APIView):
    """Mobile sync endpoint"""
    permission_classes = [IsAuthenticated]

    def post(self, request):
        return Response({
            'message': 'Mobile sync endpoint',
            'status': 'Coming in Phase 3'
        })
