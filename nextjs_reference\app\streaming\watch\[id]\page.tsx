"use client"

import Link from "next/link"
import { But<PERSON> } from "@/components/ui/button"
import { Slider } from "@/components/ui/slider"
import { ArrowLeft, Play, Pause, Volume2, VolumeX, Settings, Maximize, SkipBack, SkipForward } from "lucide-react"
import { UserNav } from "@/components/user-nav"
import { useState, useEffect, useRef } from "react"

export default function VideoPlayerPage({ params }) {
  const { id } = params

  // This would normally come from an API or database
  // For demo purposes, we'll create mock data based on the ID
  const content = getContentDetails(id)

  return (
    <div className="min-h-screen bg-black text-white">
      {/* Minimal Header */}
      <header className="absolute top-0 left-0 right-0 z-50 bg-gradient-to-b from-black/80 to-transparent">
        <div className="container flex h-16 items-center justify-between px-4">
          <div className="flex items-center gap-4">
            <Link href={`/streaming/details/${content.baseId || id.split("-")[0]}`}>
              <Button variant="ghost" className="text-white hover:bg-white/10">
                <ArrowLeft className="h-5 w-5 mr-2" /> Back
              </Button>
            </Link>
            <h1 className="text-lg font-medium">{content.title}</h1>
          </div>
          <div className="flex items-center gap-4">
            <UserNav />
          </div>
        </div>
      </header>

      {/* Video Player */}
      <VideoPlayer content={content} />

      {/* Related Content */}
      <section className="container px-4 py-8">
        <h2 className="text-xl font-bold mb-4">Up Next</h2>
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
          {content.related &&
            content.related.slice(0, 4).map((item, index) => (
              <Link key={index} href={`/streaming/watch/${item.id}`} className="group">
                <div className="relative aspect-video overflow-hidden rounded-md cursor-pointer transition-transform duration-200 hover:scale-105">
                  <img
                    src={`/placeholder.svg?height=180&width=320&text=${encodeURIComponent(item.title)}`}
                    alt={item.title}
                    className="object-cover w-full h-full"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                  <div className="absolute bottom-0 left-0 right-0 p-3">
                    <h3 className="text-sm font-medium">{item.title}</h3>
                    <p className="text-xs text-gray-400">
                      {item.type === "series" ? "Series" : "Movie"} • {item.genre}
                    </p>
                  </div>
                  <div className="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity">
                    <Button size="icon" className="h-12 w-12 rounded-full bg-blue-600/80 hover:bg-blue-700">
                      <Play className="h-6 w-6" />
                    </Button>
                  </div>
                </div>
              </Link>
            ))}
        </div>
      </section>
    </div>
  )
}

// Video Player Component with controls
function VideoPlayer({ content }) {
  const [isPlaying, setIsPlaying] = useState(false)
  const [isMuted, setIsMuted] = useState(false)
  const [currentTime, setCurrentTime] = useState(0)
  const [duration, setDuration] = useState(0)
  const [showControls, setShowControls] = useState(true)
  const [volume, setVolume] = useState(80)
  const videoRef = useRef(null)
  const controlsTimeoutRef = useRef(null)

  // Format time in MM:SS
  const formatTime = (time) => {
    const minutes = Math.floor(time / 60)
    const seconds = Math.floor(time % 60)
    return `${minutes}:${seconds < 10 ? "0" : ""}${seconds}`
  }

  // Handle play/pause
  const togglePlay = () => {
    if (isPlaying) {
      videoRef.current.pause()
    } else {
      videoRef.current.play()
    }
    setIsPlaying(!isPlaying)
  }

  // Handle mute/unmute
  const toggleMute = () => {
    videoRef.current.muted = !isMuted
    setIsMuted(!isMuted)
  }

  // Handle volume change
  const handleVolumeChange = (value) => {
    const newVolume = value[0]
    setVolume(newVolume)
    videoRef.current.volume = newVolume / 100
    if (newVolume === 0) {
      setIsMuted(true)
      videoRef.current.muted = true
    } else if (isMuted) {
      setIsMuted(false)
      videoRef.current.muted = false
    }
  }

  // Handle seeking
  const handleSeek = (value) => {
    const newTime = (value[0] / 100) * duration
    setCurrentTime(newTime)
    videoRef.current.currentTime = newTime
  }

  // Handle time update
  const handleTimeUpdate = () => {
    setCurrentTime(videoRef.current.currentTime)
  }

  // Handle video metadata loaded
  const handleLoadedMetadata = () => {
    setDuration(videoRef.current.duration)
  }

  // Handle fullscreen
  const toggleFullscreen = () => {
    if (document.fullscreenElement) {
      document.exitFullscreen()
    } else {
      videoRef.current.requestFullscreen()
    }
  }

  // Show/hide controls on mouse movement
  const handleMouseMove = () => {
    setShowControls(true)
    clearTimeout(controlsTimeoutRef.current)
    controlsTimeoutRef.current = setTimeout(() => {
      if (isPlaying) {
        setShowControls(false)
      }
    }, 3000)
  }

  // Auto-hide controls after a delay
  useEffect(() => {
    if (isPlaying) {
      controlsTimeoutRef.current = setTimeout(() => {
        setShowControls(false)
      }, 3000)
    } else {
      setShowControls(true)
    }

    return () => {
      clearTimeout(controlsTimeoutRef.current)
    }
  }, [isPlaying])

  // Clean up on unmount
  useEffect(() => {
    return () => {
      clearTimeout(controlsTimeoutRef.current)
    }
  }, [])

  return (
    <div
      className="relative w-full h-screen bg-black flex items-center justify-center"
      onMouseMove={handleMouseMove}
      onClick={togglePlay}
    >
      {/* Video Element */}
      <video
        ref={videoRef}
        className="w-full h-full object-contain"
        poster="/placeholder.svg?height=1080&width=1920"
        onTimeUpdate={handleTimeUpdate}
        onLoadedMetadata={handleLoadedMetadata}
        onEnded={() => setIsPlaying(false)}
      >
        {/* In a real app, you would have actual video sources */}
        <source src="#" type="video/mp4" />
        Your browser does not support the video tag.
      </video>

      {/* Play Button Overlay (when paused) */}
      {!isPlaying && (
        <div className="absolute inset-0 flex items-center justify-center">
          <Button
            size="icon"
            className="h-20 w-20 rounded-full bg-blue-600/80 hover:bg-blue-700"
            onClick={(e) => {
              e.stopPropagation()
              togglePlay()
            }}
          >
            <Play className="h-10 w-10" />
          </Button>
        </div>
      )}

      {/* Video Controls */}
      <div
        className={`absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/80 to-transparent px-4 py-6 transition-opacity duration-300 ${
          showControls ? "opacity-100" : "opacity-0 pointer-events-none"
        }`}
        onClick={(e) => e.stopPropagation()}
      >
        {/* Progress Bar */}
        <Slider
          value={[currentTime ? (currentTime / duration) * 100 : 0]}
          max={100}
          step={0.1}
          className="mb-4"
          onValueChange={handleSeek}
        />

        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            {/* Play/Pause Button */}
            <Button size="icon" variant="ghost" className="text-white hover:bg-white/10" onClick={togglePlay}>
              {isPlaying ? <Pause className="h-6 w-6" /> : <Play className="h-6 w-6" />}
            </Button>

            {/* Skip Backward/Forward */}
            <Button
              size="icon"
              variant="ghost"
              className="text-white hover:bg-white/10"
              onClick={() => {
                videoRef.current.currentTime = Math.max(0, currentTime - 10)
              }}
            >
              <SkipBack className="h-6 w-6" />
            </Button>

            <Button
              size="icon"
              variant="ghost"
              className="text-white hover:bg-white/10"
              onClick={() => {
                videoRef.current.currentTime = Math.min(duration, currentTime + 10)
              }}
            >
              <SkipForward className="h-6 w-6" />
            </Button>

            {/* Volume Controls */}
            <div className="flex items-center gap-2">
              <Button size="icon" variant="ghost" className="text-white hover:bg-white/10" onClick={toggleMute}>
                {isMuted || volume === 0 ? <VolumeX className="h-6 w-6" /> : <Volume2 className="h-6 w-6" />}
              </Button>
              <Slider
                value={[isMuted ? 0 : volume]}
                max={100}
                step={1}
                className="w-24"
                onValueChange={handleVolumeChange}
              />
            </div>

            {/* Time Display */}
            <div className="text-sm text-gray-300">
              {formatTime(currentTime)} / {formatTime(duration)}
            </div>
          </div>

          <div className="flex items-center gap-2">
            {/* Settings Button */}
            <Button size="icon" variant="ghost" className="text-white hover:bg-white/10">
              <Settings className="h-6 w-6" />
            </Button>

            {/* Fullscreen Button */}
            <Button size="icon" variant="ghost" className="text-white hover:bg-white/10" onClick={toggleFullscreen}>
              <Maximize className="h-6 w-6" />
            </Button>
          </div>
        </div>
      </div>
    </div>
  )
}

// Helper function to generate content details based on ID
function getContentDetails(id) {
  // Extract base ID and episode info if present
  const parts = id.split("-")
  const baseId = parts[0]
  const isEpisode = id.includes("s") && id.includes("e")

  // Default content structure
  const defaultContent = {
    title: "Content Title",
    baseId: baseId,
    type: "movie",
    genre: "Documentary",
    related: [
      { id: "coral-reef-chronicles", title: "Coral Reef Chronicles", type: "series", genre: "Documentary" },
      { id: "island-paradise", title: "Island Paradise", type: "movie", genre: "Documentary" },
      { id: "creole-kitchen", title: "Creole Kitchen", type: "series", genre: "Cultural" },
      { id: "mahé-mysteries", title: "Mahé Mysteries", type: "series", genre: "Drama" },
    ],
  }

  // Content specific to different IDs
  const contentMap = {
    "seychelles-untold": {
      title: "Seychelles Untold",
      baseId: "seychelles-untold",
      type: "series",
      genre: "Documentary",
      related: [
        { id: "coral-reef-chronicles", title: "Coral Reef Chronicles", type: "series", genre: "Documentary" },
        { id: "island-paradise", title: "Island Paradise", type: "movie", genre: "Documentary" },
        { id: "ocean-guardians", title: "Ocean Guardians", type: "movie", genre: "Documentary" },
        { id: "island-beats", title: "Island Beats", type: "series", genre: "Cultural" },
      ],
    },
    "island-paradise": {
      title: "Island Paradise",
      baseId: "island-paradise",
      type: "movie",
      genre: "Documentary",
      related: [
        { id: "seychelles-untold", title: "Seychelles Untold", type: "series", genre: "Documentary" },
        { id: "coral-reef-chronicles", title: "Coral Reef Chronicles", type: "series", genre: "Documentary" },
        { id: "ocean-guardians", title: "Ocean Guardians", type: "movie", genre: "Documentary" },
        { id: "seychelles-from-above", title: "Seychelles From Above", type: "movie", genre: "Documentary" },
      ],
    },
    "coral-reef-chronicles": {
      title: "Coral Reef Chronicles",
      baseId: "coral-reef-chronicles",
      type: "series",
      genre: "Documentary",
      related: [
        { id: "seychelles-untold", title: "Seychelles Untold", type: "series", genre: "Documentary" },
        { id: "ocean-guardians", title: "Ocean Guardians", type: "movie", genre: "Documentary" },
        { id: "island-paradise", title: "Island Paradise", type: "movie", genre: "Documentary" },
        { id: "marine-life", title: "Marine Life of Seychelles", type: "series", genre: "Documentary" },
      ],
    },
  }

  // Handle episode-specific content
  if (isEpisode && contentMap[baseId]) {
    const seasonEpisode = id.match(/s(\d+)e(\d+)/i)
    if (seasonEpisode) {
      const season = Number.parseInt(seasonEpisode[1])
      const episode = Number.parseInt(seasonEpisode[2])

      return {
        ...contentMap[baseId],
        title: `${contentMap[baseId].title} - S${season}:E${episode}`,
        season,
        episode,
      }
    }
  }

  // Return specific content if it exists, otherwise return default with the ID as title
  return (
    contentMap[baseId] || {
      ...defaultContent,
      title: baseId
        .split("-")
        .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
        .join(" "),
    }
  )
}

