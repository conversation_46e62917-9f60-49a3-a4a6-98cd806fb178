"""
Models for community app.

Handles social features including forums, posts, comments, user interactions,
and community moderation for the Kominote platform with Seychelles focus.
"""
from django.db import models
from django.contrib.auth import get_user_model

from django.urls import reverse
from django.utils import timezone

User = get_user_model()


class ForumCategory(models.Model):
    """
    Categories for organizing forum discussions.
    """
    name = models.Char<PERSON>ield(
        max_length=100,
        unique=True,
        help_text="Category name"
    )
    slug = models.SlugField(
        max_length=100,
        unique=True,
        help_text="URL-friendly category name"
    )
    description = models.TextField(
        blank=True,
        help_text="Category description"
    )

    # Visual elements
    icon = models.CharField(
        max_length=50,
        blank=True,
        help_text="Icon class name (e.g., 'fas fa-comments')"
    )
    color = models.CharField(
        max_length=7,
        default='#3B82F6',
        help_text="Category color (hex code)"
    )

    # Seychelles context
    is_local_category = models.BooleanField(
        default=False,
        help_text="Whether this category focuses on Seychelles topics"
    )

    # Settings
    is_active = models.<PERSON><PERSON>an<PERSON>ield(
        default=True,
        help_text="Whether this category is active"
    )
    is_featured = models.<PERSON>oleanField(
        default=False,
        help_text="Show in featured categories"
    )
    display_order = models.PositiveIntegerField(
        default=1,
        help_text="Display order"
    )

    # Moderation
    requires_approval = models.BooleanField(
        default=False,
        help_text="Whether posts in this category require approval"
    )

    # Statistics
    post_count = models.PositiveIntegerField(
        default=0,
        help_text="Number of posts in this category"
    )

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'community_forumcategory'
        verbose_name = 'Forum Category'
        verbose_name_plural = 'Forum Categories'
        ordering = ['display_order', 'name']

    def __str__(self):
        return self.name

    def get_absolute_url(self):
        return reverse('community:category', kwargs={'slug': self.slug})


class Post(models.Model):
    """
    User posts in the community forum.
    """
    POST_TYPES = [
        ('discussion', 'Discussion'),
        ('question', 'Question'),
        ('announcement', 'Announcement'),
        ('event', 'Event'),
        ('local_news', 'Local News'),
        ('recommendation', 'Recommendation'),
    ]

    STATUS_CHOICES = [
        ('draft', 'Draft'),
        ('published', 'Published'),
        ('pending', 'Pending Approval'),
        ('rejected', 'Rejected'),
        ('archived', 'Archived'),
    ]

    # Basic Information
    title = models.CharField(
        max_length=200,
        help_text="Post title"
    )
    slug = models.SlugField(
        max_length=200,
        unique=True,
        help_text="URL-friendly post title"
    )
    content = models.TextField(
        help_text="Post content"
    )
    excerpt = models.CharField(
        max_length=300,
        blank=True,
        help_text="Short excerpt for previews"
    )

    # Author and Category
    author = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='community_posts'
    )
    category = models.ForeignKey(
        ForumCategory,
        on_delete=models.SET_NULL,
        null=True,
        related_name='posts'
    )

    # Post Classification
    post_type = models.CharField(
        max_length=20,
        choices=POST_TYPES,
        default='discussion',
        help_text="Type of post"
    )
    status = models.CharField(
        max_length=20,
        choices=STATUS_CHOICES,
        default='published',
        help_text="Post status"
    )

    # Media
    featured_image = models.ImageField(
        upload_to='community/posts/',
        null=True,
        blank=True,
        help_text="Featured image for the post"
    )

    # Seychelles Context
    location = models.CharField(
        max_length=100,
        blank=True,
        help_text="Seychelles location related to this post"
    )
    island = models.CharField(
        max_length=20,
        choices=User.ISLAND_CHOICES,
        blank=True,
        help_text="Island related to this post"
    )
    is_local_content = models.BooleanField(
        default=False,
        help_text="Whether this post is about Seychelles"
    )

    # Engagement
    is_pinned = models.BooleanField(
        default=False,
        help_text="Pin this post to the top"
    )
    is_featured = models.BooleanField(
        default=False,
        help_text="Feature this post"
    )
    allow_comments = models.BooleanField(
        default=True,
        help_text="Allow comments on this post"
    )

    # Statistics
    view_count = models.PositiveIntegerField(
        default=0,
        help_text="Number of views"
    )
    like_count = models.PositiveIntegerField(
        default=0,
        help_text="Number of likes"
    )
    comment_count = models.PositiveIntegerField(
        default=0,
        help_text="Number of comments"
    )

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    published_at = models.DateTimeField(
        null=True,
        blank=True,
        help_text="When the post was published"
    )

    class Meta:
        db_table = 'community_post'
        verbose_name = 'Post'
        verbose_name_plural = 'Posts'
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['category', '-created_at']),
            models.Index(fields=['author', '-created_at']),
            models.Index(fields=['status', '-created_at']),
            models.Index(fields=['is_featured', '-created_at']),
            models.Index(fields=['is_local_content', '-created_at']),
        ]

    def __str__(self):
        return self.title

    def get_absolute_url(self):
        return reverse('community:post_detail', kwargs={'slug': self.slug})

    def save(self, *args, **kwargs):
        """Set published_at when status changes to published."""
        if self.status == 'published' and not self.published_at:
            self.published_at = timezone.now()
        super().save(*args, **kwargs)


class Comment(models.Model):
    """
    Comments on community posts.
    """
    STATUS_CHOICES = [
        ('published', 'Published'),
        ('pending', 'Pending Approval'),
        ('rejected', 'Rejected'),
        ('hidden', 'Hidden'),
    ]

    post = models.ForeignKey(
        Post,
        on_delete=models.CASCADE,
        related_name='comments'
    )
    author = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='community_comments'
    )
    parent = models.ForeignKey(
        'self',
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name='replies',
        help_text="Parent comment for threaded discussions"
    )

    # Content
    content = models.TextField(
        help_text="Comment content"
    )

    # Status
    status = models.CharField(
        max_length=20,
        choices=STATUS_CHOICES,
        default='published',
        help_text="Comment status"
    )

    # Statistics
    like_count = models.PositiveIntegerField(
        default=0,
        help_text="Number of likes"
    )

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'community_comment'
        verbose_name = 'Comment'
        verbose_name_plural = 'Comments'
        ordering = ['created_at']
        indexes = [
            models.Index(fields=['post', 'created_at']),
            models.Index(fields=['author', '-created_at']),
            models.Index(fields=['status', 'created_at']),
        ]

    def __str__(self):
        return f"Comment by {self.author.get_display_name()} on {self.post.title}"

    @property
    def is_reply(self):
        """Check if this is a reply to another comment."""
        return self.parent is not None


class UserFollow(models.Model):
    """
    User following relationships.
    """
    follower = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='following',
        help_text="User who is following"
    )
    following = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='followers',
        help_text="User being followed"
    )

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        db_table = 'community_userfollow'
        verbose_name = 'User Follow'
        verbose_name_plural = 'User Follows'
        unique_together = ['follower', 'following']
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.follower.get_display_name()} follows {self.following.get_display_name()}"


class PostLike(models.Model):
    """
    User likes on posts.
    """
    user = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='post_likes'
    )
    post = models.ForeignKey(
        Post,
        on_delete=models.CASCADE,
        related_name='likes'
    )

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        db_table = 'community_postlike'
        verbose_name = 'Post Like'
        verbose_name_plural = 'Post Likes'
        unique_together = ['user', 'post']
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.user.get_display_name()} likes {self.post.title}"


class CommentLike(models.Model):
    """
    User likes on comments.
    """
    user = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='comment_likes'
    )
    comment = models.ForeignKey(
        Comment,
        on_delete=models.CASCADE,
        related_name='likes'
    )

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        db_table = 'community_commentlike'
        verbose_name = 'Comment Like'
        verbose_name_plural = 'Comment Likes'
        unique_together = ['user', 'comment']
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.user.get_display_name()} likes comment on {self.comment.post.title}"


class PostBookmark(models.Model):
    """
    User bookmarks for posts.
    """
    user = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='post_bookmarks'
    )
    post = models.ForeignKey(
        Post,
        on_delete=models.CASCADE,
        related_name='bookmarks'
    )

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        db_table = 'community_postbookmark'
        verbose_name = 'Post Bookmark'
        verbose_name_plural = 'Post Bookmarks'
        unique_together = ['user', 'post']
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.user.get_display_name()} bookmarked {self.post.title}"


class CommunityProfile(models.Model):
    """
    Extended community profile for users.
    """
    user = models.OneToOneField(
        User,
        on_delete=models.CASCADE,
        related_name='community_profile'
    )

    # Profile Information
    bio = models.TextField(
        max_length=500,
        blank=True,
        help_text="Community bio"
    )
    website = models.URLField(
        blank=True,
        help_text="Personal website"
    )

    # Community Preferences
    show_email = models.BooleanField(
        default=False,
        help_text="Show email address in profile"
    )
    show_location = models.BooleanField(
        default=True,
        help_text="Show location in profile"
    )
    allow_messages = models.BooleanField(
        default=True,
        help_text="Allow private messages"
    )

    # Notification Preferences
    notify_on_follow = models.BooleanField(
        default=True,
        help_text="Notify when someone follows you"
    )
    notify_on_comment = models.BooleanField(
        default=True,
        help_text="Notify when someone comments on your posts"
    )
    notify_on_like = models.BooleanField(
        default=True,
        help_text="Notify when someone likes your posts"
    )
    notify_on_mention = models.BooleanField(
        default=True,
        help_text="Notify when someone mentions you"
    )

    # Community Statistics
    reputation_score = models.PositiveIntegerField(
        default=0,
        help_text="Community reputation score"
    )
    total_posts = models.PositiveIntegerField(
        default=0,
        help_text="Total number of posts"
    )
    total_comments = models.PositiveIntegerField(
        default=0,
        help_text="Total number of comments"
    )
    total_likes_received = models.PositiveIntegerField(
        default=0,
        help_text="Total likes received on posts and comments"
    )

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'community_communityprofile'
        verbose_name = 'Community Profile'
        verbose_name_plural = 'Community Profiles'

    def __str__(self):
        return f"{self.user.get_display_name()}'s Community Profile"

    @property
    def follower_count(self):
        """Return number of followers."""
        return self.user.followers.count()

    @property
    def following_count(self):
        """Return number of users being followed."""
        return self.user.following.count()


class Report(models.Model):
    """
    User reports for content moderation.
    """
    REPORT_TYPES = [
        ('spam', 'Spam'),
        ('harassment', 'Harassment'),
        ('inappropriate', 'Inappropriate Content'),
        ('misinformation', 'Misinformation'),
        ('copyright', 'Copyright Violation'),
        ('other', 'Other'),
    ]

    STATUS_CHOICES = [
        ('pending', 'Pending'),
        ('reviewing', 'Under Review'),
        ('resolved', 'Resolved'),
        ('dismissed', 'Dismissed'),
    ]

    # Reporter
    reporter = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='reports_made'
    )

    # Content being reported
    post = models.ForeignKey(
        Post,
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name='reports'
    )
    comment = models.ForeignKey(
        Comment,
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name='reports'
    )
    reported_user = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name='reports_against'
    )

    # Report Details
    report_type = models.CharField(
        max_length=20,
        choices=REPORT_TYPES,
        help_text="Type of report"
    )
    description = models.TextField(
        help_text="Description of the issue"
    )

    # Status
    status = models.CharField(
        max_length=20,
        choices=STATUS_CHOICES,
        default='pending',
        help_text="Report status"
    )

    # Moderation
    reviewed_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='reports_reviewed'
    )
    reviewed_at = models.DateTimeField(
        null=True,
        blank=True,
        help_text="When the report was reviewed"
    )
    resolution_notes = models.TextField(
        blank=True,
        help_text="Notes about the resolution"
    )

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'community_report'
        verbose_name = 'Report'
        verbose_name_plural = 'Reports'
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['status', '-created_at']),
            models.Index(fields=['report_type', '-created_at']),
        ]

    def __str__(self):
        content_type = "post" if self.post else "comment" if self.comment else "user"
        return f"Report by {self.reporter.get_display_name()} - {content_type} ({self.report_type})"


class Notification(models.Model):
    """
    User notifications for community activities.
    """
    NOTIFICATION_TYPES = [
        ('follow', 'New Follower'),
        ('post_like', 'Post Liked'),
        ('comment_like', 'Comment Liked'),
        ('comment', 'New Comment'),
        ('reply', 'Comment Reply'),
        ('mention', 'Mentioned in Post/Comment'),
        ('post_featured', 'Post Featured'),
    ]

    recipient = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='notifications'
    )
    sender = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='sent_notifications',
        null=True,
        blank=True
    )

    # Notification Details
    notification_type = models.CharField(
        max_length=20,
        choices=NOTIFICATION_TYPES,
        help_text="Type of notification"
    )
    title = models.CharField(
        max_length=200,
        help_text="Notification title"
    )
    message = models.TextField(
        help_text="Notification message"
    )

    # Related Content
    post = models.ForeignKey(
        Post,
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name='notifications'
    )
    comment = models.ForeignKey(
        Comment,
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name='notifications'
    )

    # Status
    is_read = models.BooleanField(
        default=False,
        help_text="Whether the notification has been read"
    )

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    read_at = models.DateTimeField(
        null=True,
        blank=True,
        help_text="When the notification was read"
    )

    class Meta:
        db_table = 'community_notification'
        verbose_name = 'Notification'
        verbose_name_plural = 'Notifications'
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['recipient', '-created_at']),
            models.Index(fields=['is_read', '-created_at']),
        ]

    def __str__(self):
        return f"Notification for {self.recipient.get_display_name()}: {self.title}"

    def mark_as_read(self):
        """Mark notification as read."""
        if not self.is_read:
            self.is_read = True
            self.read_at = timezone.now()
            self.save()
