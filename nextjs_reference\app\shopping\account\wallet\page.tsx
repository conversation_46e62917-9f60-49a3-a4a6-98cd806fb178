import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import {
  Wallet,
  Plus,
  ArrowUpRight,
  ArrowDownLeft,
  CreditCard,
  Clock,
  CheckCircle2,
  AlertCircle,
  Download,
  Filter,
  Search,
} from "lucide-react"
import FeatureNavigation from "@/components/feature-navigation"
import { Messenger } from "@/components/messenger"

export default function WalletPage() {
  // In a real app, you would fetch wallet data from an API
  const walletData = {
    balance: 120.5,
    currency: "USD",
    cards: [
      {
        id: "card-1",
        type: "Visa",
        last4: "4242",
        expiry: "04/26",
        isDefault: true,
      },
      {
        id: "card-2",
        type: "Mastercard",
        last4: "5555",
        expiry: "08/25",
        isDefault: false,
      },
    ],
    transactions: [
      {
        id: "txn-1",
        type: "deposit",
        amount: 50.0,
        date: "Mar 25, 2025",
        status: "completed",
        description: "Wallet Top-up",
        source: "Visa •••• 4242",
      },
      {
        id: "txn-2",
        type: "withdrawal",
        amount: 24.99,
        date: "Mar 22, 2025",
        status: "completed",
        description: "Payment to Island Crafts Co.",
        orderId: "ORD-12345",
      },
      {
        id: "txn-3",
        type: "deposit",
        amount: 100.0,
        date: "Mar 15, 2025",
        status: "completed",
        description: "Wallet Top-up",
        source: "Mastercard •••• 5555",
      },
      {
        id: "txn-4",
        type: "withdrawal",
        amount: 35.5,
        date: "Mar 10, 2025",
        status: "completed",
        description: "Payment to Eco Seychelles",
        orderId: "ORD-12340",
      },
      {
        id: "txn-5",
        type: "deposit",
        amount: 25.0,
        date: "Mar 5, 2025",
        status: "pending",
        description: "Refund for Order #ORD-12338",
        orderId: "ORD-12338",
      },
    ],
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header - Simplified for this page */}
      <header className="sticky top-0 z-50 w-full bg-white border-b shadow-sm">
        <div className="container flex h-16 items-center justify-between px-4">
          <div className="flex items-center gap-8">
            <Link href="/" className="flex items-center space-x-2">
              <span className="text-2xl font-bold bg-gradient-to-r from-blue-600 to-teal-500 bg-clip-text text-transparent">
                Kominote
              </span>
            </Link>
          </div>
        </div>
      </header>

      <main className="container px-4 py-8 md:px-6">
        <div className="flex items-center text-sm text-gray-500 mb-6">
          <Link href="/shopping" className="hover:text-emerald-600">
            Home
          </Link>
          <span className="mx-2">/</span>
          <Link href="/shopping/account" className="hover:text-emerald-600">
            My Account
          </Link>
          <span className="mx-2">/</span>
          <span className="text-emerald-600 font-medium">Wallet</span>
        </div>

        <div className="flex flex-col md:flex-row justify-between items-start gap-6 mb-8">
          <h1 className="text-2xl md:text-3xl font-bold">My Wallet</h1>
          <div className="flex gap-3">
            <Button variant="outline" className="gap-2 border-emerald-200 text-emerald-600 hover:bg-emerald-50">
              <Download className="h-4 w-4" />
              Export Transactions
            </Button>
            <Button className="gap-2 bg-emerald-600 hover:bg-emerald-700">
              <Plus className="h-4 w-4" />
              Add Funds
            </Button>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Wallet Balance and Actions */}
          <div className="lg:col-span-1 space-y-6">
            {/* Balance Card */}
            <div className="bg-gradient-to-r from-emerald-600 to-teal-500 rounded-xl shadow-md overflow-hidden text-white">
              <div className="p-6">
                <div className="flex items-center justify-between mb-6">
                  <div className="flex items-center gap-2">
                    <Wallet className="h-6 w-6" />
                    <h2 className="text-lg font-medium">Wallet Balance</h2>
                  </div>
                  <div className="h-8 w-8 rounded-full bg-white/20 flex items-center justify-center">
                    <span className="text-xs font-bold">{walletData.currency}</span>
                  </div>
                </div>
                <div className="mb-6">
                  <p className="text-sm opacity-80">Available Balance</p>
                  <p className="text-3xl font-bold">${walletData.balance.toFixed(2)}</p>
                </div>
                <div className="flex gap-3">
                  <Button className="flex-1 bg-white text-emerald-600 hover:bg-gray-100">
                    <Plus className="mr-2 h-4 w-4" />
                    Add Funds
                  </Button>
                  <Button variant="outline" className="flex-1 border-white text-white hover:bg-white/10">
                    Withdraw
                  </Button>
                </div>
              </div>
            </div>

            {/* Payment Methods */}
            <div className="bg-white rounded-lg shadow-sm overflow-hidden">
              <div className="p-6">
                <div className="flex items-center justify-between mb-4">
                  <h2 className="text-lg font-medium">Payment Methods</h2>
                  <Button variant="ghost" size="sm" className="text-emerald-600 hover:text-emerald-700 p-0 h-auto">
                    Add New
                  </Button>
                </div>
                <div className="space-y-4">
                  {walletData.cards.map((card) => (
                    <div key={card.id} className="border rounded-lg p-4">
                      <div className="flex justify-between items-start">
                        <div className="flex items-center gap-3">
                          <div className="h-10 w-10 bg-gray-100 rounded flex items-center justify-center">
                            <CreditCard className="h-5 w-5 text-gray-600" />
                          </div>
                          <div>
                            <p className="font-medium">
                              {card.type} •••• {card.last4}
                            </p>
                            <p className="text-sm text-gray-500">Expires {card.expiry}</p>
                          </div>
                        </div>
                        {card.isDefault && (
                          <span className="text-xs bg-emerald-100 text-emerald-600 px-2 py-1 rounded-full">
                            Default
                          </span>
                        )}
                      </div>
                      <div className="mt-3 flex justify-end gap-2">
                        <Button variant="ghost" size="sm" className="text-xs h-8">
                          Edit
                        </Button>
                        {!card.isDefault && (
                          <Button variant="ghost" size="sm" className="text-xs h-8">
                            Set as Default
                          </Button>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>

            {/* Quick Actions */}
            <div className="bg-white rounded-lg shadow-sm overflow-hidden">
              <div className="p-6">
                <h2 className="text-lg font-medium mb-4">Quick Actions</h2>
                <div className="grid grid-cols-2 gap-3">
                  <Button variant="outline" className="justify-start gap-2 h-auto py-3">
                    <ArrowUpRight className="h-4 w-4 text-emerald-600" />
                    <div className="text-left">
                      <p className="font-medium">Send Money</p>
                      <p className="text-xs text-gray-500">To other users</p>
                    </div>
                  </Button>
                  <Button variant="outline" className="justify-start gap-2 h-auto py-3">
                    <ArrowDownLeft className="h-4 w-4 text-emerald-600" />
                    <div className="text-left">
                      <p className="font-medium">Request Money</p>
                      <p className="text-xs text-gray-500">From other users</p>
                    </div>
                  </Button>
                  <Button variant="outline" className="justify-start gap-2 h-auto py-3">
                    <CreditCard className="h-4 w-4 text-emerald-600" />
                    <div className="text-left">
                      <p className="font-medium">Add Card</p>
                      <p className="text-xs text-gray-500">Link a new card</p>
                    </div>
                  </Button>
                  <Button variant="outline" className="justify-start gap-2 h-auto py-3">
                    <Download className="h-4 w-4 text-emerald-600" />
                    <div className="text-left">
                      <p className="font-medium">Statements</p>
                      <p className="text-xs text-gray-500">Download history</p>
                    </div>
                  </Button>
                </div>
              </div>
            </div>
          </div>

          {/* Transaction History */}
          <div className="lg:col-span-2">
            <div className="bg-white rounded-lg shadow-sm overflow-hidden">
              <div className="p-6">
                <h2 className="text-lg font-medium mb-4">Transaction History</h2>

                <div className="flex flex-col sm:flex-row gap-4 mb-6">
                  <div className="relative flex-1">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                    <Input placeholder="Search transactions..." className="pl-10" />
                  </div>
                  <Button variant="outline" size="icon" className="h-10 w-10">
                    <Filter className="h-4 w-4" />
                  </Button>
                  <select className="h-10 rounded-md border border-input bg-background px-3 py-2 text-sm">
                    <option>All Transactions</option>
                    <option>Deposits</option>
                    <option>Withdrawals</option>
                    <option>Pending</option>
                  </select>
                </div>

                <Tabs defaultValue="all">
                  <TabsList className="grid w-full grid-cols-4">
                    <TabsTrigger value="all">All</TabsTrigger>
                    <TabsTrigger value="deposits">Deposits</TabsTrigger>
                    <TabsTrigger value="withdrawals">Withdrawals</TabsTrigger>
                    <TabsTrigger value="pending">Pending</TabsTrigger>
                  </TabsList>
                  <TabsContent value="all" className="mt-4">
                    <div className="space-y-4">
                      {walletData.transactions.map((transaction) => (
                        <div key={transaction.id} className="border rounded-lg p-4">
                          <div className="flex items-start gap-3">
                            <div
                              className={`h-10 w-10 rounded-full flex items-center justify-center ${
                                transaction.type === "deposit" ? "bg-green-100" : "bg-amber-100"
                              }`}
                            >
                              {transaction.type === "deposit" ? (
                                <ArrowDownLeft
                                  className={`h-5 w-5 ${
                                    transaction.status === "pending" ? "text-gray-400" : "text-green-600"
                                  }`}
                                />
                              ) : (
                                <ArrowUpRight
                                  className={`h-5 w-5 ${
                                    transaction.status === "pending" ? "text-gray-400" : "text-amber-600"
                                  }`}
                                />
                              )}
                            </div>
                            <div className="flex-1">
                              <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-1">
                                <div>
                                  <p className="font-medium">{transaction.description}</p>
                                  <div className="flex items-center gap-2 text-sm text-gray-500">
                                    <Clock className="h-3 w-3" />
                                    <span>{transaction.date}</span>
                                    {transaction.status === "pending" && (
                                      <span className="flex items-center gap-1 text-amber-600">
                                        <Clock className="h-3 w-3" />
                                        Pending
                                      </span>
                                    )}
                                  </div>
                                </div>
                                <div className="flex items-center gap-2">
                                  <span
                                    className={`font-medium ${
                                      transaction.type === "deposit" ? "text-green-600" : "text-amber-600"
                                    }`}
                                  >
                                    {transaction.type === "deposit" ? "+" : "-"}${transaction.amount.toFixed(2)}
                                  </span>
                                  {transaction.status === "completed" ? (
                                    <CheckCircle2 className="h-4 w-4 text-green-600" />
                                  ) : (
                                    <AlertCircle className="h-4 w-4 text-amber-600" />
                                  )}
                                </div>
                              </div>
                              {transaction.source && (
                                <p className="text-xs text-gray-500 mt-1">Source: {transaction.source}</p>
                              )}
                              {transaction.orderId && (
                                <p className="text-xs text-gray-500 mt-1">
                                  Order:{" "}
                                  <Link
                                    href={`/shopping/account/orders/${transaction.orderId}`}
                                    className="text-emerald-600 hover:underline"
                                  >
                                    {transaction.orderId}
                                  </Link>
                                </p>
                              )}
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </TabsContent>
                  <TabsContent value="deposits" className="mt-4">
                    <div className="space-y-4">
                      {walletData.transactions
                        .filter((t) => t.type === "deposit")
                        .map((transaction) => (
                          <div key={transaction.id} className="border rounded-lg p-4">
                            <div className="flex items-start gap-3">
                              <div
                                className={`h-10 w-10 rounded-full flex items-center justify-center ${
                                  transaction.status === "pending" ? "bg-gray-100" : "bg-green-100"
                                }`}
                              >
                                <ArrowDownLeft
                                  className={`h-5 w-5 ${
                                    transaction.status === "pending" ? "text-gray-400" : "text-green-600"
                                  }`}
                                />
                              </div>
                              <div className="flex-1">
                                <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-1">
                                  <div>
                                    <p className="font-medium">{transaction.description}</p>
                                    <div className="flex items-center gap-2 text-sm text-gray-500">
                                      <Clock className="h-3 w-3" />
                                      <span>{transaction.date}</span>
                                      {transaction.status === "pending" && (
                                        <span className="flex items-center gap-1 text-amber-600">
                                          <Clock className="h-3 w-3" />
                                          Pending
                                        </span>
                                      )}
                                    </div>
                                  </div>
                                  <div className="flex items-center gap-2">
                                    <span className="font-medium text-green-600">
                                      +${transaction.amount.toFixed(2)}
                                    </span>
                                    {transaction.status === "completed" ? (
                                      <CheckCircle2 className="h-4 w-4 text-green-600" />
                                    ) : (
                                      <AlertCircle className="h-4 w-4 text-amber-600" />
                                    )}
                                  </div>
                                </div>
                                {transaction.source && (
                                  <p className="text-xs text-gray-500 mt-1">Source: {transaction.source}</p>
                                )}
                                {transaction.orderId && (
                                  <p className="text-xs text-gray-500 mt-1">
                                    Order:{" "}
                                    <Link
                                      href={`/shopping/account/orders/${transaction.orderId}`}
                                      className="text-emerald-600 hover:underline"
                                    >
                                      {transaction.orderId}
                                    </Link>
                                  </p>
                                )}
                              </div>
                            </div>
                          </div>
                        ))}
                    </div>
                  </TabsContent>
                  <TabsContent value="withdrawals" className="mt-4">
                    <div className="space-y-4">
                      {walletData.transactions
                        .filter((t) => t.type === "withdrawal")
                        .map((transaction) => (
                          <div key={transaction.id} className="border rounded-lg p-4">
                            <div className="flex items-start gap-3">
                              <div className="h-10 w-10 rounded-full bg-amber-100 flex items-center justify-center">
                                <ArrowUpRight className="h-5 w-5 text-amber-600" />
                              </div>
                              <div className="flex-1">
                                <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-1">
                                  <div>
                                    <p className="font-medium">{transaction.description}</p>
                                    <div className="flex items-center gap-2 text-sm text-gray-500">
                                      <Clock className="h-3 w-3" />
                                      <span>{transaction.date}</span>
                                    </div>
                                  </div>
                                  <div className="flex items-center gap-2">
                                    <span className="font-medium text-amber-600">
                                      -${transaction.amount.toFixed(2)}
                                    </span>
                                    <CheckCircle2 className="h-4 w-4 text-green-600" />
                                  </div>
                                </div>
                                {transaction.orderId && (
                                  <p className="text-xs text-gray-500 mt-1">
                                    Order:{" "}
                                    <Link
                                      href={`/shopping/account/orders/${transaction.orderId}`}
                                      className="text-emerald-600 hover:underline"
                                    >
                                      {transaction.orderId}
                                    </Link>
                                  </p>
                                )}
                              </div>
                            </div>
                          </div>
                        ))}
                    </div>
                  </TabsContent>
                  <TabsContent value="pending" className="mt-4">
                    <div className="space-y-4">
                      {walletData.transactions
                        .filter((t) => t.status === "pending")
                        .map((transaction) => (
                          <div key={transaction.id} className="border rounded-lg p-4">
                            <div className="flex items-start gap-3">
                              <div className="h-10 w-10 rounded-full bg-gray-100 flex items-center justify-center">
                                {transaction.type === "deposit" ? (
                                  <ArrowDownLeft className="h-5 w-5 text-gray-400" />
                                ) : (
                                  <ArrowUpRight className="h-5 w-5 text-gray-400" />
                                )}
                              </div>
                              <div className="flex-1">
                                <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-1">
                                  <div>
                                    <p className="font-medium">{transaction.description}</p>
                                    <div className="flex items-center gap-2 text-sm text-gray-500">
                                      <Clock className="h-3 w-3" />
                                      <span>{transaction.date}</span>
                                      <span className="flex items-center gap-1 text-amber-600">
                                        <Clock className="h-3 w-3" />
                                        Pending
                                      </span>
                                    </div>
                                  </div>
                                  <div className="flex items-center gap-2">
                                    <span
                                      className={`font-medium ${
                                        transaction.type === "deposit" ? "text-green-600" : "text-amber-600"
                                      }`}
                                    >
                                      {transaction.type === "deposit" ? "+" : "-"}${transaction.amount.toFixed(2)}
                                    </span>
                                    <AlertCircle className="h-4 w-4 text-amber-600" />
                                  </div>
                                </div>
                                {transaction.source && (
                                  <p className="text-xs text-gray-500 mt-1">Source: {transaction.source}</p>
                                )}
                                {transaction.orderId && (
                                  <p className="text-xs text-gray-500 mt-1">
                                    Order:{" "}
                                    <Link
                                      href={`/shopping/account/orders/${transaction.orderId}`}
                                      className="text-emerald-600 hover:underline"
                                    >
                                      {transaction.orderId}
                                    </Link>
                                  </p>
                                )}
                              </div>
                            </div>
                          </div>
                        ))}
                    </div>
                  </TabsContent>
                </Tabs>

                <div className="mt-6 flex justify-center">
                  <Button variant="outline" className="border-emerald-200 text-emerald-600 hover:bg-emerald-50">
                    Load More Transactions
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </main>

      {/* Footer - Simplified for this page */}
      <footer className="bg-white border-t py-8 mt-12">
        <div className="container px-4 md:px-6">
          <div className="border-t mt-8 pt-8 flex flex-col md:flex-row justify-between items-center">
            <p className="text-sm text-gray-600">
              &copy; {new Date().getFullYear()} Kominote Shopping. All rights reserved.
            </p>
            <div className="flex items-center gap-4 mt-4 md:mt-0">
              <Link href="#" className="text-gray-600 hover:text-emerald-600">
                Terms of Service
              </Link>
              <Link href="#" className="text-gray-600 hover:text-emerald-600">
                Privacy Policy
              </Link>
              <Link href="#" className="text-gray-600 hover:text-emerald-600">
                Cookie Policy
              </Link>
            </div>
          </div>
        </div>
      </footer>

      {/* Side Navigation */}
      <FeatureNavigation currentFeature="shopping" colorScheme="emerald" />

      {/* Messenger */}
      <Messenger variant="light" />
    </div>
  )
}

