"""
URL configuration for jobs app.

Handles job listings, applications, employer features, and career-related functionality.
"""
from django.urls import path
from . import views

app_name = 'jobs'

urlpatterns = [
    # Jobs main views
    path('', views.JobsHomeView.as_view(), name='jobs-home'),
    path('jobs/', views.JobListView.as_view(), name='job-list'),
    path('jobs/<int:pk>/', views.JobDetailView.as_view(), name='job-detail'),
    path('jobs/search/', views.JobSearchView.as_view(), name='job-search'),
    
    # Job categories and filtering
    path('categories/', views.JobCategoryListView.as_view(), name='category-list'),
    path('categories/<int:pk>/', views.JobCategoryDetailView.as_view(), name='category-detail'),
    path('companies/', views.CompanyListView.as_view(), name='company-list'),
    path('companies/<int:pk>/', views.CompanyDetailView.as_view(), name='company-detail'),
    
    # Job applications
    path('jobs/<int:pk>/apply/', views.JobApplicationView.as_view(), name='job-apply'),
    path('applications/', views.ApplicationListView.as_view(), name='application-list'),
    path('applications/<int:pk>/', views.ApplicationDetailView.as_view(), name='application-detail'),
    
    # Employer features
    path('employer/dashboard/', views.EmployerDashboardView.as_view(), name='employer-dashboard'),
    path('employer/jobs/', views.EmployerJobsView.as_view(), name='employer-jobs'),
    path('employer/jobs/create/', views.JobCreateView.as_view(), name='job-create'),
    path('employer/jobs/<int:pk>/edit/', views.JobEditView.as_view(), name='job-edit'),
    path('employer/applications/', views.EmployerApplicationsView.as_view(), name='employer-applications'),
    
    # Job seeker features
    path('profile/', views.JobSeekerProfileView.as_view(), name='jobseeker-profile'),
    path('profile/edit/', views.JobSeekerProfileEditView.as_view(), name='jobseeker-profile-edit'),
    path('saved-jobs/', views.SavedJobsView.as_view(), name='saved-jobs'),
    path('jobs/<int:pk>/save/', views.SaveJobView.as_view(), name='save-job'),
]
