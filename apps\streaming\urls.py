"""
URL configuration for streaming app.

Handles video content, streaming services, playlists, and media management.
"""
from django.urls import path
from . import views

app_name = 'streaming'

urlpatterns = [
    # Streaming main views
    path('', views.StreamingHomeView.as_view(), name='streaming-home'),
    path('videos/', views.VideoListView.as_view(), name='video-list'),
    path('videos/<int:pk>/', views.VideoDetailView.as_view(), name='video-detail'),
    path('videos/<int:pk>/watch/', views.VideoWatchView.as_view(), name='video-watch'),
    
    # Categories and playlists
    path('categories/', views.CategoryListView.as_view(), name='category-list'),
    path('categories/<int:pk>/', views.CategoryDetailView.as_view(), name='category-detail'),
    path('playlists/', views.PlaylistListView.as_view(), name='playlist-list'),
    path('playlists/<int:pk>/', views.PlaylistDetailView.as_view(), name='playlist-detail'),
    
    # User interactions
    path('favorites/', views.FavoriteVideosView.as_view(), name='favorites'),
    path('history/', views.WatchHistoryView.as_view(), name='watch-history'),
]
