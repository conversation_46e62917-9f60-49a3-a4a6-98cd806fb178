"use client"

import Link from "next/link"
import { Play, ShoppingBag, Users, Calendar, Briefcase, Home } from "lucide-react"
import { cn } from "@/lib/utils"

type FeatureType = "home" | "streaming" | "shopping" | "community" | "events" | "jobs"

interface QuickAccessSectionProps {
  currentFeature: FeatureType
  colorScheme?: "blue" | "emerald" | "purple" | "amber" | "red" | "gray"
  title?: string
  description?: string
  className?: string
}

export default function QuickAccessSection({
  currentFeature,
  colorScheme = "gray",
  title = "Quick Access",
  description = "Jump to other Kominote features",
  className,
}: QuickAccessSectionProps) {
  // Color mappings for different feature pages
  const colorMappings = {
    blue: {
      bg: "bg-blue-100",
      hover: "hover:bg-blue-200",
      text: "text-blue-600",
    },
    emerald: {
      bg: "bg-emerald-100",
      hover: "hover:bg-emerald-200",
      text: "text-emerald-600",
    },
    purple: {
      bg: "bg-purple-100",
      hover: "hover:bg-purple-200",
      text: "text-purple-600",
    },
    amber: {
      bg: "bg-amber-100",
      hover: "hover:bg-amber-200",
      text: "text-amber-600",
    },
    red: {
      bg: "bg-red-100",
      hover: "hover:bg-red-200",
      text: "text-red-600",
    },
    gray: {\
      bg-gray-100: "bg-gray-100",
      hover: "hover:bg-gray-200",
      text: "text-gray-600",
    },
  }

  const colors = colorMappings[colorScheme]

  const features = [
    {
      type: "home" as FeatureType,
      name: "Home",
      href: "/",
      icon: <Home className="h-5 w-5" />,
    },
    {
      type: "streaming" as FeatureType,
      name: "Streaming",
      href: "/streaming",
      icon: <Play className="h-5 w-5" />,
    },
    {
      type: "shopping" as FeatureType,
      name: "Shopping",
      href: "/shopping",
      icon: <ShoppingBag className="h-5 w-5" />,
    },
    {
      type: "community" as FeatureType,
      name: "Community",
      href: "/community",
      icon: <Users className="h-5 w-5" />,
    },
    {
      type: "events" as FeatureType,
      name: "Events",
      href: "/events",
      icon: <Calendar className="h-5 w-5" />,
    },
    {
      type: "jobs" as FeatureType,
      name: "Jobs",
      href: "/jobs",
      icon: <Briefcase className="h-5 w-5" />,
    },
  ]

  // Filter out current feature
  const filteredFeatures = features.filter((feature) => feature.type !== currentFeature)

  return (
    <section className={cn("py-12", className)}>
      <div className="container px-4 md:px-6">
        <h2 className="text-2xl font-bold">{title}</h2>
        <p className="text-gray-500">{description}</p>
        <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-5 gap-4 mt-8">
          {filteredFeatures.map((feature) => (
            <Link
              key={feature.type}
              href={feature.href}
              className="group flex flex-col items-center p-4 rounded-lg hover:bg-gray-50 transition-colors"
            >
              <div
                className={cn(
                  "h-12 w-12 rounded-full flex items-center justify-center mb-2 group-hover:bg-opacity-80",
                  colors.bg,
                )}
              >
                <span className={colors.text}>{feature.icon}</span>
              </div>
              <span className="text-sm font-medium">{feature.name}</span>
            </Link>
          ))}
        </div>
      </div>
    </section>
  )
}

