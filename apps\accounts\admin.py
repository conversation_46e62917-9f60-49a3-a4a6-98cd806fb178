"""
Admin configuration for accounts app.
"""
from django.contrib import admin
from django.contrib.auth.admin import UserAdmin as BaseUserAdmin
from django.utils.html import format_html
from .models import User, UserProfile, UserRole, UserActivity


@admin.register(User)
class UserAdmin(BaseUserAdmin):
    """
    Custom User admin with additional fields.
    """
    list_display = [
        'username', 'email', 'get_full_name', 'island', 'is_verified',
        'is_active', 'is_staff', 'date_joined', 'last_activity'
    ]
    list_filter = [
        'is_active', 'is_staff', 'is_superuser', 'is_verified',
        'island', 'language_preference', 'date_joined'
    ]
    search_fields = ['username', 'email', 'first_name', 'last_name', 'phone_number']
    ordering = ['-date_joined']

    fieldsets = BaseUserAdmin.fieldsets + (
        ('Seychelles Information', {
            'fields': ('phone_number', 'island', 'district', 'language_preference')
        }),
        ('Profile Information', {
            'fields': ('date_of_birth', 'profile_picture', 'bio')
        }),
        ('Account Status', {
            'fields': ('is_verified', 'verification_token', 'last_activity')
        }),
    )

    add_fieldsets = BaseUserAdmin.add_fieldsets + (
        ('Additional Information', {
            'fields': ('email', 'first_name', 'last_name', 'phone_number', 'island')
        }),
    )

    readonly_fields = ['date_joined', 'last_login', 'last_activity']


@admin.register(UserProfile)
class UserProfileAdmin(admin.ModelAdmin):
    """
    User Profile admin configuration.
    """
    list_display = [
        'user', 'profile_visibility', 'email_notifications',
        'total_logins', 'total_posts', 'total_purchases', 'created_at'
    ]
    list_filter = [
        'profile_visibility', 'email_notifications', 'sms_notifications',
        'marketing_emails', 'show_online_status'
    ]
    search_fields = ['user__username', 'user__email', 'interests']
    readonly_fields = ['created_at', 'updated_at']

    fieldsets = [
        ('User', {
            'fields': ['user']
        }),
        ('Social Media', {
            'fields': ['facebook_url', 'instagram_url', 'twitter_url', 'linkedin_url']
        }),
        ('Notifications', {
            'fields': ['email_notifications', 'sms_notifications', 'marketing_emails']
        }),
        ('Privacy', {
            'fields': ['profile_visibility', 'show_online_status']
        }),
        ('Interests & Statistics', {
            'fields': ['interests', 'total_logins', 'total_posts', 'total_purchases']
        }),
        ('Timestamps', {
            'fields': ['created_at', 'updated_at'],
            'classes': ['collapse']
        }),
    ]


@admin.register(UserRole)
class UserRoleAdmin(admin.ModelAdmin):
    """
    User Role admin configuration.
    """
    list_display = ['user', 'role', 'is_active', 'granted_at', 'granted_by']
    list_filter = ['role', 'is_active', 'granted_at']
    search_fields = ['user__username', 'user__email']
    readonly_fields = ['granted_at']

    def save_model(self, request, obj, form, change):
        if not change:  # Only set granted_by for new objects
            obj.granted_by = request.user
        super().save_model(request, obj, form, change)


@admin.register(UserActivity)
class UserActivityAdmin(admin.ModelAdmin):
    """
    User Activity admin configuration.
    """
    list_display = [
        'user', 'activity_type', 'description_short', 'ip_address', 'timestamp'
    ]
    list_filter = ['activity_type', 'timestamp']
    search_fields = ['user__username', 'user__email', 'description']
    readonly_fields = ['timestamp']
    date_hierarchy = 'timestamp'

    def description_short(self, obj):
        """Return shortened description for list display."""
        if obj.description:
            return obj.description[:50] + '...' if len(obj.description) > 50 else obj.description
        return '-'
    description_short.short_description = 'Description'

    def has_add_permission(self, request):
        """Disable manual addition of activities."""
        return False

    def has_change_permission(self, request, obj=None):
        """Make activities read-only."""
        return False
