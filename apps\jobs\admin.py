"""
Admin configuration for jobs app.

Provides comprehensive admin interfaces for managing job listings, companies, applications,
and job seeker profiles for the Kominote job platform.
"""
from django.contrib import admin
from django.utils.html import format_html
from django.urls import reverse
from django.utils import timezone
from .models import (
    JobCategory, Company, JobSkill, Job, JobSeekerProfile,
    JobApplication, SavedJob, CompanyReview
)


@admin.register(JobCategory)
class JobCategoryAdmin(admin.ModelAdmin):
    """Admin interface for Job Categories."""
    list_display = ['name', 'name_fr', 'name_cr', 'color_display', 'is_active', 'sort_order', 'job_count']
    list_filter = ['is_active', 'created_at']
    search_fields = ['name', 'name_fr', 'name_cr', 'description']
    prepopulated_fields = {'slug': ('name',)}
    ordering = ['sort_order', 'name']

    fieldsets = (
        ('Basic Information', {
            'fields': ('name', 'slug', 'description', 'icon', 'color')
        }),
        ('Multi-language', {
            'fields': ('name_fr', 'name_cr'),
            'classes': ('collapse',)
        }),
        ('Settings', {
            'fields': ('is_active', 'sort_order')
        }),
    )

    def color_display(self, obj):
        """Display color as a colored box."""
        return format_html(
            '<div style="width: 20px; height: 20px; background-color: {}; border: 1px solid #ccc;"></div>',
            obj.color
        )
    color_display.short_description = 'Color'

    def job_count(self, obj):
        """Display number of jobs in this category."""
        return obj.jobs.count()
    job_count.short_description = 'Jobs'


@admin.register(Company)
class CompanyAdmin(admin.ModelAdmin):
    """Admin interface for Companies."""
    list_display = [
        'name', 'company_type', 'company_size', 'island', 'district',
        'is_verified', 'is_featured', 'is_hiring', 'total_jobs', 'average_rating'
    ]
    list_filter = [
        'company_type', 'company_size', 'island', 'is_verified', 'is_featured',
        'is_hiring', 'is_local_company', 'supports_local_talent'
    ]
    search_fields = ['name', 'description', 'email', 'headquarters']
    prepopulated_fields = {'slug': ('name',)}
    ordering = ['name']

    fieldsets = (
        ('Basic Information', {
            'fields': ('name', 'slug', 'description', 'short_description')
        }),
        ('Company Details', {
            'fields': ('company_type', 'company_size', 'founded_year')
        }),
        ('Location', {
            'fields': ('headquarters', 'island', 'district')
        }),
        ('Contact Information', {
            'fields': ('website', 'email', 'phone')
        }),
        ('Social Media', {
            'fields': ('linkedin_url', 'facebook_url', 'twitter_url', 'instagram_url'),
            'classes': ('collapse',)
        }),
        ('Media', {
            'fields': ('logo', 'cover_image')
        }),
        ('Status & Features', {
            'fields': ('is_verified', 'is_featured', 'is_hiring')
        }),
        ('Seychelles Features', {
            'fields': ('is_local_company', 'supports_local_talent'),
            'classes': ('collapse',)
        }),
        ('Statistics', {
            'fields': ('total_jobs', 'total_employees', 'average_rating', 'total_reviews'),
            'classes': ('collapse',)
        }),
        ('SEO', {
            'fields': ('meta_title', 'meta_description'),
            'classes': ('collapse',)
        }),
    )

    actions = ['mark_verified', 'mark_unverified', 'mark_featured', 'mark_unfeatured']

    def mark_verified(self, request, queryset):
        """Mark selected companies as verified."""
        queryset.update(is_verified=True)
    mark_verified.short_description = "Mark selected companies as verified"

    def mark_unverified(self, request, queryset):
        """Mark selected companies as unverified."""
        queryset.update(is_verified=False)
    mark_unverified.short_description = "Mark selected companies as unverified"

    def mark_featured(self, request, queryset):
        """Mark selected companies as featured."""
        queryset.update(is_featured=True)
    mark_featured.short_description = "Mark selected companies as featured"

    def mark_unfeatured(self, request, queryset):
        """Remove featured status from selected companies."""
        queryset.update(is_featured=False)
    mark_unfeatured.short_description = "Remove featured status"


@admin.register(JobSkill)
class JobSkillAdmin(admin.ModelAdmin):
    """Admin interface for Job Skills."""
    list_display = ['name', 'skill_type', 'is_active', 'job_count']
    list_filter = ['skill_type', 'is_active', 'created_at']
    search_fields = ['name', 'description']
    prepopulated_fields = {'slug': ('name',)}
    ordering = ['name']

    def job_count(self, obj):
        """Display number of jobs requiring this skill."""
        return obj.jobs.count()
    job_count.short_description = 'Jobs'


@admin.register(Job)
class JobAdmin(admin.ModelAdmin):
    """Admin interface for Jobs."""
    list_display = [
        'title', 'company', 'category', 'job_type', 'experience_level',
        'island', 'status', 'is_featured', 'is_urgent', 'salary_range_display',
        'total_applications', 'days_since_posted'
    ]
    list_filter = [
        'status', 'job_type', 'experience_level', 'education_level', 'category',
        'island', 'is_featured', 'is_urgent', 'is_remote', 'is_local_job',
        'supports_local_talent', 'work_permit_sponsored'
    ]
    search_fields = ['title', 'description', 'summary', 'company__name']
    prepopulated_fields = {'slug': ('title',)}
    date_hierarchy = 'created_at'
    ordering = ['-created_at']
    filter_horizontal = ['skills']

    fieldsets = (
        ('Basic Information', {
            'fields': ('title', 'slug', 'description', 'summary')
        }),
        ('Company & Category', {
            'fields': ('company', 'category')
        }),
        ('Job Details', {
            'fields': ('job_type', 'experience_level', 'education_level')
        }),
        ('Location', {
            'fields': ('location', 'island', 'district', 'is_remote')
        }),
        ('Salary & Benefits', {
            'fields': (
                'salary_min', 'salary_max', 'salary_currency', 'salary_period', 'benefits'
            )
        }),
        ('Requirements', {
            'fields': ('requirements', 'responsibilities', 'skills')
        }),
        ('Application Details', {
            'fields': (
                'application_deadline', 'application_email', 'application_url', 'how_to_apply'
            )
        }),
        ('Status & Visibility', {
            'fields': ('status', 'is_featured', 'is_urgent')
        }),
        ('Seychelles Features', {
            'fields': ('is_local_job', 'supports_local_talent', 'work_permit_sponsored'),
            'classes': ('collapse',)
        }),
        ('Statistics', {
            'fields': ('total_applications', 'total_views', 'total_saves'),
            'classes': ('collapse',)
        }),
        ('SEO', {
            'fields': ('meta_title', 'meta_description'),
            'classes': ('collapse',)
        }),
    )

    actions = ['publish_jobs', 'pause_jobs', 'mark_featured', 'mark_unfeatured']

    def publish_jobs(self, request, queryset):
        """Publish selected jobs."""
        queryset.update(status='published', published_at=timezone.now())
    publish_jobs.short_description = "Publish selected jobs"

    def pause_jobs(self, request, queryset):
        """Pause selected jobs."""
        queryset.update(status='paused')
    pause_jobs.short_description = "Pause selected jobs"

    def mark_featured(self, request, queryset):
        """Mark selected jobs as featured."""
        queryset.update(is_featured=True)
    mark_featured.short_description = "Mark selected jobs as featured"

    def mark_unfeatured(self, request, queryset):
        """Remove featured status from selected jobs."""
        queryset.update(is_featured=False)
    mark_unfeatured.short_description = "Remove featured status"

    def days_since_posted(self, obj):
        """Display days since job was posted."""
        return f"{obj.days_since_posted} days"
    days_since_posted.short_description = 'Posted'


@admin.register(JobSeekerProfile)
class JobSeekerProfileAdmin(admin.ModelAdmin):
    """Admin interface for Job Seeker Profiles."""
    list_display = [
        'user', 'headline', 'current_position', 'current_company',
        'availability', 'experience_years', 'profile_visibility',
        'applications_sent', 'profile_views'
    ]
    list_filter = [
        'availability', 'profile_visibility', 'willing_to_relocate',
        'open_to_remote', 'allow_contact', 'created_at'
    ]
    search_fields = [
        'user__username', 'user__email', 'headline', 'summary',
        'current_position', 'current_company'
    ]
    ordering = ['-created_at']
    filter_horizontal = ['skills']

    fieldsets = (
        ('User Information', {
            'fields': ('user',)
        }),
        ('Professional Information', {
            'fields': ('headline', 'summary', 'experience_years')
        }),
        ('Current Status', {
            'fields': ('current_position', 'current_company', 'availability')
        }),
        ('Preferences', {
            'fields': (
                'desired_job_types', 'desired_salary_min', 'desired_salary_max',
                'preferred_locations', 'willing_to_relocate', 'open_to_remote'
            )
        }),
        ('Skills & Education', {
            'fields': ('skills', 'education', 'certifications')
        }),
        ('Documents', {
            'fields': ('resume', 'cover_letter_template', 'portfolio_url')
        }),
        ('Privacy Settings', {
            'fields': ('profile_visibility', 'allow_contact')
        }),
        ('Statistics', {
            'fields': ('profile_views', 'applications_sent'),
            'classes': ('collapse',)
        }),
    )


@admin.register(JobApplication)
class JobApplicationAdmin(admin.ModelAdmin):
    """Admin interface for Job Applications."""
    list_display = [
        'applicant', 'job', 'status', 'expected_salary', 'available_start_date',
        'applied_at', 'reviewed_by', 'days_since_applied'
    ]
    list_filter = ['status', 'applied_at', 'reviewed_at']
    search_fields = [
        'applicant__username', 'applicant__email', 'job__title',
        'job__company__name', 'cover_letter'
    ]
    date_hierarchy = 'applied_at'
    ordering = ['-applied_at']

    fieldsets = (
        ('Application Details', {
            'fields': ('job', 'applicant', 'status', 'applied_at')
        }),
        ('Application Content', {
            'fields': ('cover_letter', 'resume', 'additional_documents')
        }),
        ('Application Details', {
            'fields': ('expected_salary', 'available_start_date', 'additional_notes')
        }),
        ('Employer Actions', {
            'fields': ('employer_notes', 'reviewed_by', 'reviewed_at')
        }),
        ('Interview Details', {
            'fields': (
                'interview_scheduled_date', 'interview_location', 'interview_notes'
            ),
            'classes': ('collapse',)
        }),
    )

    actions = ['mark_under_review', 'mark_shortlisted', 'mark_rejected']

    def mark_under_review(self, request, queryset):
        """Mark selected applications as under review."""
        queryset.update(status='under_review', reviewed_by=request.user, reviewed_at=timezone.now())
    mark_under_review.short_description = "Mark as under review"

    def mark_shortlisted(self, request, queryset):
        """Mark selected applications as shortlisted."""
        queryset.update(status='shortlisted', reviewed_by=request.user, reviewed_at=timezone.now())
    mark_shortlisted.short_description = "Mark as shortlisted"

    def mark_rejected(self, request, queryset):
        """Mark selected applications as rejected."""
        queryset.update(status='rejected', reviewed_by=request.user, reviewed_at=timezone.now())
    mark_rejected.short_description = "Mark as rejected"

    def days_since_applied(self, obj):
        """Display days since application was submitted."""
        return f"{obj.days_since_applied} days"
    days_since_applied.short_description = 'Applied'


@admin.register(SavedJob)
class SavedJobAdmin(admin.ModelAdmin):
    """Admin interface for Saved Jobs."""
    list_display = ['user', 'job', 'saved_at', 'has_notes']
    list_filter = ['saved_at']
    search_fields = ['user__username', 'user__email', 'job__title', 'notes']
    ordering = ['-saved_at']

    def has_notes(self, obj):
        """Display whether the saved job has notes."""
        return bool(obj.notes)
    has_notes.boolean = True
    has_notes.short_description = 'Has Notes'


@admin.register(CompanyReview)
class CompanyReviewAdmin(admin.ModelAdmin):
    """Admin interface for Company Reviews."""
    list_display = [
        'reviewer', 'company', 'overall_rating', 'employment_status',
        'job_title', 'is_verified', 'is_approved', 'would_recommend',
        'helpful_votes', 'created_at'
    ]
    list_filter = [
        'overall_rating', 'employment_status', 'is_verified', 'is_approved',
        'would_recommend', 'created_at'
    ]
    search_fields = [
        'reviewer__username', 'company__name', 'title', 'pros', 'cons',
        'job_title'
    ]
    ordering = ['-created_at']

    fieldsets = (
        ('Review Details', {
            'fields': ('company', 'reviewer', 'overall_rating', 'title')
        }),
        ('Review Content', {
            'fields': ('pros', 'cons', 'advice_to_management')
        }),
        ('Category Ratings', {
            'fields': (
                'work_life_balance_rating', 'salary_benefits_rating',
                'career_opportunities_rating', 'management_rating', 'culture_values_rating'
            ),
            'classes': ('collapse',)
        }),
        ('Employment Details', {
            'fields': ('job_title', 'employment_status', 'employment_duration')
        }),
        ('Verification & Moderation', {
            'fields': ('is_verified', 'is_approved', 'moderated_by', 'moderation_notes')
        }),
        ('Engagement', {
            'fields': ('helpful_votes', 'would_recommend')
        }),
    )

    actions = ['approve_reviews', 'disapprove_reviews', 'mark_verified']

    def approve_reviews(self, request, queryset):
        """Approve selected reviews."""
        queryset.update(is_approved=True, moderated_by=request.user)
    approve_reviews.short_description = "Approve selected reviews"

    def disapprove_reviews(self, request, queryset):
        """Disapprove selected reviews."""
        queryset.update(is_approved=False, moderated_by=request.user)
    disapprove_reviews.short_description = "Disapprove selected reviews"

    def mark_verified(self, request, queryset):
        """Mark selected reviews as verified."""
        queryset.update(is_verified=True)
    mark_verified.short_description = "Mark selected reviews as verified"
