"""
URL configuration for kominote project.

Kominote - Seychelles' All-in-One Digital Hub
Main URL routing for the entire platform including:
- Admin interface
- API endpoints for mobile apps
- Web frontend routes
- Static/Media file serving
"""
from django.contrib import admin
from django.urls import path, include
from django.conf import settings
from django.conf.urls.static import static
from django.http import JsonResponse
from drf_spectacular.views import SpectacularAPIView, SpectacularSwaggerView, SpectacularRedocView
from rest_framework_simplejwt.views import (
    TokenObtainPairView,
    TokenRefreshView,
    TokenVerifyView,
)

def api_root(request):
    """API root endpoint with basic information"""
    return JsonResponse({
        'message': 'Welcome to Kominote API',
        'description': 'Seychelles All-in-One Digital Hub',
        'version': '1.0.0',
        'features': [
            'Streaming',
            'Shopping',
            'Community',
            'Events',
            'Jobs'
        ],
        'documentation': {
            'swagger': '/api/docs/',
            'redoc': '/api/redoc/',
            'schema': '/api/schema/'
        }
    })

urlpatterns = [
    # Admin
    path('admin/', admin.site.urls),

    # API Root
    path('api/', api_root, name='api-root'),

    # API Documentation
    path('api/schema/', SpectacularAPIView.as_view(), name='schema'),
    path('api/docs/', SpectacularSwaggerView.as_view(url_name='schema'), name='swagger-ui'),
    path('api/redoc/', SpectacularRedocView.as_view(url_name='schema'), name='redoc'),

    # JWT Authentication endpoints
    path('api/auth/token/', TokenObtainPairView.as_view(), name='token_obtain_pair'),
    path('api/auth/token/refresh/', TokenRefreshView.as_view(), name='token_refresh'),
    path('api/auth/token/verify/', TokenVerifyView.as_view(), name='token_verify'),

    # Kominote App URLs (Phase 2 - Created)
    path('', include('apps.landing.urls')),  # Landing page as root
    path('api/', include('apps.api.urls')),  # Centralized API endpoints
    path('accounts/', include('apps.accounts.urls')),
    path('streaming/', include('apps.streaming.urls')),
    path('shopping/', include('apps.shopping.urls')),
    path('community/', include('apps.community.urls')),
    path('events/', include('apps.events.urls')),
    path('jobs/', include('apps.jobs.urls')),
]

# Serve static and media files in development
if settings.DEBUG:
    urlpatterns += static(settings.STATIC_URL, document_root=settings.STATIC_ROOT)
    urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)

    # Debug toolbar
    import debug_toolbar
    urlpatterns = [
        path('__debug__/', include(debug_toolbar.urls)),
    ] + urlpatterns
