# Generated by Django 4.2.21 on 2025-05-30 10:22

from django.conf import settings
import django.core.validators
from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='ContentTag',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(help_text='Tag name', max_length=50, unique=True)),
                ('slug', models.SlugField(help_text='URL-friendly tag name', unique=True)),
                ('description', models.TextField(blank=True, help_text='Tag description')),
                ('is_local_tag', models.BooleanField(default=False, help_text='Whether this tag relates to Seychelles content')),
                ('usage_count', models.PositiveIntegerField(default=0, help_text='Number of times this tag is used')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
            ],
            options={
                'verbose_name': 'Content Tag',
                'verbose_name_plural': 'Content Tags',
                'db_table': 'streaming_contenttag',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='Playlist',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(help_text='Playlist name', max_length=100)),
                ('description', models.TextField(blank=True, help_text='Playlist description')),
                ('privacy', models.CharField(choices=[('public', 'Public'), ('unlisted', 'Unlisted'), ('private', 'Private')], default='private', help_text='Playlist privacy setting', max_length=10)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'Playlist',
                'verbose_name_plural': 'Playlists',
                'db_table': 'streaming_playlist',
                'ordering': ['-updated_at'],
            },
        ),
        migrations.CreateModel(
            name='VideoCategory',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(help_text='Category name', max_length=100, unique=True)),
                ('slug', models.SlugField(help_text='URL-friendly category name', max_length=100, unique=True)),
                ('description', models.TextField(blank=True, help_text='Category description')),
                ('icon', models.ImageField(blank=True, help_text='Category icon', null=True, upload_to='streaming/categories/icons/')),
                ('banner_image', models.ImageField(blank=True, help_text='Category banner image', null=True, upload_to='streaming/categories/banners/')),
                ('is_local_content', models.BooleanField(default=False, help_text='Whether this category focuses on Seychelles content')),
                ('is_active', models.BooleanField(default=True, help_text='Whether this category is active')),
                ('is_featured', models.BooleanField(default=False, help_text='Show in featured categories')),
                ('display_order', models.PositiveIntegerField(default=1, help_text='Display order')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'Video Category',
                'verbose_name_plural': 'Video Categories',
                'db_table': 'streaming_videocategory',
                'ordering': ['display_order', 'name'],
            },
        ),
        migrations.CreateModel(
            name='VideoContent',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(help_text='Content title', max_length=200)),
                ('slug', models.SlugField(help_text='URL-friendly title', max_length=200, unique=True)),
                ('description', models.TextField(help_text='Content description')),
                ('short_description', models.CharField(blank=True, help_text='Short description for cards and previews', max_length=300)),
                ('content_type', models.CharField(choices=[('movie', 'Movie'), ('series', 'Series'), ('documentary', 'Documentary'), ('short', 'Short Film'), ('live', 'Live Stream')], help_text='Type of content', max_length=20)),
                ('video_file', models.FileField(blank=True, help_text='Main video file', null=True, upload_to='streaming/videos/', validators=[django.core.validators.FileExtensionValidator(allowed_extensions=['mp4', 'webm', 'mov'])])),
                ('trailer_file', models.FileField(blank=True, help_text='Trailer video file', null=True, upload_to='streaming/trailers/', validators=[django.core.validators.FileExtensionValidator(allowed_extensions=['mp4', 'webm', 'mov'])])),
                ('thumbnail', models.ImageField(blank=True, help_text='Content thumbnail', null=True, upload_to='streaming/thumbnails/')),
                ('poster', models.ImageField(blank=True, help_text='Content poster', null=True, upload_to='streaming/posters/')),
                ('banner', models.ImageField(blank=True, help_text='Content banner for hero sections', null=True, upload_to='streaming/banners/')),
                ('duration_minutes', models.PositiveIntegerField(blank=True, help_text='Duration in minutes', null=True)),
                ('quality', models.CharField(choices=[('240p', '240p'), ('360p', '360p'), ('480p', '480p'), ('720p', '720p (HD)'), ('1080p', '1080p (Full HD)'), ('1440p', '1440p (2K)'), ('2160p', '2160p (4K)')], default='720p', help_text='Video quality', max_length=10)),
                ('file_size_mb', models.PositiveIntegerField(blank=True, help_text='File size in MB', null=True)),
                ('language', models.CharField(choices=[('en', 'English'), ('fr', 'French'), ('cr', 'Creole'), ('multi', 'Multiple Languages')], default='en', help_text='Primary language', max_length=10)),
                ('has_subtitles', models.BooleanField(default=False, help_text='Whether subtitles are available')),
                ('subtitle_languages', models.CharField(blank=True, help_text='Available subtitle languages (comma-separated)', max_length=100)),
                ('director', models.CharField(blank=True, help_text='Director name(s)', max_length=200)),
                ('producer', models.CharField(blank=True, help_text='Producer name(s)', max_length=200)),
                ('cast', models.TextField(blank=True, help_text='Cast members (comma-separated)')),
                ('production_year', models.PositiveIntegerField(blank=True, help_text='Year of production', null=True)),
                ('is_local_production', models.BooleanField(default=False, help_text='Whether this is a Seychelles production')),
                ('filming_locations', models.CharField(blank=True, help_text='Filming locations in Seychelles', max_length=300)),
                ('local_cast', models.TextField(blank=True, help_text='Local Seychellois cast members')),
                ('is_published', models.BooleanField(default=False, help_text='Whether content is published and visible')),
                ('is_featured', models.BooleanField(default=False, help_text='Show in featured content')),
                ('is_premium', models.BooleanField(default=False, help_text='Whether this is premium content')),
                ('publish_date', models.DateTimeField(default=django.utils.timezone.now, help_text='When content becomes available')),
                ('view_count', models.PositiveIntegerField(default=0, help_text='Total view count')),
                ('like_count', models.PositiveIntegerField(default=0, help_text='Total likes')),
                ('average_rating', models.DecimalField(decimal_places=2, default=0.0, help_text='Average user rating', max_digits=3)),
                ('rating_count', models.PositiveIntegerField(default=0, help_text='Number of ratings')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('category', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='content', to='streaming.videocategory')),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_content', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Video Content',
                'verbose_name_plural': 'Video Content',
                'db_table': 'streaming_videocontent',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='UserRating',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('rating', models.PositiveIntegerField(help_text='Rating from 1 to 5 stars', validators=[django.core.validators.MinValueValidator(1), django.core.validators.MaxValueValidator(5)])),
                ('review', models.TextField(blank=True, help_text='Optional written review')),
                ('is_approved', models.BooleanField(default=True, help_text='Whether the review is approved for display')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('content', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='user_ratings', to='streaming.videocontent')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='content_ratings', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'User Rating',
                'verbose_name_plural': 'User Ratings',
                'db_table': 'streaming_userrating',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='StreamingProfile',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('preferred_quality', models.CharField(choices=[('auto', 'Auto (Based on connection)'), ('240p', '240p (Data Saver)'), ('360p', '360p'), ('480p', '480p'), ('720p', '720p (HD)'), ('1080p', '1080p (Full HD)')], default='auto', help_text='Preferred video quality', max_length=10)),
                ('auto_play_next', models.BooleanField(default=True, help_text='Automatically play next episode')),
                ('skip_intro', models.BooleanField(default=False, help_text='Automatically skip intro sequences')),
                ('preferred_language', models.CharField(choices=[('en', 'English'), ('fr', 'French'), ('cr', 'Creole'), ('multi', 'Multiple Languages')], default='en', help_text='Preferred content language', max_length=10)),
                ('subtitle_language', models.CharField(choices=[('en', 'English'), ('fr', 'French'), ('cr', 'Creole'), ('multi', 'Multiple Languages')], default='en', help_text='Preferred subtitle language', max_length=10)),
                ('always_show_subtitles', models.BooleanField(default=False, help_text='Always show subtitles when available')),
                ('prefer_local_content', models.BooleanField(default=True, help_text='Prefer Seychelles local content in recommendations')),
                ('content_rating_limit', models.CharField(choices=[('G', 'General Audiences'), ('PG', 'Parental Guidance'), ('PG13', 'PG-13'), ('R', 'Restricted'), ('NC17', 'Adults Only')], default='R', help_text='Maximum content rating to show', max_length=10)),
                ('limit_mobile_quality', models.BooleanField(default=True, help_text='Limit video quality on mobile data')),
                ('mobile_quality_limit', models.CharField(choices=[('auto', 'Auto (Based on connection)'), ('240p', '240p (Data Saver)'), ('360p', '360p'), ('480p', '480p')], default='480p', help_text='Maximum quality on mobile data', max_length=10)),
                ('notify_new_episodes', models.BooleanField(default=True, help_text='Notify when new episodes are available')),
                ('notify_new_local_content', models.BooleanField(default=True, help_text='Notify about new Seychelles content')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('favorite_categories', models.ManyToManyField(blank=True, help_text="User's favorite content categories", related_name='favorited_by_users', to='streaming.videocategory')),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='streaming_profile', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Streaming Profile',
                'verbose_name_plural': 'Streaming Profiles',
                'db_table': 'streaming_streamingprofile',
            },
        ),
        migrations.CreateModel(
            name='Season',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('season_number', models.PositiveIntegerField(help_text='Season number')),
                ('title', models.CharField(blank=True, help_text='Season title (optional)', max_length=200)),
                ('description', models.TextField(blank=True, help_text='Season description')),
                ('poster', models.ImageField(blank=True, help_text='Season poster', null=True, upload_to='streaming/seasons/')),
                ('release_date', models.DateField(blank=True, help_text='Season release date', null=True)),
                ('is_complete', models.BooleanField(default=False, help_text='Whether all episodes are released')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('series', models.ForeignKey(limit_choices_to={'content_type': 'series'}, on_delete=django.db.models.deletion.CASCADE, related_name='seasons', to='streaming.videocontent')),
            ],
            options={
                'verbose_name': 'Season',
                'verbose_name_plural': 'Seasons',
                'db_table': 'streaming_season',
                'ordering': ['series', 'season_number'],
            },
        ),
        migrations.CreateModel(
            name='PlaylistItem',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('order', models.PositiveIntegerField(default=1, help_text='Order within the playlist')),
                ('added_at', models.DateTimeField(auto_now_add=True)),
                ('content', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='playlist_items', to='streaming.videocontent')),
                ('playlist', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='playlist_items', to='streaming.playlist')),
            ],
            options={
                'verbose_name': 'Playlist Item',
                'verbose_name_plural': 'Playlist Items',
                'db_table': 'streaming_playlistitem',
                'ordering': ['playlist', 'order'],
            },
        ),
        migrations.AddField(
            model_name='playlist',
            name='content',
            field=models.ManyToManyField(related_name='playlists', through='streaming.PlaylistItem', to='streaming.videocontent'),
        ),
        migrations.AddField(
            model_name='playlist',
            name='user',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='playlists', to=settings.AUTH_USER_MODEL),
        ),
        migrations.CreateModel(
            name='Episode',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('episode_number', models.PositiveIntegerField(help_text='Episode number within the season')),
                ('title', models.CharField(help_text='Episode title', max_length=200)),
                ('slug', models.SlugField(help_text='URL-friendly episode identifier', max_length=250)),
                ('description', models.TextField(blank=True, help_text='Episode description')),
                ('video_file', models.FileField(help_text='Episode video file', upload_to='streaming/episodes/', validators=[django.core.validators.FileExtensionValidator(allowed_extensions=['mp4', 'webm', 'mov'])])),
                ('thumbnail', models.ImageField(blank=True, help_text='Episode thumbnail', null=True, upload_to='streaming/episode_thumbnails/')),
                ('duration_minutes', models.PositiveIntegerField(help_text='Episode duration in minutes')),
                ('quality', models.CharField(choices=[('240p', '240p'), ('360p', '360p'), ('480p', '480p'), ('720p', '720p (HD)'), ('1080p', '1080p (Full HD)'), ('1440p', '1440p (2K)'), ('2160p', '2160p (4K)')], default='720p', help_text='Video quality', max_length=10)),
                ('air_date', models.DateTimeField(help_text='When the episode airs/becomes available')),
                ('is_published', models.BooleanField(default=False, help_text='Whether episode is published')),
                ('view_count', models.PositiveIntegerField(default=0, help_text='Episode view count')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('season', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='episodes', to='streaming.season')),
            ],
            options={
                'verbose_name': 'Episode',
                'verbose_name_plural': 'Episodes',
                'db_table': 'streaming_episode',
                'ordering': ['season', 'episode_number'],
            },
        ),
        migrations.CreateModel(
            name='ContentTagging',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('added_at', models.DateTimeField(auto_now_add=True)),
                ('added_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='added_tags', to=settings.AUTH_USER_MODEL)),
                ('content', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='content_tags', to='streaming.videocontent')),
                ('tag', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='tagged_content', to='streaming.contenttag')),
            ],
            options={
                'verbose_name': 'Content Tagging',
                'verbose_name_plural': 'Content Tagging',
                'db_table': 'streaming_contenttagging',
            },
        ),
        migrations.CreateModel(
            name='WatchHistory',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('watch_time_seconds', models.PositiveIntegerField(default=0, help_text='Time watched in seconds')),
                ('total_duration_seconds', models.PositiveIntegerField(help_text='Total content duration in seconds')),
                ('progress_percentage', models.DecimalField(decimal_places=2, default=0.0, help_text='Watch progress as percentage', max_digits=5)),
                ('is_completed', models.BooleanField(default=False, help_text='Whether content was watched to completion')),
                ('device_type', models.CharField(blank=True, help_text='Device used for watching', max_length=50)),
                ('ip_address', models.GenericIPAddressField(blank=True, null=True)),
                ('started_at', models.DateTimeField(auto_now_add=True)),
                ('last_watched_at', models.DateTimeField(auto_now=True)),
                ('content', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='watch_history', to='streaming.videocontent')),
                ('episode', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='watch_history', to='streaming.episode')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='watch_history', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Watch History',
                'verbose_name_plural': 'Watch History',
                'db_table': 'streaming_watchhistory',
                'ordering': ['-last_watched_at'],
                'indexes': [models.Index(fields=['user', '-last_watched_at'], name='streaming_w_user_id_d11903_idx'), models.Index(fields=['content', '-last_watched_at'], name='streaming_w_content_bf2480_idx')],
            },
        ),
        migrations.AddIndex(
            model_name='videocontent',
            index=models.Index(fields=['content_type', '-publish_date'], name='streaming_v_content_4e8d72_idx'),
        ),
        migrations.AddIndex(
            model_name='videocontent',
            index=models.Index(fields=['category', '-view_count'], name='streaming_v_categor_33c50d_idx'),
        ),
        migrations.AddIndex(
            model_name='videocontent',
            index=models.Index(fields=['is_featured', '-publish_date'], name='streaming_v_is_feat_0c9560_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='userrating',
            unique_together={('user', 'content')},
        ),
        migrations.AlterUniqueTogether(
            name='season',
            unique_together={('series', 'season_number')},
        ),
        migrations.AlterUniqueTogether(
            name='playlistitem',
            unique_together={('playlist', 'content')},
        ),
        migrations.AddIndex(
            model_name='episode',
            index=models.Index(fields=['season', 'episode_number'], name='streaming_e_season__ad7aea_idx'),
        ),
        migrations.AddIndex(
            model_name='episode',
            index=models.Index(fields=['air_date'], name='streaming_e_air_dat_62a15d_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='episode',
            unique_together={('season', 'episode_number')},
        ),
        migrations.AlterUniqueTogether(
            name='contenttagging',
            unique_together={('content', 'tag')},
        ),
    ]
