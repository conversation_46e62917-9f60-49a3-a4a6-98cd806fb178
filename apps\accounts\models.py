"""
Models for accounts app.

Handles user management, authentication, and user profiles for the Kominote platform.
"""
from django.contrib.auth.models import AbstractUser
from django.db import models
from django.core.validators import RegexValidator
from django.utils import timezone


class User(AbstractUser):
    """
    Custom User model for Kominote platform.
    Extends Django's AbstractUser with Seychelles-specific fields.
    """

    # Contact Information
    phone_validator = RegexValidator(
        regex=r'^\+248\d{7}$',
        message="Phone number must be in format: +248XXXXXXX (Seychelles format)"
    )
    phone_number = models.CharField(
        max_length=12,
        validators=[phone_validator],
        blank=True,
        null=True,
        help_text="Seychelles phone number format: +248XXXXXXX"
    )

    # Profile Information
    date_of_birth = models.DateField(null=True, blank=True)
    profile_picture = models.ImageField(
        upload_to='profiles/',
        null=True,
        blank=True,
        help_text="Profile picture for user avatar"
    )
    bio = models.TextField(
        max_length=500,
        blank=True,
        help_text="Short bio or description"
    )

    # Location (Seychelles Islands)
    ISLAND_CHOICES = [
        ('mahe', 'Mahé'),
        ('praslin', 'Praslin'),
        ('la_digue', 'La Digue'),
        ('silhouette', 'Silhouette'),
        ('fregate', 'Fregate'),
        ('bird', 'Bird Island'),
        ('denis', 'Denis Island'),
        ('other', 'Other'),
    ]
    island = models.CharField(
        max_length=20,
        choices=ISLAND_CHOICES,
        blank=True,
        help_text="Which island in Seychelles"
    )
    district = models.CharField(
        max_length=100,
        blank=True,
        help_text="District or area"
    )

    # User Preferences
    language_preference = models.CharField(
        max_length=10,
        choices=[
            ('en', 'English'),
            ('fr', 'French'),
            ('cr', 'Creole'),
        ],
        default='en',
        help_text="Preferred language for the platform"
    )

    # Account Status
    is_verified = models.BooleanField(
        default=False,
        help_text="Whether the user's email/phone is verified"
    )
    verification_token = models.CharField(
        max_length=100,
        blank=True,
        null=True,
        help_text="Token for email/phone verification"
    )

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    last_activity = models.DateTimeField(default=timezone.now)

    class Meta:
        db_table = 'accounts_user'
        verbose_name = 'User'
        verbose_name_plural = 'Users'
        ordering = ['-date_joined']

    def __str__(self):
        return f"{self.username} ({self.get_full_name() or self.email})"

    def get_full_name(self):
        """Return the first_name plus the last_name, with a space in between."""
        full_name = f"{self.first_name} {self.last_name}".strip()
        return full_name or self.username

    def get_display_name(self):
        """Return the best available display name for the user."""
        return self.get_full_name() or self.username

    @property
    def age(self):
        """Calculate user's age from date of birth."""
        if self.date_of_birth:
            today = timezone.now().date()
            return today.year - self.date_of_birth.year - (
                (today.month, today.day) < (self.date_of_birth.month, self.date_of_birth.day)
            )
        return None


class UserProfile(models.Model):
    """
    Extended user profile for additional user information and preferences.
    """
    user = models.OneToOneField(
        User,
        on_delete=models.CASCADE,
        related_name='profile'
    )

    # Social Media Links
    facebook_url = models.URLField(blank=True, help_text="Facebook profile URL")
    instagram_url = models.URLField(blank=True, help_text="Instagram profile URL")
    twitter_url = models.URLField(blank=True, help_text="Twitter profile URL")
    linkedin_url = models.URLField(blank=True, help_text="LinkedIn profile URL")

    # Platform Preferences
    email_notifications = models.BooleanField(
        default=True,
        help_text="Receive email notifications"
    )
    sms_notifications = models.BooleanField(
        default=False,
        help_text="Receive SMS notifications"
    )
    marketing_emails = models.BooleanField(
        default=False,
        help_text="Receive marketing emails"
    )

    # Privacy Settings
    profile_visibility = models.CharField(
        max_length=20,
        choices=[
            ('public', 'Public'),
            ('friends', 'Friends Only'),
            ('private', 'Private'),
        ],
        default='public',
        help_text="Who can see your profile"
    )
    show_online_status = models.BooleanField(
        default=True,
        help_text="Show when you're online"
    )

    # Interests (for content recommendations)
    interests = models.TextField(
        blank=True,
        help_text="Comma-separated list of interests"
    )

    # Statistics
    total_logins = models.PositiveIntegerField(default=0)
    total_posts = models.PositiveIntegerField(default=0)
    total_purchases = models.PositiveIntegerField(default=0)

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'accounts_userprofile'
        verbose_name = 'User Profile'
        verbose_name_plural = 'User Profiles'

    def __str__(self):
        return f"{self.user.get_display_name()}'s Profile"

    def get_interests_list(self):
        """Return interests as a list."""
        if self.interests:
            return [interest.strip() for interest in self.interests.split(',')]
        return []


class UserRole(models.Model):
    """
    User roles for different platform features.
    """
    ROLE_CHOICES = [
        ('customer', 'Customer'),
        ('vendor', 'Vendor'),
        ('content_creator', 'Content Creator'),
        ('event_organizer', 'Event Organizer'),
        ('employer', 'Employer'),
        ('job_seeker', 'Job Seeker'),
        ('moderator', 'Moderator'),
        ('admin', 'Administrator'),
    ]

    user = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='roles'
    )
    role = models.CharField(max_length=20, choices=ROLE_CHOICES)
    is_active = models.BooleanField(default=True)
    granted_at = models.DateTimeField(auto_now_add=True)
    granted_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='granted_roles'
    )

    class Meta:
        db_table = 'accounts_userrole'
        verbose_name = 'User Role'
        verbose_name_plural = 'User Roles'
        unique_together = ['user', 'role']

    def __str__(self):
        return f"{self.user.get_display_name()} - {self.get_role_display()}"


class UserActivity(models.Model):
    """
    Track user activity for analytics and engagement.
    """
    ACTIVITY_CHOICES = [
        ('login', 'Login'),
        ('logout', 'Logout'),
        ('profile_update', 'Profile Update'),
        ('password_change', 'Password Change'),
        ('purchase', 'Purchase'),
        ('post_create', 'Post Created'),
        ('comment', 'Comment'),
        ('like', 'Like'),
        ('share', 'Share'),
        ('video_watch', 'Video Watch'),
        ('event_book', 'Event Booking'),
        ('job_apply', 'Job Application'),
    ]

    user = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='activities'
    )
    activity_type = models.CharField(max_length=20, choices=ACTIVITY_CHOICES)
    description = models.TextField(blank=True)
    ip_address = models.GenericIPAddressField(null=True, blank=True)
    user_agent = models.TextField(blank=True)
    timestamp = models.DateTimeField(auto_now_add=True)

    # Optional reference to related object
    content_type = models.CharField(max_length=50, blank=True)
    object_id = models.PositiveIntegerField(null=True, blank=True)

    class Meta:
        db_table = 'accounts_useractivity'
        verbose_name = 'User Activity'
        verbose_name_plural = 'User Activities'
        ordering = ['-timestamp']
        indexes = [
            models.Index(fields=['user', '-timestamp']),
            models.Index(fields=['activity_type', '-timestamp']),
        ]

    def __str__(self):
        return f"{self.user.get_display_name()} - {self.get_activity_type_display()} at {self.timestamp}"
