"""
Admin interface for community app.

Provides comprehensive admin interface for managing community features
including forums, posts, comments, user interactions, and moderation.
"""
from django.contrib import admin
from .models import (
    ForumCategory, Post, Comment, UserFollow, PostLike, CommentLike,
    PostBookmark, CommunityProfile, Report, Notification
)


@admin.register(ForumCategory)
class ForumCategoryAdmin(admin.ModelAdmin):
    """Admin interface for forum categories."""
    list_display = [
        'name', 'slug', 'is_local_category', 'is_featured', 'is_active',
        'post_count', 'display_order', 'requires_approval'
    ]
    list_filter = ['is_local_category', 'is_featured', 'is_active', 'requires_approval', 'created_at']
    search_fields = ['name', 'description']
    prepopulated_fields = {'slug': ('name',)}
    ordering = ['display_order', 'name']
    readonly_fields = ['post_count']

    fieldsets = (
        ('Basic Information', {
            'fields': ('name', 'slug', 'description')
        }),
        ('Visual Elements', {
            'fields': ('icon', 'color'),
            'classes': ('collapse',)
        }),
        ('Settings', {
            'fields': ('is_local_category', 'is_featured', 'is_active', 'display_order')
        }),
        ('Moderation', {
            'fields': ('requires_approval',)
        }),
        ('Statistics', {
            'fields': ('post_count',),
            'classes': ('collapse',)
        }),
    )

    actions = ['activate_categories', 'deactivate_categories', 'feature_categories']

    def activate_categories(self, request, queryset):
        """Activate selected categories."""
        updated = queryset.update(is_active=True)
        self.message_user(request, f'{updated} categories activated.')
    activate_categories.short_description = 'Activate selected categories'

    def deactivate_categories(self, request, queryset):
        """Deactivate selected categories."""
        updated = queryset.update(is_active=False)
        self.message_user(request, f'{updated} categories deactivated.')
    deactivate_categories.short_description = 'Deactivate selected categories'

    def feature_categories(self, request, queryset):
        """Feature selected categories."""
        updated = queryset.update(is_featured=True)
        self.message_user(request, f'{updated} categories featured.')
    feature_categories.short_description = 'Feature selected categories'


class CommentInline(admin.TabularInline):
    """Inline admin for comments."""
    model = Comment
    extra = 0
    fields = ['author', 'content', 'status', 'like_count', 'created_at']
    readonly_fields = ['like_count', 'created_at']
    can_delete = False


@admin.register(Post)
class PostAdmin(admin.ModelAdmin):
    """Admin interface for posts."""
    list_display = [
        'title', 'author', 'category', 'post_type', 'status', 'is_local_content',
        'is_featured', 'is_pinned', 'view_count', 'like_count', 'comment_count', 'created_at'
    ]
    list_filter = [
        'category', 'post_type', 'status', 'is_local_content', 'is_featured',
        'is_pinned', 'island', 'allow_comments', 'created_at'
    ]
    search_fields = ['title', 'content', 'author__username', 'location']
    prepopulated_fields = {'slug': ('title',)}
    autocomplete_fields = ['author', 'category']
    readonly_fields = ['view_count', 'like_count', 'comment_count', 'published_at']
    date_hierarchy = 'created_at'

    fieldsets = (
        ('Basic Information', {
            'fields': ('title', 'slug', 'content', 'excerpt', 'author', 'category')
        }),
        ('Classification', {
            'fields': ('post_type', 'status', 'featured_image')
        }),
        ('Seychelles Context', {
            'fields': ('location', 'island', 'is_local_content'),
            'classes': ('collapse',)
        }),
        ('Engagement Settings', {
            'fields': ('is_pinned', 'is_featured', 'allow_comments')
        }),
        ('Statistics', {
            'fields': ('view_count', 'like_count', 'comment_count', 'published_at'),
            'classes': ('collapse',)
        }),
    )

    inlines = [CommentInline]

    actions = [
        'publish_posts', 'unpublish_posts', 'feature_posts', 'unfeature_posts',
        'pin_posts', 'unpin_posts', 'approve_posts'
    ]

    def publish_posts(self, request, queryset):
        """Publish selected posts."""
        updated = queryset.update(status='published')
        self.message_user(request, f'{updated} posts published.')
    publish_posts.short_description = 'Publish selected posts'

    def unpublish_posts(self, request, queryset):
        """Unpublish selected posts."""
        updated = queryset.update(status='draft')
        self.message_user(request, f'{updated} posts unpublished.')
    unpublish_posts.short_description = 'Unpublish selected posts'

    def feature_posts(self, request, queryset):
        """Feature selected posts."""
        updated = queryset.update(is_featured=True)
        self.message_user(request, f'{updated} posts featured.')
    feature_posts.short_description = 'Feature selected posts'

    def unfeature_posts(self, request, queryset):
        """Unfeature selected posts."""
        updated = queryset.update(is_featured=False)
        self.message_user(request, f'{updated} posts unfeatured.')
    unfeature_posts.short_description = 'Unfeature selected posts'

    def pin_posts(self, request, queryset):
        """Pin selected posts."""
        updated = queryset.update(is_pinned=True)
        self.message_user(request, f'{updated} posts pinned.')
    pin_posts.short_description = 'Pin selected posts'

    def unpin_posts(self, request, queryset):
        """Unpin selected posts."""
        updated = queryset.update(is_pinned=False)
        self.message_user(request, f'{updated} posts unpinned.')
    unpin_posts.short_description = 'Unpin selected posts'

    def approve_posts(self, request, queryset):
        """Approve pending posts."""
        updated = queryset.filter(status='pending').update(status='published')
        self.message_user(request, f'{updated} posts approved and published.')
    approve_posts.short_description = 'Approve pending posts'


@admin.register(Comment)
class CommentAdmin(admin.ModelAdmin):
    """Admin interface for comments."""
    list_display = [
        'get_post_title', 'author', 'get_content_preview', 'status',
        'is_reply', 'like_count', 'created_at'
    ]
    list_filter = ['status', 'created_at', 'post__category']
    search_fields = ['content', 'author__username', 'post__title']
    autocomplete_fields = ['post', 'author', 'parent']
    readonly_fields = ['like_count', 'is_reply']
    date_hierarchy = 'created_at'

    fieldsets = (
        ('Comment Information', {
            'fields': ('post', 'author', 'parent', 'content')
        }),
        ('Status', {
            'fields': ('status',)
        }),
        ('Statistics', {
            'fields': ('like_count', 'is_reply'),
            'classes': ('collapse',)
        }),
    )

    actions = ['approve_comments', 'reject_comments', 'hide_comments']

    def get_post_title(self, obj):
        """Get the post title."""
        return obj.post.title
    get_post_title.short_description = 'Post'

    def get_content_preview(self, obj):
        """Get a preview of the comment content."""
        return obj.content[:100] + '...' if len(obj.content) > 100 else obj.content
    get_content_preview.short_description = 'Content Preview'

    def approve_comments(self, request, queryset):
        """Approve selected comments."""
        updated = queryset.update(status='published')
        self.message_user(request, f'{updated} comments approved.')
    approve_comments.short_description = 'Approve selected comments'

    def reject_comments(self, request, queryset):
        """Reject selected comments."""
        updated = queryset.update(status='rejected')
        self.message_user(request, f'{updated} comments rejected.')
    reject_comments.short_description = 'Reject selected comments'

    def hide_comments(self, request, queryset):
        """Hide selected comments."""
        updated = queryset.update(status='hidden')
        self.message_user(request, f'{updated} comments hidden.')
    hide_comments.short_description = 'Hide selected comments'


@admin.register(UserFollow)
class UserFollowAdmin(admin.ModelAdmin):
    """Admin interface for user follows."""
    list_display = ['follower', 'following', 'created_at']
    list_filter = ['created_at']
    search_fields = ['follower__username', 'following__username']
    autocomplete_fields = ['follower', 'following']

    fieldsets = (
        ('Follow Relationship', {
            'fields': ('follower', 'following')
        }),
    )


@admin.register(PostLike)
class PostLikeAdmin(admin.ModelAdmin):
    """Admin interface for post likes."""
    list_display = ['user', 'get_post_title', 'created_at']
    list_filter = ['created_at', 'post__category']
    search_fields = ['user__username', 'post__title']
    autocomplete_fields = ['user', 'post']

    def get_post_title(self, obj):
        """Get the post title."""
        return obj.post.title
    get_post_title.short_description = 'Post'


@admin.register(CommentLike)
class CommentLikeAdmin(admin.ModelAdmin):
    """Admin interface for comment likes."""
    list_display = ['user', 'get_comment_preview', 'get_post_title', 'created_at']
    list_filter = ['created_at']
    search_fields = ['user__username', 'comment__content', 'comment__post__title']
    autocomplete_fields = ['user', 'comment']

    def get_comment_preview(self, obj):
        """Get a preview of the comment."""
        return obj.comment.content[:50] + '...' if len(obj.comment.content) > 50 else obj.comment.content
    get_comment_preview.short_description = 'Comment'

    def get_post_title(self, obj):
        """Get the post title."""
        return obj.comment.post.title
    get_post_title.short_description = 'Post'


@admin.register(PostBookmark)
class PostBookmarkAdmin(admin.ModelAdmin):
    """Admin interface for post bookmarks."""
    list_display = ['user', 'get_post_title', 'created_at']
    list_filter = ['created_at', 'post__category']
    search_fields = ['user__username', 'post__title']
    autocomplete_fields = ['user', 'post']

    def get_post_title(self, obj):
        """Get the post title."""
        return obj.post.title
    get_post_title.short_description = 'Post'


@admin.register(CommunityProfile)
class CommunityProfileAdmin(admin.ModelAdmin):
    """Admin interface for community profiles."""
    list_display = [
        'user', 'reputation_score', 'total_posts', 'total_comments',
        'total_likes_received', 'follower_count', 'following_count'
    ]
    list_filter = ['show_email', 'show_location', 'allow_messages', 'created_at']
    search_fields = ['user__username', 'bio', 'website']
    autocomplete_fields = ['user']
    readonly_fields = [
        'reputation_score', 'total_posts', 'total_comments',
        'total_likes_received', 'follower_count', 'following_count'
    ]

    fieldsets = (
        ('User', {
            'fields': ('user',)
        }),
        ('Profile Information', {
            'fields': ('bio', 'website')
        }),
        ('Privacy Settings', {
            'fields': ('show_email', 'show_location', 'allow_messages')
        }),
        ('Notification Preferences', {
            'fields': (
                'notify_on_follow', 'notify_on_comment',
                'notify_on_like', 'notify_on_mention'
            ),
            'classes': ('collapse',)
        }),
        ('Statistics', {
            'fields': (
                'reputation_score', 'total_posts', 'total_comments',
                'total_likes_received', 'follower_count', 'following_count'
            ),
            'classes': ('collapse',)
        }),
    )


@admin.register(Report)
class ReportAdmin(admin.ModelAdmin):
    """Admin interface for reports."""
    list_display = [
        'reporter', 'get_content_type', 'report_type', 'status',
        'reviewed_by', 'created_at'
    ]
    list_filter = ['report_type', 'status', 'created_at', 'reviewed_at']
    search_fields = ['reporter__username', 'description', 'resolution_notes']
    autocomplete_fields = ['reporter', 'post', 'comment', 'reported_user', 'reviewed_by']
    readonly_fields = ['get_content_type']
    date_hierarchy = 'created_at'

    fieldsets = (
        ('Report Information', {
            'fields': ('reporter', 'report_type', 'description')
        }),
        ('Reported Content', {
            'fields': ('post', 'comment', 'reported_user', 'get_content_type')
        }),
        ('Status & Review', {
            'fields': ('status', 'reviewed_by', 'reviewed_at', 'resolution_notes')
        }),
    )

    actions = ['mark_reviewing', 'mark_resolved', 'mark_dismissed']

    def get_content_type(self, obj):
        """Get the type of content being reported."""
        if obj.post:
            return f"Post: {obj.post.title}"
        elif obj.comment:
            return f"Comment on: {obj.comment.post.title}"
        elif obj.reported_user:
            return f"User: {obj.reported_user.get_display_name()}"
        return "Unknown"
    get_content_type.short_description = 'Reported Content'

    def mark_reviewing(self, request, queryset):
        """Mark reports as under review."""
        updated = queryset.update(status='reviewing')
        self.message_user(request, f'{updated} reports marked as under review.')
    mark_reviewing.short_description = 'Mark as under review'

    def mark_resolved(self, request, queryset):
        """Mark reports as resolved."""
        from django.utils import timezone
        updated = queryset.update(status='resolved', reviewed_at=timezone.now())
        self.message_user(request, f'{updated} reports marked as resolved.')
    mark_resolved.short_description = 'Mark as resolved'

    def mark_dismissed(self, request, queryset):
        """Mark reports as dismissed."""
        from django.utils import timezone
        updated = queryset.update(status='dismissed', reviewed_at=timezone.now())
        self.message_user(request, f'{updated} reports dismissed.')
    mark_dismissed.short_description = 'Dismiss reports'


@admin.register(Notification)
class NotificationAdmin(admin.ModelAdmin):
    """Admin interface for notifications."""
    list_display = [
        'recipient', 'notification_type', 'title', 'sender',
        'is_read', 'created_at'
    ]
    list_filter = ['notification_type', 'is_read', 'created_at']
    search_fields = ['recipient__username', 'sender__username', 'title', 'message']
    autocomplete_fields = ['recipient', 'sender', 'post', 'comment']
    readonly_fields = ['read_at']
    date_hierarchy = 'created_at'

    fieldsets = (
        ('Notification Details', {
            'fields': ('recipient', 'sender', 'notification_type', 'title', 'message')
        }),
        ('Related Content', {
            'fields': ('post', 'comment'),
            'classes': ('collapse',)
        }),
        ('Status', {
            'fields': ('is_read', 'read_at')
        }),
    )

    actions = ['mark_as_read', 'mark_as_unread']

    def mark_as_read(self, request, queryset):
        """Mark notifications as read."""
        from django.utils import timezone
        updated = queryset.update(is_read=True, read_at=timezone.now())
        self.message_user(request, f'{updated} notifications marked as read.')
    mark_as_read.short_description = 'Mark as read'

    def mark_as_unread(self, request, queryset):
        """Mark notifications as unread."""
        updated = queryset.update(is_read=False, read_at=None)
        self.message_user(request, f'{updated} notifications marked as unread.')
    mark_as_unread.short_description = 'Mark as unread'


# Customize admin site header for community
admin.site.site_header = "Kominote Community Administration"
admin.site.site_title = "Kominote Community Admin"
admin.site.index_title = "Welcome to Kominote Community Administration"
