"""Views for jobs app."""
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework.permissions import AllowAny, IsAuthenticated

class JobsHomeView(APIView):
    permission_classes = [AllowAny]
    def get(self, request): return Response({'message': 'Jobs Home', 'status': 'Phase 2 Complete'})

class JobListView(APIView):
    permission_classes = [AllowAny]
    def get(self, request): return Response({'message': 'Job List', 'status': 'Coming in Phase 3'})

class JobDetailView(APIView):
    permission_classes = [AllowAny]
    def get(self, request, pk): return Response({'message': f'Job Detail {pk}', 'status': 'Coming in Phase 3'})

class JobSearchView(APIView):
    permission_classes = [AllowAny]
    def get(self, request): return Response({'message': 'Job Search', 'status': 'Coming in Phase 3'})

class JobCategoryListView(APIView):
    permission_classes = [AllowAny]
    def get(self, request): return Response({'message': 'Job Category List', 'status': 'Coming in Phase 3'})

class JobCategoryDetailView(APIView):
    permission_classes = [AllowAny]
    def get(self, request, pk): return Response({'message': f'Job Category Detail {pk}', 'status': 'Coming in Phase 3'})

class CompanyListView(APIView):
    permission_classes = [AllowAny]
    def get(self, request): return Response({'message': 'Company List', 'status': 'Coming in Phase 3'})

class CompanyDetailView(APIView):
    permission_classes = [AllowAny]
    def get(self, request, pk): return Response({'message': f'Company Detail {pk}', 'status': 'Coming in Phase 3'})

class JobApplicationView(APIView):
    permission_classes = [IsAuthenticated]
    def post(self, request, pk): return Response({'message': f'Apply to Job {pk}', 'status': 'Coming in Phase 3'})

class ApplicationListView(APIView):
    permission_classes = [IsAuthenticated]
    def get(self, request): return Response({'message': 'Application List', 'status': 'Coming in Phase 3'})

class ApplicationDetailView(APIView):
    permission_classes = [IsAuthenticated]
    def get(self, request, pk): return Response({'message': f'Application Detail {pk}', 'status': 'Coming in Phase 3'})

class EmployerDashboardView(APIView):
    permission_classes = [IsAuthenticated]
    def get(self, request): return Response({'message': 'Employer Dashboard', 'status': 'Coming in Phase 3'})

class EmployerJobsView(APIView):
    permission_classes = [IsAuthenticated]
    def get(self, request): return Response({'message': 'Employer Jobs', 'status': 'Coming in Phase 3'})

class JobCreateView(APIView):
    permission_classes = [IsAuthenticated]
    def post(self, request): return Response({'message': 'Job Create', 'status': 'Coming in Phase 3'})

class JobEditView(APIView):
    permission_classes = [IsAuthenticated]
    def put(self, request, pk): return Response({'message': f'Job Edit {pk}', 'status': 'Coming in Phase 3'})

class EmployerApplicationsView(APIView):
    permission_classes = [IsAuthenticated]
    def get(self, request): return Response({'message': 'Employer Applications', 'status': 'Coming in Phase 3'})

class JobSeekerProfileView(APIView):
    permission_classes = [IsAuthenticated]
    def get(self, request): return Response({'message': 'Job Seeker Profile', 'status': 'Coming in Phase 3'})

class JobSeekerProfileEditView(APIView):
    permission_classes = [IsAuthenticated]
    def put(self, request): return Response({'message': 'Job Seeker Profile Edit', 'status': 'Coming in Phase 3'})

class SavedJobsView(APIView):
    permission_classes = [IsAuthenticated]
    def get(self, request): return Response({'message': 'Saved Jobs', 'status': 'Coming in Phase 3'})

class SaveJobView(APIView):
    permission_classes = [IsAuthenticated]
    def post(self, request, pk): return Response({'message': f'Save Job {pk}', 'status': 'Coming in Phase 3'})
