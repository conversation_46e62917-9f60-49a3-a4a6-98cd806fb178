"""Views for community app."""
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework.permissions import AllowAny, IsAuthenticated

class CommunityHomeView(APIView):
    permission_classes = [AllowAny]
    def get(self, request): return Response({'message': 'Community Home', 'status': 'Phase 2 Complete'})

class ForumListView(APIView):
    permission_classes = [AllowAny]
    def get(self, request): return Response({'message': 'Forum List', 'status': 'Coming in Phase 3'})

class ForumDetailView(APIView):
    permission_classes = [AllowAny]
    def get(self, request, pk): return Response({'message': f'Forum Detail {pk}', 'status': 'Coming in Phase 3'})

class PostListView(APIView):
    permission_classes = [AllowAny]
    def get(self, request): return Response({'message': 'Post List', 'status': 'Coming in Phase 3'})

class PostDetailView(APIView):
    permission_classes = [AllowAny]
    def get(self, request, pk): return Response({'message': f'Post Detail {pk}', 'status': 'Coming in Phase 3'})

class PostCreateView(APIView):
    permission_classes = [IsAuthenticated]
    def post(self, request): return Response({'message': 'Post Create', 'status': 'Coming in Phase 3'})

class PostEditView(APIView):
    permission_classes = [IsAuthenticated]
    def put(self, request, pk): return Response({'message': f'Post Edit {pk}', 'status': 'Coming in Phase 3'})

class CommentListView(APIView):
    permission_classes = [AllowAny]
    def get(self, request, post_pk): return Response({'message': f'Comments for Post {post_pk}', 'status': 'Coming in Phase 3'})

class CommentDetailView(APIView):
    permission_classes = [AllowAny]
    def get(self, request, pk): return Response({'message': f'Comment Detail {pk}', 'status': 'Coming in Phase 3'})

class PostLikeView(APIView):
    permission_classes = [IsAuthenticated]
    def post(self, request, pk): return Response({'message': f'Like Post {pk}', 'status': 'Coming in Phase 3'})

class ProfileListView(APIView):
    permission_classes = [AllowAny]
    def get(self, request): return Response({'message': 'Profile List', 'status': 'Coming in Phase 3'})

class ProfileDetailView(APIView):
    permission_classes = [AllowAny]
    def get(self, request, pk): return Response({'message': f'Profile Detail {pk}', 'status': 'Coming in Phase 3'})

class FollowUserView(APIView):
    permission_classes = [IsAuthenticated]
    def post(self, request, pk): return Response({'message': f'Follow User {pk}', 'status': 'Coming in Phase 3'})
