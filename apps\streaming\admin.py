"""
Admin interface for streaming app.

Provides comprehensive admin interface for managing video content, categories,
playlists, and user interactions in the Kominote streaming platform.
"""
from django.contrib import admin
from django.utils.html import format_html
from django.urls import reverse
from .models import (
    VideoCategory, VideoContent, Season, Episode, Playlist, PlaylistItem,
    WatchHistory, UserRating, ContentTag, ContentTagging, StreamingProfile
)


@admin.register(VideoCategory)
class VideoCategoryAdmin(admin.ModelAdmin):
    """Admin interface for video categories."""
    list_display = ['name', 'slug', 'is_local_content', 'is_featured', 'is_active', 'display_order', 'content_count']
    list_filter = ['is_local_content', 'is_featured', 'is_active', 'created_at']
    search_fields = ['name', 'description']
    prepopulated_fields = {'slug': ('name',)}
    ordering = ['display_order', 'name']

    fieldsets = (
        ('Basic Information', {
            'fields': ('name', 'slug', 'description')
        }),
        ('Visual Elements', {
            'fields': ('icon', 'banner_image'),
            'classes': ('collapse',)
        }),
        ('Settings', {
            'fields': ('is_local_content', 'is_featured', 'is_active', 'display_order')
        }),
    )

    def content_count(self, obj):
        """Display number of content items in this category."""
        count = obj.content.count()
        if count > 0:
            url = reverse('admin:streaming_videocontent_changelist') + f'?category__id__exact={obj.id}'
            return format_html('<a href="{}">{} items</a>', url, count)
        return '0 items'
    content_count.short_description = 'Content Count'


class SeasonInline(admin.TabularInline):
    """Inline admin for seasons within series."""
    model = Season
    extra = 1
    fields = ['season_number', 'title', 'release_date', 'is_complete']
    readonly_fields = ['episode_count']

    def episode_count(self, obj):
        if obj.pk:
            return obj.episode_count
        return 0
    episode_count.short_description = 'Episodes'


class ContentTaggingInline(admin.TabularInline):
    """Inline admin for content tags."""
    model = ContentTagging
    extra = 1
    autocomplete_fields = ['tag']


@admin.register(VideoContent)
class VideoContentAdmin(admin.ModelAdmin):
    """Admin interface for video content."""
    list_display = [
        'title', 'content_type', 'category', 'is_local_production',
        'is_published', 'is_featured', 'view_count', 'average_rating', 'created_at'
    ]
    list_filter = [
        'content_type', 'category', 'is_local_production', 'is_published',
        'is_featured', 'is_premium', 'language', 'production_year', 'created_at'
    ]
    search_fields = ['title', 'description', 'director', 'producer', 'cast']
    prepopulated_fields = {'slug': ('title',)}
    autocomplete_fields = ['category', 'created_by']
    readonly_fields = ['view_count', 'like_count', 'average_rating', 'rating_count', 'duration_display']
    date_hierarchy = 'publish_date'

    fieldsets = (
        ('Basic Information', {
            'fields': ('title', 'slug', 'description', 'short_description', 'content_type', 'category')
        }),
        ('Media Files', {
            'fields': ('video_file', 'trailer_file', 'thumbnail', 'poster', 'banner'),
            'classes': ('collapse',)
        }),
        ('Technical Details', {
            'fields': ('duration_minutes', 'duration_display', 'quality', 'file_size_mb'),
            'classes': ('collapse',)
        }),
        ('Content Details', {
            'fields': ('language', 'has_subtitles', 'subtitle_languages'),
            'classes': ('collapse',)
        }),
        ('Production Information', {
            'fields': ('director', 'producer', 'cast', 'production_year'),
            'classes': ('collapse',)
        }),
        ('Seychelles Context', {
            'fields': ('is_local_production', 'filming_locations', 'local_cast'),
            'classes': ('collapse',)
        }),
        ('Publishing & Access', {
            'fields': ('is_published', 'is_featured', 'is_premium', 'publish_date', 'created_by')
        }),
        ('Statistics', {
            'fields': ('view_count', 'like_count', 'average_rating', 'rating_count'),
            'classes': ('collapse',)
        }),
    )

    inlines = [ContentTaggingInline]

    def get_inlines(self, request, obj=None):
        """Add SeasonInline only for series content."""
        inlines = [ContentTaggingInline]
        if obj and obj.content_type == 'series':
            inlines.insert(0, SeasonInline)
        return inlines

    def save_model(self, request, obj, form, change):
        """Set created_by to current user if not set."""
        if not change and not obj.created_by:
            obj.created_by = request.user
        super().save_model(request, obj, form, change)


class EpisodeInline(admin.TabularInline):
    """Inline admin for episodes within seasons."""
    model = Episode
    extra = 1
    fields = ['episode_number', 'title', 'duration_minutes', 'air_date', 'is_published', 'view_count']
    readonly_fields = ['view_count']


@admin.register(Season)
class SeasonAdmin(admin.ModelAdmin):
    """Admin interface for seasons."""
    list_display = ['__str__', 'series', 'season_number', 'episode_count', 'release_date', 'is_complete']
    list_filter = ['series', 'is_complete', 'release_date']
    search_fields = ['title', 'series__title']
    autocomplete_fields = ['series']

    inlines = [EpisodeInline]

    fieldsets = (
        ('Basic Information', {
            'fields': ('series', 'season_number', 'title', 'description')
        }),
        ('Media', {
            'fields': ('poster',),
            'classes': ('collapse',)
        }),
        ('Release Information', {
            'fields': ('release_date', 'is_complete')
        }),
    )


@admin.register(Episode)
class EpisodeAdmin(admin.ModelAdmin):
    """Admin interface for episodes."""
    list_display = ['title', 'season', 'episode_number', 'duration_minutes', 'air_date', 'is_published', 'view_count']
    list_filter = ['season__series', 'is_published', 'air_date']
    search_fields = ['title', 'description', 'season__series__title']
    autocomplete_fields = ['season']
    prepopulated_fields = {'slug': ('title',)}
    date_hierarchy = 'air_date'

    fieldsets = (
        ('Basic Information', {
            'fields': ('season', 'episode_number', 'title', 'slug', 'description')
        }),
        ('Media Files', {
            'fields': ('video_file', 'thumbnail')
        }),
        ('Technical Details', {
            'fields': ('duration_minutes', 'quality')
        }),
        ('Publishing', {
            'fields': ('air_date', 'is_published')
        }),
        ('Statistics', {
            'fields': ('view_count',),
            'classes': ('collapse',)
        }),
    )


class PlaylistItemInline(admin.TabularInline):
    """Inline admin for playlist items."""
    model = PlaylistItem
    extra = 1
    autocomplete_fields = ['content']
    fields = ['content', 'order', 'added_at']
    readonly_fields = ['added_at']


@admin.register(Playlist)
class PlaylistAdmin(admin.ModelAdmin):
    """Admin interface for playlists."""
    list_display = ['name', 'user', 'privacy', 'item_count', 'created_at']
    list_filter = ['privacy', 'created_at']
    search_fields = ['name', 'description', 'user__username', 'user__email']
    autocomplete_fields = ['user']

    inlines = [PlaylistItemInline]

    fieldsets = (
        ('Basic Information', {
            'fields': ('user', 'name', 'description')
        }),
        ('Settings', {
            'fields': ('privacy',)
        }),
    )


@admin.register(WatchHistory)
class WatchHistoryAdmin(admin.ModelAdmin):
    """Admin interface for watch history."""
    list_display = [
        'user', 'get_content_title', 'progress_percentage', 'is_completed',
        'device_type', 'last_watched_at'
    ]
    list_filter = ['is_completed', 'device_type', 'last_watched_at']
    search_fields = ['user__username', 'content__title', 'episode__title']
    autocomplete_fields = ['user', 'content', 'episode']
    readonly_fields = ['progress_percentage', 'is_completed']
    date_hierarchy = 'last_watched_at'

    fieldsets = (
        ('User & Content', {
            'fields': ('user', 'content', 'episode')
        }),
        ('Progress Tracking', {
            'fields': ('watch_time_seconds', 'total_duration_seconds', 'progress_percentage', 'is_completed')
        }),
        ('Session Info', {
            'fields': ('device_type', 'ip_address'),
            'classes': ('collapse',)
        }),
    )

    def get_content_title(self, obj):
        """Get the title of the content being watched."""
        if obj.episode:
            return f"{obj.episode.season.series.title} - {obj.episode.title}"
        elif obj.content:
            return obj.content.title
        return "Unknown"
    get_content_title.short_description = 'Content'


@admin.register(UserRating)
class UserRatingAdmin(admin.ModelAdmin):
    """Admin interface for user ratings."""
    list_display = ['user', 'content', 'rating', 'is_approved', 'created_at']
    list_filter = ['rating', 'is_approved', 'created_at']
    search_fields = ['user__username', 'content__title', 'review']
    autocomplete_fields = ['user', 'content']

    fieldsets = (
        ('Rating Information', {
            'fields': ('user', 'content', 'rating')
        }),
        ('Review', {
            'fields': ('review',)
        }),
        ('Moderation', {
            'fields': ('is_approved',)
        }),
    )


@admin.register(ContentTag)
class ContentTagAdmin(admin.ModelAdmin):
    """Admin interface for content tags."""
    list_display = ['name', 'slug', 'is_local_tag', 'usage_count', 'created_at']
    list_filter = ['is_local_tag', 'created_at']
    search_fields = ['name', 'description']
    prepopulated_fields = {'slug': ('name',)}
    readonly_fields = ['usage_count']

    fieldsets = (
        ('Basic Information', {
            'fields': ('name', 'slug', 'description')
        }),
        ('Settings', {
            'fields': ('is_local_tag',)
        }),
        ('Statistics', {
            'fields': ('usage_count',),
            'classes': ('collapse',)
        }),
    )


@admin.register(StreamingProfile)
class StreamingProfileAdmin(admin.ModelAdmin):
    """Admin interface for streaming profiles."""
    list_display = [
        'user', 'preferred_quality', 'preferred_language', 'prefer_local_content',
        'auto_play_next', 'updated_at'
    ]
    list_filter = [
        'preferred_quality', 'preferred_language', 'prefer_local_content',
        'auto_play_next', 'always_show_subtitles', 'limit_mobile_quality'
    ]
    search_fields = ['user__username', 'user__email']
    autocomplete_fields = ['user']
    filter_horizontal = ['favorite_categories']

    fieldsets = (
        ('User', {
            'fields': ('user',)
        }),
        ('Viewing Preferences', {
            'fields': ('preferred_quality', 'auto_play_next', 'skip_intro')
        }),
        ('Language Preferences', {
            'fields': ('preferred_language', 'subtitle_language', 'always_show_subtitles')
        }),
        ('Content Preferences', {
            'fields': ('favorite_categories', 'prefer_local_content')
        }),
        ('Parental Controls', {
            'fields': ('content_rating_limit',),
            'classes': ('collapse',)
        }),
        ('Data Usage', {
            'fields': ('limit_mobile_quality', 'mobile_quality_limit'),
            'classes': ('collapse',)
        }),
        ('Notifications', {
            'fields': ('notify_new_episodes', 'notify_new_local_content'),
            'classes': ('collapse',)
        }),
    )


# Customize admin site header
admin.site.site_header = "Kominote Streaming Administration"
admin.site.site_title = "Kominote Streaming Admin"
admin.site.index_title = "Welcome to Kominote Streaming Administration"
