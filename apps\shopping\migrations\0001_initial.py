# Generated by Django 4.2.21 on 2025-05-30 10:36

from decimal import Decimal
from django.conf import settings
import django.core.validators
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Order',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('order_number', models.CharField(help_text='Unique order number', max_length=20, unique=True)),
                ('status', models.CharField(choices=[('pending', 'Pending'), ('confirmed', 'Confirmed'), ('processing', 'Processing'), ('shipped', 'Shipped'), ('delivered', 'Delivered'), ('cancelled', 'Cancelled'), ('refunded', 'Refunded')], default='pending', help_text='Order status', max_length=20)),
                ('payment_status', models.CharField(choices=[('pending', 'Pending'), ('paid', 'Paid'), ('failed', 'Failed'), ('refunded', 'Refunded')], default='pending', help_text='Payment status', max_length=20)),
                ('subtotal', models.DecimalField(decimal_places=2, help_text='Subtotal amount', max_digits=10)),
                ('shipping_cost', models.DecimalField(decimal_places=2, default=0.0, help_text='Shipping cost', max_digits=8)),
                ('tax_amount', models.DecimalField(decimal_places=2, default=0.0, help_text='Tax amount', max_digits=8)),
                ('total_amount', models.DecimalField(decimal_places=2, help_text='Total order amount', max_digits=10)),
                ('shipping_name', models.CharField(help_text='Shipping recipient name', max_length=200)),
                ('shipping_email', models.EmailField(help_text='Shipping email', max_length=254)),
                ('shipping_phone', models.CharField(help_text='Shipping phone number', max_length=20)),
                ('shipping_island', models.CharField(choices=[('mahe', 'Mahé'), ('praslin', 'Praslin'), ('la_digue', 'La Digue'), ('silhouette', 'Silhouette'), ('fregate', 'Fregate'), ('bird', 'Bird Island'), ('denis', 'Denis Island'), ('other', 'Other')], help_text='Shipping island', max_length=20)),
                ('shipping_district', models.CharField(help_text='Shipping district', max_length=100)),
                ('shipping_address', models.TextField(help_text='Full shipping address')),
                ('customer_notes', models.TextField(blank=True, help_text='Customer notes for the order')),
                ('admin_notes', models.TextField(blank=True, help_text='Admin notes (internal)')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('shipped_at', models.DateTimeField(blank=True, help_text='When the order was shipped', null=True)),
                ('delivered_at', models.DateTimeField(blank=True, help_text='When the order was delivered', null=True)),
                ('customer', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='orders', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Order',
                'verbose_name_plural': 'Orders',
                'db_table': 'shopping_order',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='Product',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(help_text='Product name', max_length=200)),
                ('slug', models.SlugField(help_text='URL-friendly product name', max_length=200, unique=True)),
                ('description', models.TextField(help_text='Product description')),
                ('short_description', models.CharField(blank=True, help_text='Short description for listings', max_length=300)),
                ('price', models.DecimalField(decimal_places=2, help_text='Product price in SCR', max_digits=10, validators=[django.core.validators.MinValueValidator(Decimal('0.01'))])),
                ('original_price', models.DecimalField(blank=True, decimal_places=2, help_text='Original price (for discounts)', max_digits=10, null=True)),
                ('stock_quantity', models.PositiveIntegerField(default=0, help_text='Available stock quantity')),
                ('low_stock_threshold', models.PositiveIntegerField(default=5, help_text='Low stock warning threshold')),
                ('track_inventory', models.BooleanField(default=True, help_text='Whether to track inventory for this product')),
                ('condition', models.CharField(choices=[('new', 'New'), ('like_new', 'Like New'), ('good', 'Good'), ('fair', 'Fair'), ('poor', 'Poor')], default='new', help_text='Product condition', max_length=20)),
                ('brand', models.CharField(blank=True, help_text='Product brand', max_length=100)),
                ('model', models.CharField(blank=True, help_text='Product model', max_length=100)),
                ('weight', models.DecimalField(blank=True, decimal_places=2, help_text='Product weight in kg', max_digits=8, null=True)),
                ('dimensions', models.CharField(blank=True, help_text='Product dimensions (L x W x H)', max_length=100)),
                ('is_local_product', models.BooleanField(default=False, help_text='Whether this is a locally made/sourced product')),
                ('origin_island', models.CharField(blank=True, choices=[('mahe', 'Mahé'), ('praslin', 'Praslin'), ('la_digue', 'La Digue'), ('silhouette', 'Silhouette'), ('fregate', 'Fregate'), ('bird', 'Bird Island'), ('denis', 'Denis Island'), ('other', 'Other')], help_text='Island of origin/production', max_length=20)),
                ('local_materials', models.BooleanField(default=False, help_text='Made with local Seychelles materials')),
                ('featured_image', models.ImageField(blank=True, help_text='Main product image', null=True, upload_to='shopping/products/')),
                ('meta_title', models.CharField(blank=True, help_text='SEO meta title', max_length=200)),
                ('meta_description', models.CharField(blank=True, help_text='SEO meta description', max_length=300)),
                ('tags', models.CharField(blank=True, help_text='Product tags (comma-separated)', max_length=500)),
                ('is_active', models.BooleanField(default=True, help_text='Whether the product is active and visible')),
                ('is_featured', models.BooleanField(default=False, help_text='Featured product status')),
                ('is_digital', models.BooleanField(default=False, help_text='Whether this is a digital product')),
                ('view_count', models.PositiveIntegerField(default=0, help_text='Number of product views')),
                ('sales_count', models.PositiveIntegerField(default=0, help_text='Number of times sold')),
                ('average_rating', models.DecimalField(decimal_places=2, default=0.0, help_text='Average product rating', max_digits=3)),
                ('rating_count', models.PositiveIntegerField(default=0, help_text='Number of ratings')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'Product',
                'verbose_name_plural': 'Products',
                'db_table': 'shopping_product',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='Wishlist',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(help_text='Wishlist name', max_length=100)),
                ('description', models.TextField(blank=True, help_text='Wishlist description')),
                ('is_public', models.BooleanField(default=False, help_text='Whether this wishlist is public')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='wishlists', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Wishlist',
                'verbose_name_plural': 'Wishlists',
                'db_table': 'shopping_wishlist',
                'ordering': ['-updated_at'],
            },
        ),
        migrations.CreateModel(
            name='Vendor',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('business_name', models.CharField(help_text='Business or vendor name', max_length=200)),
                ('business_registration', models.CharField(blank=True, help_text='Business registration number', max_length=100)),
                ('vendor_type', models.CharField(choices=[('individual', 'Individual Seller'), ('business', 'Business'), ('cooperative', 'Cooperative'), ('government', 'Government Entity'), ('ngo', 'NGO/Non-Profit')], default='individual', help_text='Type of vendor', max_length=20)),
                ('business_email', models.EmailField(blank=True, help_text='Business email address', max_length=254)),
                ('business_phone', models.CharField(blank=True, help_text='Business phone number', max_length=20)),
                ('island', models.CharField(blank=True, choices=[('mahe', 'Mahé'), ('praslin', 'Praslin'), ('la_digue', 'La Digue'), ('silhouette', 'Silhouette'), ('fregate', 'Fregate'), ('bird', 'Bird Island'), ('denis', 'Denis Island'), ('other', 'Other')], help_text='Island location', max_length=20)),
                ('district', models.CharField(blank=True, help_text='District or area', max_length=100)),
                ('address', models.TextField(blank=True, help_text='Full business address')),
                ('description', models.TextField(blank=True, help_text='Business description')),
                ('logo', models.ImageField(blank=True, help_text='Business logo', null=True, upload_to='shopping/vendors/logos/')),
                ('banner', models.ImageField(blank=True, help_text='Business banner image', null=True, upload_to='shopping/vendors/banners/')),
                ('is_verified', models.BooleanField(default=False, help_text='Whether the vendor is verified')),
                ('is_active', models.BooleanField(default=True, help_text='Whether the vendor account is active')),
                ('is_featured', models.BooleanField(default=False, help_text='Featured vendor status')),
                ('business_hours', models.TextField(blank=True, help_text='Business operating hours')),
                ('total_sales', models.DecimalField(decimal_places=2, default=0.0, help_text='Total sales amount', max_digits=10)),
                ('total_orders', models.PositiveIntegerField(default=0, help_text='Total number of orders')),
                ('average_rating', models.DecimalField(decimal_places=2, default=0.0, help_text='Average vendor rating', max_digits=3)),
                ('rating_count', models.PositiveIntegerField(default=0, help_text='Number of ratings')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='vendor_profile', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Vendor',
                'verbose_name_plural': 'Vendors',
                'db_table': 'shopping_vendor',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='ShoppingCart',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='shopping_cart', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Shopping Cart',
                'verbose_name_plural': 'Shopping Carts',
                'db_table': 'shopping_shoppingcart',
            },
        ),
        migrations.CreateModel(
            name='ProductImage',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('image', models.ImageField(help_text='Product image', upload_to='shopping/products/gallery/')),
                ('alt_text', models.CharField(blank=True, help_text='Alternative text for accessibility', max_length=200)),
                ('display_order', models.PositiveIntegerField(default=1, help_text='Display order')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('product', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='images', to='shopping.product')),
            ],
            options={
                'verbose_name': 'Product Image',
                'verbose_name_plural': 'Product Images',
                'db_table': 'shopping_productimage',
                'ordering': ['display_order', 'created_at'],
            },
        ),
        migrations.CreateModel(
            name='ProductCategory',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(help_text='Category name', max_length=100, unique=True)),
                ('slug', models.SlugField(help_text='URL-friendly category name', max_length=100, unique=True)),
                ('description', models.TextField(blank=True, help_text='Category description')),
                ('icon', models.ImageField(blank=True, help_text='Category icon', null=True, upload_to='shopping/categories/icons/')),
                ('banner_image', models.ImageField(blank=True, help_text='Category banner image', null=True, upload_to='shopping/categories/banners/')),
                ('is_local_category', models.BooleanField(default=False, help_text='Whether this category focuses on Seychelles products')),
                ('is_active', models.BooleanField(default=True, help_text='Whether this category is active')),
                ('is_featured', models.BooleanField(default=False, help_text='Show in featured categories')),
                ('display_order', models.PositiveIntegerField(default=1, help_text='Display order')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('parent', models.ForeignKey(blank=True, help_text='Parent category for hierarchical organization', null=True, on_delete=django.db.models.deletion.CASCADE, related_name='subcategories', to='shopping.productcategory')),
            ],
            options={
                'verbose_name': 'Product Category',
                'verbose_name_plural': 'Product Categories',
                'db_table': 'shopping_productcategory',
                'ordering': ['display_order', 'name'],
            },
        ),
        migrations.AddField(
            model_name='product',
            name='category',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='products', to='shopping.productcategory'),
        ),
        migrations.AddField(
            model_name='product',
            name='vendor',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='products', to='shopping.vendor'),
        ),
        migrations.CreateModel(
            name='OrderItem',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('product_name', models.CharField(help_text='Product name at time of order', max_length=200)),
                ('product_price', models.DecimalField(decimal_places=2, help_text='Product price at time of order', max_digits=10)),
                ('quantity', models.PositiveIntegerField(help_text='Quantity ordered')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('order', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='items', to='shopping.order')),
                ('product', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='order_items', to='shopping.product')),
                ('vendor', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='order_items', to='shopping.vendor')),
            ],
            options={
                'verbose_name': 'Order Item',
                'verbose_name_plural': 'Order Items',
                'db_table': 'shopping_orderitem',
                'ordering': ['created_at'],
            },
        ),
        migrations.CreateModel(
            name='CartItem',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('quantity', models.PositiveIntegerField(default=1, help_text='Quantity of the product', validators=[django.core.validators.MinValueValidator(1)])),
                ('added_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('cart', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='items', to='shopping.shoppingcart')),
                ('product', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='cart_items', to='shopping.product')),
            ],
            options={
                'verbose_name': 'Cart Item',
                'verbose_name_plural': 'Cart Items',
                'db_table': 'shopping_cartitem',
                'ordering': ['-added_at'],
            },
        ),
        migrations.CreateModel(
            name='WishlistItem',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('added_at', models.DateTimeField(auto_now_add=True)),
                ('product', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='wishlist_items', to='shopping.product')),
                ('wishlist', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='items', to='shopping.wishlist')),
            ],
            options={
                'verbose_name': 'Wishlist Item',
                'verbose_name_plural': 'Wishlist Items',
                'db_table': 'shopping_wishlistitem',
                'ordering': ['-added_at'],
                'unique_together': {('wishlist', 'product')},
            },
        ),
        migrations.CreateModel(
            name='ProductReview',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('rating', models.PositiveIntegerField(help_text='Rating from 1 to 5 stars', validators=[django.core.validators.MinValueValidator(1), django.core.validators.MaxValueValidator(5)])),
                ('title', models.CharField(help_text='Review title', max_length=200)),
                ('content', models.TextField(help_text='Review content')),
                ('image1', models.ImageField(blank=True, help_text='Review image 1', null=True, upload_to='shopping/reviews/')),
                ('image2', models.ImageField(blank=True, help_text='Review image 2', null=True, upload_to='shopping/reviews/')),
                ('image3', models.ImageField(blank=True, help_text='Review image 3', null=True, upload_to='shopping/reviews/')),
                ('is_approved', models.BooleanField(default=True, help_text='Whether the review is approved for display')),
                ('is_verified_purchase', models.BooleanField(default=False, help_text='Whether this is from a verified purchase')),
                ('helpful_count', models.PositiveIntegerField(default=0, help_text='Number of users who found this helpful')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('customer', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='product_reviews', to=settings.AUTH_USER_MODEL)),
                ('order_item', models.ForeignKey(blank=True, help_text='Order item this review is for (if applicable)', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='review', to='shopping.orderitem')),
                ('product', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='reviews', to='shopping.product')),
            ],
            options={
                'verbose_name': 'Product Review',
                'verbose_name_plural': 'Product Reviews',
                'db_table': 'shopping_productreview',
                'ordering': ['-created_at'],
                'unique_together': {('product', 'customer')},
            },
        ),
        migrations.AddIndex(
            model_name='product',
            index=models.Index(fields=['category', '-created_at'], name='shopping_pr_categor_f53551_idx'),
        ),
        migrations.AddIndex(
            model_name='product',
            index=models.Index(fields=['vendor', '-created_at'], name='shopping_pr_vendor__d9d881_idx'),
        ),
        migrations.AddIndex(
            model_name='product',
            index=models.Index(fields=['is_featured', '-created_at'], name='shopping_pr_is_feat_f9633c_idx'),
        ),
        migrations.AddIndex(
            model_name='product',
            index=models.Index(fields=['is_local_product', '-created_at'], name='shopping_pr_is_loca_38459f_idx'),
        ),
        migrations.AddIndex(
            model_name='order',
            index=models.Index(fields=['customer', '-created_at'], name='shopping_or_custome_3771be_idx'),
        ),
        migrations.AddIndex(
            model_name='order',
            index=models.Index(fields=['status', '-created_at'], name='shopping_or_status_f11b49_idx'),
        ),
        migrations.AddIndex(
            model_name='order',
            index=models.Index(fields=['payment_status', '-created_at'], name='shopping_or_payment_e5a2bd_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='cartitem',
            unique_together={('cart', 'product')},
        ),
    ]
