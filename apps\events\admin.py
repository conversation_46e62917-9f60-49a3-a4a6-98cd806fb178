"""
Admin configuration for events app.

Provides comprehensive admin interfaces for managing events, venues, organizers,
registrations, tickets, and reviews.
"""
from django.contrib import admin
from django.utils.html import format_html
from django.urls import reverse
from django.utils import timezone
from .models import (
    EventCategory, Venue, EventOrganizer, Event, EventRegistration,
    EventTicket, EventImage, EventReview
)


@admin.register(EventCategory)
class EventCategoryAdmin(admin.ModelAdmin):
    """Admin interface for Event Categories."""
    list_display = ['name', 'name_fr', 'name_cr', 'color_display', 'is_active', 'sort_order', 'event_count']
    list_filter = ['is_active', 'created_at']
    search_fields = ['name', 'name_fr', 'name_cr', 'description']
    prepopulated_fields = {'slug': ('name',)}
    ordering = ['sort_order', 'name']

    fieldsets = (
        ('Basic Information', {
            'fields': ('name', 'slug', 'description', 'icon', 'color')
        }),
        ('Multi-language', {
            'fields': ('name_fr', 'name_cr'),
            'classes': ('collapse',)
        }),
        ('Settings', {
            'fields': ('is_active', 'sort_order')
        }),
    )

    def color_display(self, obj):
        """Display color as a colored box."""
        return format_html(
            '<div style="width: 20px; height: 20px; background-color: {}; border: 1px solid #ccc;"></div>',
            obj.color
        )
    color_display.short_description = 'Color'

    def event_count(self, obj):
        """Display number of events in this category."""
        return obj.events.count()
    event_count.short_description = 'Events'


@admin.register(Venue)
class VenueAdmin(admin.ModelAdmin):
    """Admin interface for Venues."""
    list_display = ['name', 'venue_type', 'island', 'district', 'capacity', 'is_active', 'is_verified', 'total_events']
    list_filter = ['venue_type', 'island', 'is_active', 'is_verified', 'has_parking', 'has_accessibility']
    search_fields = ['name', 'address', 'district', 'contact_name', 'contact_email']
    prepopulated_fields = {'slug': ('name',)}
    ordering = ['name']

    fieldsets = (
        ('Basic Information', {
            'fields': ('name', 'slug', 'description', 'venue_type', 'image')
        }),
        ('Location', {
            'fields': ('address', 'island', 'district', 'latitude', 'longitude')
        }),
        ('Capacity & Features', {
            'fields': ('capacity', 'has_parking', 'has_accessibility', 'has_wifi', 'has_catering')
        }),
        ('Contact Information', {
            'fields': ('contact_name', 'contact_phone', 'contact_email', 'website')
        }),
        ('Status', {
            'fields': ('is_active', 'is_verified')
        }),
        ('Statistics', {
            'fields': ('total_events', 'average_rating'),
            'classes': ('collapse',)
        }),
    )

    actions = ['mark_verified', 'mark_unverified']

    def mark_verified(self, request, queryset):
        """Mark selected venues as verified."""
        queryset.update(is_verified=True)
    mark_verified.short_description = "Mark selected venues as verified"

    def mark_unverified(self, request, queryset):
        """Mark selected venues as unverified."""
        queryset.update(is_verified=False)
    mark_unverified.short_description = "Mark selected venues as unverified"


@admin.register(EventOrganizer)
class EventOrganizerAdmin(admin.ModelAdmin):
    """Admin interface for Event Organizers."""
    list_display = ['user', 'organization_name', 'organizer_type', 'is_verified', 'total_events', 'total_attendees']
    list_filter = ['organizer_type', 'is_verified', 'created_at']
    search_fields = ['user__username', 'user__email', 'organization_name', 'contact_email']
    ordering = ['-total_events', 'organization_name']

    fieldsets = (
        ('Basic Information', {
            'fields': ('user', 'organization_name', 'organizer_type', 'description')
        }),
        ('Contact Information', {
            'fields': ('contact_phone', 'contact_email', 'website')
        }),
        ('Social Media', {
            'fields': ('facebook_url', 'instagram_url', 'twitter_url'),
            'classes': ('collapse',)
        }),
        ('Verification', {
            'fields': ('is_verified', 'verification_documents')
        }),
        ('Statistics', {
            'fields': ('total_events', 'total_attendees', 'average_rating'),
            'classes': ('collapse',)
        }),
    )

    actions = ['mark_verified', 'mark_unverified']

    def mark_verified(self, request, queryset):
        """Mark selected organizers as verified."""
        queryset.update(is_verified=True)
    mark_verified.short_description = "Mark selected organizers as verified"

    def mark_unverified(self, request, queryset):
        """Mark selected organizers as unverified."""
        queryset.update(is_verified=False)
    mark_unverified.short_description = "Mark selected organizers as unverified"


class EventImageInline(admin.TabularInline):
    """Inline admin for Event Images."""
    model = EventImage
    extra = 1
    fields = ['image', 'caption', 'alt_text', 'sort_order', 'is_featured']


class EventTicketInline(admin.TabularInline):
    """Inline admin for Event Tickets."""
    model = EventTicket
    extra = 1
    fields = ['name', 'ticket_type', 'price', 'quantity_available', 'is_active']


@admin.register(Event)
class EventAdmin(admin.ModelAdmin):
    """Admin interface for Events."""
    list_display = [
        'title', 'category', 'organizer', 'venue', 'start_date', 'start_time',
        'status', 'pricing_type', 'price', 'is_featured', 'total_registrations'
    ]
    list_filter = [
        'status', 'category', 'pricing_type', 'event_type', 'is_featured',
        'is_local_event', 'supports_local_culture', 'start_date'
    ]
    search_fields = ['title', 'title_fr', 'title_cr', 'description', 'tags']
    prepopulated_fields = {'slug': ('title',)}
    date_hierarchy = 'start_date'
    ordering = ['-start_date', '-start_time']
    inlines = [EventImageInline, EventTicketInline]

    fieldsets = (
        ('Basic Information', {
            'fields': ('title', 'slug', 'description', 'short_description', 'featured_image')
        }),
        ('Multi-language', {
            'fields': ('title_fr', 'title_cr'),
            'classes': ('collapse',)
        }),
        ('Categorization', {
            'fields': ('category', 'tags')
        }),
        ('Organization', {
            'fields': ('organizer', 'venue')
        }),
        ('Date & Time', {
            'fields': ('start_date', 'start_time', 'end_date', 'end_time', 'timezone')
        }),
        ('Registration', {
            'fields': (
                'event_type', 'registration_required', 'max_attendees',
                'registration_deadline', 'allow_waitlist'
            )
        }),
        ('Pricing', {
            'fields': ('pricing_type', 'price', 'currency')
        }),
        ('Status & Visibility', {
            'fields': ('status', 'is_featured', 'is_recurring')
        }),
        ('Seychelles Features', {
            'fields': ('is_local_event', 'supports_local_culture'),
            'classes': ('collapse',)
        }),
        ('Requirements', {
            'fields': ('age_restriction', 'dress_code', 'special_requirements'),
            'classes': ('collapse',)
        }),
        ('Contact', {
            'fields': ('contact_email', 'contact_phone'),
            'classes': ('collapse',)
        }),
        ('SEO', {
            'fields': ('meta_title', 'meta_description'),
            'classes': ('collapse',)
        }),
        ('Statistics', {
            'fields': (
                'total_registrations', 'total_attendees', 'total_views',
                'average_rating', 'total_reviews'
            ),
            'classes': ('collapse',)
        }),
    )

    actions = ['mark_featured', 'mark_unfeatured', 'publish_events', 'cancel_events']

    def mark_featured(self, request, queryset):
        """Mark selected events as featured."""
        queryset.update(is_featured=True)
    mark_featured.short_description = "Mark selected events as featured"

    def mark_unfeatured(self, request, queryset):
        """Remove featured status from selected events."""
        queryset.update(is_featured=False)
    mark_unfeatured.short_description = "Remove featured status"

    def publish_events(self, request, queryset):
        """Publish selected events."""
        queryset.update(status='published', published_at=timezone.now())
    publish_events.short_description = "Publish selected events"

    def cancel_events(self, request, queryset):
        """Cancel selected events."""
        queryset.update(status='cancelled')
    cancel_events.short_description = "Cancel selected events"


@admin.register(EventRegistration)
class EventRegistrationAdmin(admin.ModelAdmin):
    """Admin interface for Event Registrations."""
    list_display = [
        'user', 'event', 'status', 'number_of_attendees', 'registration_date',
        'payment_status', 'amount_paid', 'checked_in'
    ]
    list_filter = ['status', 'payment_status', 'checked_in', 'registration_date']
    search_fields = ['user__username', 'user__email', 'event__title', 'contact_phone']
    date_hierarchy = 'registration_date'
    ordering = ['-registration_date']

    fieldsets = (
        ('Registration Details', {
            'fields': ('event', 'user', 'status', 'registration_date')
        }),
        ('Attendees', {
            'fields': ('number_of_attendees', 'attendee_names')
        }),
        ('Contact', {
            'fields': ('contact_phone', 'special_requirements')
        }),
        ('Payment', {
            'fields': ('amount_paid', 'payment_status', 'payment_reference')
        }),
        ('Check-in', {
            'fields': ('checked_in', 'check_in_time')
        }),
    )

    actions = ['confirm_registrations', 'check_in_attendees']

    def confirm_registrations(self, request, queryset):
        """Confirm selected registrations."""
        queryset.update(status='confirmed')
    confirm_registrations.short_description = "Confirm selected registrations"

    def check_in_attendees(self, request, queryset):
        """Check in selected attendees."""
        queryset.update(checked_in=True, check_in_time=timezone.now())
    check_in_attendees.short_description = "Check in selected attendees"


@admin.register(EventTicket)
class EventTicketAdmin(admin.ModelAdmin):
    """Admin interface for Event Tickets."""
    list_display = [
        'event', 'name', 'ticket_type', 'price', 'quantity_available',
        'quantity_sold', 'remaining_quantity', 'is_active'
    ]
    list_filter = ['ticket_type', 'is_active', 'created_at']
    search_fields = ['event__title', 'name', 'description']
    ordering = ['event', 'price']

    fieldsets = (
        ('Basic Information', {
            'fields': ('event', 'name', 'description', 'ticket_type')
        }),
        ('Pricing', {
            'fields': ('price', 'currency')
        }),
        ('Availability', {
            'fields': ('quantity_available', 'quantity_sold')
        }),
        ('Sale Period', {
            'fields': ('sale_start_date', 'sale_end_date')
        }),
        ('Settings', {
            'fields': ('is_active', 'min_quantity', 'max_quantity')
        }),
    )

    def remaining_quantity(self, obj):
        """Display remaining ticket quantity."""
        return obj.remaining_quantity
    remaining_quantity.short_description = 'Remaining'


@admin.register(EventImage)
class EventImageAdmin(admin.ModelAdmin):
    """Admin interface for Event Images."""
    list_display = ['event', 'caption', 'sort_order', 'is_featured', 'created_at']
    list_filter = ['is_featured', 'created_at']
    search_fields = ['event__title', 'caption', 'alt_text']
    ordering = ['event', 'sort_order']

    fieldsets = (
        ('Image Details', {
            'fields': ('event', 'image', 'caption', 'alt_text')
        }),
        ('Settings', {
            'fields': ('sort_order', 'is_featured')
        }),
    )


@admin.register(EventReview)
class EventReviewAdmin(admin.ModelAdmin):
    """Admin interface for Event Reviews."""
    list_display = [
        'user', 'event', 'rating', 'title', 'is_verified', 'is_approved',
        'helpful_votes', 'created_at'
    ]
    list_filter = ['rating', 'is_verified', 'is_approved', 'created_at']
    search_fields = ['user__username', 'event__title', 'title', 'content']
    ordering = ['-created_at']

    fieldsets = (
        ('Review Details', {
            'fields': ('event', 'user', 'registration', 'rating', 'title', 'content')
        }),
        ('Category Ratings', {
            'fields': ('organization_rating', 'venue_rating', 'value_rating'),
            'classes': ('collapse',)
        }),
        ('Verification & Moderation', {
            'fields': ('is_verified', 'is_approved', 'moderated_by', 'moderation_notes')
        }),
        ('Engagement', {
            'fields': ('helpful_votes',)
        }),
    )

    actions = ['approve_reviews', 'disapprove_reviews', 'mark_verified']

    def approve_reviews(self, request, queryset):
        """Approve selected reviews."""
        queryset.update(is_approved=True, moderated_by=request.user)
    approve_reviews.short_description = "Approve selected reviews"

    def disapprove_reviews(self, request, queryset):
        """Disapprove selected reviews."""
        queryset.update(is_approved=False, moderated_by=request.user)
    disapprove_reviews.short_description = "Disapprove selected reviews"

    def mark_verified(self, request, queryset):
        """Mark selected reviews as verified."""
        queryset.update(is_verified=True)
    mark_verified.short_description = "Mark selected reviews as verified"
