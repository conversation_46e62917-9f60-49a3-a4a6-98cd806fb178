import Link from "next/link"
import Image from "next/image"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Search, Calendar, MapPin, Clock, Filter, Plus, Users, Heart } from "lucide-react"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ist, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { UserNav } from "@/components/user-nav"
import FeatureNavigation from "@/components/feature-navigation"
import QuickAccessSection from "@/components/quick-access-section"

export default function UpcomingEventsPage() {
  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="sticky top-0 z-50 w-full bg-white border-b shadow-sm">
        <div className="container flex h-16 items-center justify-between px-4">
          <div className="flex items-center gap-8">
            <Link href="/" className="flex items-center space-x-2">
              <span className="text-2xl font-bold bg-gradient-to-r from-blue-600 to-teal-500 bg-clip-text text-transparent">
                Kominote
              </span>
            </Link>
            <div className="hidden md:flex relative w-full max-w-md">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <Input
                type="search"
                placeholder="Search events, venues, categories..."
                className="pl-10 pr-4 py-2 w-full border-amber-200 focus:border-amber-500"
              />
            </div>
          </div>
          <div className="flex items-center gap-4">
            <Button variant="ghost" size="icon" className="text-gray-600 hover:text-amber-600">
              <Heart className="h-5 w-5" />
              <span className="sr-only">Saved Events</span>
            </Button>
            <Button variant="ghost" size="icon" className="text-gray-600 hover:text-amber-600">
              <Calendar className="h-5 w-5" />
              <span className="sr-only">My Calendar</span>
            </Button>
            <UserNav />
          </div>
        </div>
        <div className="container px-4 py-2 border-t border-gray-100">
          <nav className="flex items-center gap-6 overflow-x-auto pb-2 md:pb-0">
            <Link href="/events" className="text-sm font-medium whitespace-nowrap text-gray-600 hover:text-amber-600">
              Home
            </Link>
            <Link
              href="/events/upcoming"
              className="text-sm font-medium whitespace-nowrap text-amber-600 border-b-2 border-amber-600 pb-1"
            >
              Upcoming Events
            </Link>
            <Link
              href="/events/categories"
              className="text-sm font-medium whitespace-nowrap text-gray-600 hover:text-amber-600"
            >
              Categories
            </Link>
            <Link
              href="/events/venues"
              className="text-sm font-medium whitespace-nowrap text-gray-600 hover:text-amber-600"
            >
              Venues
            </Link>
            <Link
              href="/events/my-events"
              className="text-sm font-medium whitespace-nowrap text-gray-600 hover:text-amber-600"
            >
              My Events
            </Link>
            <Link
              href="/events/create"
              className="text-sm font-medium whitespace-nowrap text-gray-600 hover:text-amber-600"
            >
              Create Event
            </Link>
          </nav>
        </div>
      </header>

      {/* Quick Access Section */}
      <QuickAccessSection
        currentFeature="events"
        colorScheme="amber"
        title="Quick Access"
        description="Jump to other Kominote features"
      />

      <main className="container px-4 py-8">
        {/* Events Header */}
        <div className="flex flex-col md:flex-row md:items-center justify-between gap-4 mb-8">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Upcoming Events</h1>
            <p className="text-gray-600 mt-1">Discover exciting events happening in Seychelles</p>
          </div>
          <div className="flex items-center gap-3">
            <Button variant="outline" size="sm" className="gap-2 border-amber-200 text-amber-600 hover:bg-amber-50">
              <Filter className="h-4 w-4" /> Filter
            </Button>
            <Button className="bg-amber-600 hover:bg-amber-700 gap-2">
              <Plus className="h-4 w-4" /> Create Event
            </Button>
          </div>
        </div>

        {/* Search and Filter */}
        <div className="bg-white rounded-lg shadow-sm p-4 mb-8 border border-gray-200">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="relative col-span-2">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <Input
                type="search"
                placeholder="Search events..."
                className="pl-10 pr-4 py-2 w-full border-gray-200 focus:border-amber-500"
              />
            </div>
            <div>
              <select className="w-full text-sm border rounded-md px-2 py-2 bg-white">
                <option>All Categories</option>
                <option>Cultural</option>
                <option>Music & Entertainment</option>
                <option>Food & Drink</option>
                <option>Sports & Fitness</option>
                <option>Business & Networking</option>
              </select>
            </div>
            <div>
              <select className="w-full text-sm border rounded-md px-2 py-2 bg-white">
                <option>All Locations</option>
                <option>Mahé</option>
                <option>Praslin</option>
                <option>La Digue</option>
                <option>Other Islands</option>
              </select>
            </div>
          </div>
        </div>

        {/* Events Tabs */}
        <Tabs defaultValue="all" className="w-full mb-8">
          <TabsList className="bg-white border border-gray-200 p-1 rounded-lg">
            <TabsTrigger value="all" className="data-[state=active]:bg-amber-600 data-[state=active]:text-white">
              All Events
            </TabsTrigger>
            <TabsTrigger value="today" className="data-[state=active]:bg-amber-600 data-[state=active]:text-white">
              Today
            </TabsTrigger>
            <TabsTrigger value="weekend" className="data-[state=active]:bg-amber-600 data-[state=active]:text-white">
              This Weekend
            </TabsTrigger>
            <TabsTrigger value="month" className="data-[state=active]:bg-amber-600 data-[state=active]:text-white">
              This Month
            </TabsTrigger>
          </TabsList>

          <TabsContent value="all" className="mt-6">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {[1, 2, 3, 4, 5, 6, 7, 8, 9].map((item) => (
                <EventCard key={item} />
              ))}
            </div>
          </TabsContent>

          <TabsContent value="today" className="mt-6">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {[1, 2, 3].map((item) => (
                <EventCard key={item} />
              ))}
            </div>
          </TabsContent>

          <TabsContent value="weekend" className="mt-6">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {[1, 2, 3, 4, 5].map((item) => (
                <EventCard key={item} />
              ))}
            </div>
          </TabsContent>

          <TabsContent value="month" className="mt-6">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {[1, 2, 3, 4, 5, 6, 7, 8, 9].map((item) => (
                <EventCard key={item} />
              ))}
            </div>
          </TabsContent>
        </Tabs>

        {/* Load More */}
        <div className="flex justify-center mt-8">
          <Button variant="outline" className="border-amber-200 text-amber-600 hover:bg-amber-50">
            Load More Events
          </Button>
        </div>
      </main>

      {/* Footer */}
      <footer className="bg-white border-t py-12 mt-12">
        <div className="container px-4 md:px-6">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            <div className="space-y-4">
              <Link href="/" className="flex items-center space-x-2">
                <span className="text-2xl font-bold bg-gradient-to-r from-blue-600 to-teal-500 bg-clip-text text-transparent">
                  Kominote
                </span>
              </Link>
              <p className="text-sm text-gray-600">
                Seychelles' premier events platform connecting people with local happenings.
              </p>
            </div>
            <div className="space-y-4">
              <h3 className="font-medium">Events</h3>
              <ul className="space-y-2">
                <li>
                  <Link href="/events/upcoming" className="text-sm text-gray-600 hover:text-amber-600">
                    Upcoming Events
                  </Link>
                </li>
                <li>
                  <Link href="/events/categories" className="text-sm text-gray-600 hover:text-amber-600">
                    Categories
                  </Link>
                </li>
                <li>
                  <Link href="/events/venues" className="text-sm text-gray-600 hover:text-amber-600">
                    Venues
                  </Link>
                </li>
                <li>
                  <Link href="/events/create" className="text-sm text-gray-600 hover:text-amber-600">
                    Create Event
                  </Link>
                </li>
              </ul>
            </div>
            <div className="space-y-4">
              <h3 className="font-medium">Organizers</h3>
              <ul className="space-y-2">
                <li>
                  <Link href="#" className="text-sm text-gray-600 hover:text-amber-600">
                    Become an Organizer
                  </Link>
                </li>
                <li>
                  <Link href="#" className="text-sm text-gray-600 hover:text-amber-600">
                    Organizer Resources
                  </Link>
                </li>
                <li>
                  <Link href="#" className="text-sm text-gray-600 hover:text-amber-600">
                    Success Stories
                  </Link>
                </li>
                <li>
                  <Link href="#" className="text-sm text-gray-600 hover:text-amber-600">
                    Organizer Directory
                  </Link>
                </li>
              </ul>
            </div>
            <div className="space-y-4">
              <h3 className="font-medium">Help & Support</h3>
              <ul className="space-y-2">
                <li>
                  <Link href="#" className="text-sm text-gray-600 hover:text-amber-600">
                    FAQ
                  </Link>
                </li>
                <li>
                  <Link href="#" className="text-sm text-gray-600 hover:text-amber-600">
                    Contact Us
                  </Link>
                </li>
                <li>
                  <Link href="#" className="text-sm text-gray-600 hover:text-amber-600">
                    Terms & Conditions
                  </Link>
                </li>
                <li>
                  <Link href="#" className="text-sm text-gray-600 hover:text-amber-600">
                    Privacy Policy
                  </Link>
                </li>
              </ul>
            </div>
          </div>
          <div className="border-t mt-12 pt-8 flex flex-col md:flex-row justify-between items-center">
            <p className="text-sm text-gray-600">
              &copy; {new Date().getFullYear()} Kominote Events. All rights reserved.
            </p>
            <div className="flex items-center gap-4 mt-4 md:mt-0">
              <Link href="#" className="text-gray-600 hover:text-amber-600">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="24"
                  height="24"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  className="h-5 w-5"
                >
                  <path d="M18 2h-3a5 5 0 0 0-5 5v3H7v4h3v8h4v-8h3l1-4h-4V7a1 1 0 0 1 1-1h3z" />
                </svg>
              </Link>
              <Link href="#" className="text-gray-600 hover:text-amber-600">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="24"
                  height="24"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  className="h-5 w-5"
                >
                  <rect width="20" height="20" x="2" y="2" rx="5" ry="5" />
                  <path d="M16 11.37A4 4 0 1 1 12.63 8 4 4 0 0 1 16 11.37z" />
                  <line x1="17.5" x2="17.51" y1="6.5" y2="6.5" />
                </svg>
              </Link>
              <Link href="#" className="text-gray-600 hover:text-amber-600">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="24"
                  height="24"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  className="h-5 w-5"
                >
                  <path d="M22 4s-.7 2.1-2 3.4c1.6 10-9.4 17.3-18 11.6 2.2.1 4.4-.6 6-2C3 15.5.5 9.6 3 5c2.2 2.6 5.6 4.1 9 4-.9-4.2 4-6.6 7-3.8 1.1 0 3-1.2 3-1.2z" />
                </svg>
              </Link>
            </div>
          </div>
        </div>
      </footer>

      {/* Side Navigation */}
      <FeatureNavigation currentFeature="events" colorScheme="amber" prominentSectionHeight={300} />
    </div>
  )
}

// Event Card Component
function EventCard() {
  return (
    <Link href="/events/cultural-festival-2023" className="group">
      <div className="bg-white rounded-lg shadow-sm overflow-hidden transition-all duration-200 hover:shadow-md border border-transparent hover:border-amber-200">
        <div className="relative">
          <div className="aspect-[16/9] overflow-hidden">
            <Image
              src="/placeholder.svg?height=225&width=400"
              width={400}
              height={225}
              alt="Event image"
              className="object-cover w-full h-full group-hover:scale-105 transition-transform duration-300"
            />
          </div>
          <Badge className="absolute top-2 left-2 bg-amber-600">Featured</Badge>
          <Button
            variant="ghost"
            size="icon"
            className="absolute top-2 right-2 h-8 w-8 rounded-full bg-white/80 text-gray-600 hover:text-amber-600 hover:bg-white"
          >
            <Heart className="h-4 w-4" />
          </Button>
        </div>
        <div className="p-4">
          <div className="flex items-center gap-2 mb-2">
            <Badge variant="outline" className="text-xs bg-amber-50 text-amber-700 border-amber-200">
              Cultural
            </Badge>
            <Badge variant="outline" className="text-xs bg-blue-50 text-blue-700 border-blue-200">
              Festival
            </Badge>
          </div>
          <h3 className="font-bold text-lg group-hover:text-amber-600 transition-colors">Cultural Festival 2023</h3>
          <div className="flex items-center gap-1 mt-2 text-gray-500 text-sm">
            <Calendar className="h-4 w-4" />
            <span>Sat, Oct 15, 2023</span>
          </div>
          <div className="flex items-center gap-1 mt-1 text-gray-500 text-sm">
            <Clock className="h-4 w-4" />
            <span>10:00 AM - 6:00 PM</span>
          </div>
          <div className="flex items-center gap-1 mt-1 text-gray-500 text-sm">
            <MapPin className="h-4 w-4" />
            <span>Victoria, Mahé</span>
          </div>
          <div className="flex items-center gap-1 mt-1 text-gray-500 text-sm">
            <Users className="h-4 w-4" />
            <span>120 attending</span>
          </div>
          <div className="mt-4 flex items-center justify-between">
            <span className="font-bold text-amber-600">SCR 150.00</span>
            <Button size="sm" className="bg-amber-600 hover:bg-amber-700">
              Get Tickets
            </Button>
          </div>
        </div>
      </div>
    </Link>
  )
}

