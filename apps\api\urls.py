"""
URL configuration for api app.

Centralized API endpoints and versioning for the Kominote platform.
"""
from django.urls import path, include
from . import views

app_name = 'api'

urlpatterns = [
    # API root and documentation
    path('', views.APIRootView.as_view(), name='api-root'),
    path('health/', views.HealthCheckView.as_view(), name='health-check'),
    path('version/', views.APIVersionView.as_view(), name='api-version'),
    
    # API v1 endpoints - include all app APIs
    path('v1/accounts/', include('apps.accounts.urls')),
    path('v1/streaming/', include('apps.streaming.urls')),
    path('v1/shopping/', include('apps.shopping.urls')),
    path('v1/community/', include('apps.community.urls')),
    path('v1/events/', include('apps.events.urls')),
    path('v1/jobs/', include('apps.jobs.urls')),
    
    # Aggregated endpoints
    path('search/', views.GlobalSearchView.as_view(), name='global-search'),
    path('dashboard/', views.DashboardDataView.as_view(), name='dashboard-data'),
    path('notifications/', views.NotificationListView.as_view(), name='notifications'),
    path('analytics/', views.AnalyticsView.as_view(), name='analytics'),
    
    # Mobile app specific endpoints
    path('mobile/config/', views.MobileConfigView.as_view(), name='mobile-config'),
    path('mobile/sync/', views.MobileSyncView.as_view(), name='mobile-sync'),
]
