# Generated by Django 4.2.21 on 2025-05-30 10:51

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Comment',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('content', models.TextField(help_text='Comment content')),
                ('status', models.CharField(choices=[('published', 'Published'), ('pending', 'Pending Approval'), ('rejected', 'Rejected'), ('hidden', 'Hidden')], default='published', help_text='Comment status', max_length=20)),
                ('like_count', models.PositiveIntegerField(default=0, help_text='Number of likes')),
                ('created_at', models.DateTime<PERSON>ield(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('author', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='community_comments', to=settings.AUTH_USER_MODEL)),
                ('parent', models.ForeignKey(blank=True, help_text='Parent comment for threaded discussions', null=True, on_delete=django.db.models.deletion.CASCADE, related_name='replies', to='community.comment')),
            ],
            options={
                'verbose_name': 'Comment',
                'verbose_name_plural': 'Comments',
                'db_table': 'community_comment',
                'ordering': ['created_at'],
            },
        ),
        migrations.CreateModel(
            name='ForumCategory',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(help_text='Category name', max_length=100, unique=True)),
                ('slug', models.SlugField(help_text='URL-friendly category name', max_length=100, unique=True)),
                ('description', models.TextField(blank=True, help_text='Category description')),
                ('icon', models.CharField(blank=True, help_text="Icon class name (e.g., 'fas fa-comments')", max_length=50)),
                ('color', models.CharField(default='#3B82F6', help_text='Category color (hex code)', max_length=7)),
                ('is_local_category', models.BooleanField(default=False, help_text='Whether this category focuses on Seychelles topics')),
                ('is_active', models.BooleanField(default=True, help_text='Whether this category is active')),
                ('is_featured', models.BooleanField(default=False, help_text='Show in featured categories')),
                ('display_order', models.PositiveIntegerField(default=1, help_text='Display order')),
                ('requires_approval', models.BooleanField(default=False, help_text='Whether posts in this category require approval')),
                ('post_count', models.PositiveIntegerField(default=0, help_text='Number of posts in this category')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'Forum Category',
                'verbose_name_plural': 'Forum Categories',
                'db_table': 'community_forumcategory',
                'ordering': ['display_order', 'name'],
            },
        ),
        migrations.CreateModel(
            name='Post',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(help_text='Post title', max_length=200)),
                ('slug', models.SlugField(help_text='URL-friendly post title', max_length=200, unique=True)),
                ('content', models.TextField(help_text='Post content')),
                ('excerpt', models.CharField(blank=True, help_text='Short excerpt for previews', max_length=300)),
                ('post_type', models.CharField(choices=[('discussion', 'Discussion'), ('question', 'Question'), ('announcement', 'Announcement'), ('event', 'Event'), ('local_news', 'Local News'), ('recommendation', 'Recommendation')], default='discussion', help_text='Type of post', max_length=20)),
                ('status', models.CharField(choices=[('draft', 'Draft'), ('published', 'Published'), ('pending', 'Pending Approval'), ('rejected', 'Rejected'), ('archived', 'Archived')], default='published', help_text='Post status', max_length=20)),
                ('featured_image', models.ImageField(blank=True, help_text='Featured image for the post', null=True, upload_to='community/posts/')),
                ('location', models.CharField(blank=True, help_text='Seychelles location related to this post', max_length=100)),
                ('island', models.CharField(blank=True, choices=[('mahe', 'Mahé'), ('praslin', 'Praslin'), ('la_digue', 'La Digue'), ('silhouette', 'Silhouette'), ('fregate', 'Fregate'), ('bird', 'Bird Island'), ('denis', 'Denis Island'), ('other', 'Other')], help_text='Island related to this post', max_length=20)),
                ('is_local_content', models.BooleanField(default=False, help_text='Whether this post is about Seychelles')),
                ('is_pinned', models.BooleanField(default=False, help_text='Pin this post to the top')),
                ('is_featured', models.BooleanField(default=False, help_text='Feature this post')),
                ('allow_comments', models.BooleanField(default=True, help_text='Allow comments on this post')),
                ('view_count', models.PositiveIntegerField(default=0, help_text='Number of views')),
                ('like_count', models.PositiveIntegerField(default=0, help_text='Number of likes')),
                ('comment_count', models.PositiveIntegerField(default=0, help_text='Number of comments')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('published_at', models.DateTimeField(blank=True, help_text='When the post was published', null=True)),
                ('author', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='community_posts', to=settings.AUTH_USER_MODEL)),
                ('category', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='posts', to='community.forumcategory')),
            ],
            options={
                'verbose_name': 'Post',
                'verbose_name_plural': 'Posts',
                'db_table': 'community_post',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='Notification',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('notification_type', models.CharField(choices=[('follow', 'New Follower'), ('post_like', 'Post Liked'), ('comment_like', 'Comment Liked'), ('comment', 'New Comment'), ('reply', 'Comment Reply'), ('mention', 'Mentioned in Post/Comment'), ('post_featured', 'Post Featured')], help_text='Type of notification', max_length=20)),
                ('title', models.CharField(help_text='Notification title', max_length=200)),
                ('message', models.TextField(help_text='Notification message')),
                ('is_read', models.BooleanField(default=False, help_text='Whether the notification has been read')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('read_at', models.DateTimeField(blank=True, help_text='When the notification was read', null=True)),
                ('comment', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='notifications', to='community.comment')),
                ('post', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='notifications', to='community.post')),
                ('recipient', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='notifications', to=settings.AUTH_USER_MODEL)),
                ('sender', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='sent_notifications', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Notification',
                'verbose_name_plural': 'Notifications',
                'db_table': 'community_notification',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='CommunityProfile',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('bio', models.TextField(blank=True, help_text='Community bio', max_length=500)),
                ('website', models.URLField(blank=True, help_text='Personal website')),
                ('show_email', models.BooleanField(default=False, help_text='Show email address in profile')),
                ('show_location', models.BooleanField(default=True, help_text='Show location in profile')),
                ('allow_messages', models.BooleanField(default=True, help_text='Allow private messages')),
                ('notify_on_follow', models.BooleanField(default=True, help_text='Notify when someone follows you')),
                ('notify_on_comment', models.BooleanField(default=True, help_text='Notify when someone comments on your posts')),
                ('notify_on_like', models.BooleanField(default=True, help_text='Notify when someone likes your posts')),
                ('notify_on_mention', models.BooleanField(default=True, help_text='Notify when someone mentions you')),
                ('reputation_score', models.PositiveIntegerField(default=0, help_text='Community reputation score')),
                ('total_posts', models.PositiveIntegerField(default=0, help_text='Total number of posts')),
                ('total_comments', models.PositiveIntegerField(default=0, help_text='Total number of comments')),
                ('total_likes_received', models.PositiveIntegerField(default=0, help_text='Total likes received on posts and comments')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='community_profile', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Community Profile',
                'verbose_name_plural': 'Community Profiles',
                'db_table': 'community_communityprofile',
            },
        ),
        migrations.CreateModel(
            name='CommentLike',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('comment', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='likes', to='community.comment')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='comment_likes', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Comment Like',
                'verbose_name_plural': 'Comment Likes',
                'db_table': 'community_commentlike',
                'ordering': ['-created_at'],
            },
        ),
        migrations.AddField(
            model_name='comment',
            name='post',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='comments', to='community.post'),
        ),
        migrations.CreateModel(
            name='UserFollow',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('follower', models.ForeignKey(help_text='User who is following', on_delete=django.db.models.deletion.CASCADE, related_name='following', to=settings.AUTH_USER_MODEL)),
                ('following', models.ForeignKey(help_text='User being followed', on_delete=django.db.models.deletion.CASCADE, related_name='followers', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'User Follow',
                'verbose_name_plural': 'User Follows',
                'db_table': 'community_userfollow',
                'ordering': ['-created_at'],
                'unique_together': {('follower', 'following')},
            },
        ),
        migrations.CreateModel(
            name='Report',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('report_type', models.CharField(choices=[('spam', 'Spam'), ('harassment', 'Harassment'), ('inappropriate', 'Inappropriate Content'), ('misinformation', 'Misinformation'), ('copyright', 'Copyright Violation'), ('other', 'Other')], help_text='Type of report', max_length=20)),
                ('description', models.TextField(help_text='Description of the issue')),
                ('status', models.CharField(choices=[('pending', 'Pending'), ('reviewing', 'Under Review'), ('resolved', 'Resolved'), ('dismissed', 'Dismissed')], default='pending', help_text='Report status', max_length=20)),
                ('reviewed_at', models.DateTimeField(blank=True, help_text='When the report was reviewed', null=True)),
                ('resolution_notes', models.TextField(blank=True, help_text='Notes about the resolution')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('comment', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='reports', to='community.comment')),
                ('post', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='reports', to='community.post')),
                ('reported_user', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='reports_against', to=settings.AUTH_USER_MODEL)),
                ('reporter', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='reports_made', to=settings.AUTH_USER_MODEL)),
                ('reviewed_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='reports_reviewed', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Report',
                'verbose_name_plural': 'Reports',
                'db_table': 'community_report',
                'ordering': ['-created_at'],
                'indexes': [models.Index(fields=['status', '-created_at'], name='community_r_status_9e80af_idx'), models.Index(fields=['report_type', '-created_at'], name='community_r_report__d87394_idx')],
            },
        ),
        migrations.CreateModel(
            name='PostLike',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('post', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='likes', to='community.post')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='post_likes', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Post Like',
                'verbose_name_plural': 'Post Likes',
                'db_table': 'community_postlike',
                'ordering': ['-created_at'],
                'unique_together': {('user', 'post')},
            },
        ),
        migrations.CreateModel(
            name='PostBookmark',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('post', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='bookmarks', to='community.post')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='post_bookmarks', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Post Bookmark',
                'verbose_name_plural': 'Post Bookmarks',
                'db_table': 'community_postbookmark',
                'ordering': ['-created_at'],
                'unique_together': {('user', 'post')},
            },
        ),
        migrations.AddIndex(
            model_name='post',
            index=models.Index(fields=['category', '-created_at'], name='community_p_categor_d5bd82_idx'),
        ),
        migrations.AddIndex(
            model_name='post',
            index=models.Index(fields=['author', '-created_at'], name='community_p_author__c7cd0c_idx'),
        ),
        migrations.AddIndex(
            model_name='post',
            index=models.Index(fields=['status', '-created_at'], name='community_p_status_f7c524_idx'),
        ),
        migrations.AddIndex(
            model_name='post',
            index=models.Index(fields=['is_featured', '-created_at'], name='community_p_is_feat_7dc4c1_idx'),
        ),
        migrations.AddIndex(
            model_name='post',
            index=models.Index(fields=['is_local_content', '-created_at'], name='community_p_is_loca_fdc7b0_idx'),
        ),
        migrations.AddIndex(
            model_name='notification',
            index=models.Index(fields=['recipient', '-created_at'], name='community_n_recipie_d8a4a4_idx'),
        ),
        migrations.AddIndex(
            model_name='notification',
            index=models.Index(fields=['is_read', '-created_at'], name='community_n_is_read_2f6dcf_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='commentlike',
            unique_together={('user', 'comment')},
        ),
        migrations.AddIndex(
            model_name='comment',
            index=models.Index(fields=['post', 'created_at'], name='community_c_post_id_bea033_idx'),
        ),
        migrations.AddIndex(
            model_name='comment',
            index=models.Index(fields=['author', '-created_at'], name='community_c_author__460540_idx'),
        ),
        migrations.AddIndex(
            model_name='comment',
            index=models.Index(fields=['status', 'created_at'], name='community_c_status_e5c695_idx'),
        ),
    ]
