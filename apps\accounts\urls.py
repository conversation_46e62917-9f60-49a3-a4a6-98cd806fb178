"""
URL configuration for accounts app.

Handles user authentication, registration, profile management, and account-related endpoints.
"""
from django.urls import path
from . import views

app_name = 'accounts'

urlpatterns = [
    # User authentication and registration
    path('', views.AccountsAPIView.as_view(), name='accounts-root'),
    path('register/', views.RegisterView.as_view(), name='register'),
    path('profile/', views.ProfileView.as_view(), name='profile'),
    path('profile/update/', views.ProfileUpdateView.as_view(), name='profile-update'),
    
    # User management (admin)
    path('users/', views.UserListView.as_view(), name='user-list'),
    path('users/<int:pk>/', views.UserDetailView.as_view(), name='user-detail'),
]
