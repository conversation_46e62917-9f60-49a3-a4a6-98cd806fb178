"use client"

import { useState, useEffect } from "react"
import Link from "next/link"
import { Play, ShoppingBag, Users, Calendar, Briefcase, Home } from "lucide-react"
import { cn } from "@/lib/utils"

type FeatureType = "home" | "streaming" | "shopping" | "community" | "events" | "jobs"

interface SideNavigationProps {
  currentFeature: FeatureType
}

export default function SideNavigation({ currentFeature }: SideNavigationProps) {
  const [isVisible, setIsVisible] = useState(false)

  useEffect(() => {
    const handleScroll = () => {
      // Show side navigation after scrolling down 300px
      setIsVisible(window.scrollY > 300)
    }

    window.addEventListener("scroll", handleScroll)
    return () => window.removeEventListener("scroll", handleScroll)
  }, [])

  const features = [
    {
      type: "home" as FeatureType,
      name: "Home",
      href: "/",
      icon: <Home className="h-5 w-5" />,
      bgColor: "bg-gray-100",
      hoverBgColor: "hover:bg-gray-200",
      textColor: "text-gray-600",
    },
    {
      type: "streaming" as FeatureType,
      name: "Streaming",
      href: "/streaming",
      icon: <Play className="h-5 w-5" />,
      bgColor: "bg-blue-100",
      hoverBgColor: "hover:bg-blue-200",
      textColor: "text-blue-600",
    },
    {
      type: "shopping" as FeatureType,
      name: "Shopping",
      href: "/shopping",
      icon: <ShoppingBag className="h-5 w-5" />,
      bgColor: "bg-emerald-100",
      hoverBgColor: "hover:bg-emerald-200",
      textColor: "text-emerald-600",
    },
    {
      type: "community" as FeatureType,
      name: "Community",
      href: "/community",
      icon: <Users className="h-5 w-5" />,
      bgColor: "bg-purple-100",
      hoverBgColor: "hover:bg-purple-200",
      textColor: "text-purple-600",
    },
    {
      type: "events" as FeatureType,
      name: "Events",
      href: "/events",
      icon: <Calendar className="h-5 w-5" />,
      bgColor: "bg-amber-100",
      hoverBgColor: "hover:bg-amber-200",
      textColor: "text-amber-600",
    },
    {
      type: "jobs" as FeatureType,
      name: "Jobs",
      href: "/jobs",
      icon: <Briefcase className="h-5 w-5" />,
      bgColor: "bg-red-100",
      hoverBgColor: "hover:bg-red-200",
      textColor: "text-red-600",
    },
  ]

  // Filter out current feature
  const filteredFeatures = features.filter((feature) => feature.type !== currentFeature)

  return (
    <div
      className={cn(
        "fixed left-0 top-1/2 transform -translate-y-1/2 z-40 transition-all duration-300",
        isVisible ? "translate-x-0" : "-translate-x-full",
      )}
    >
      <div className="flex flex-col gap-2 p-2 bg-white border border-gray-200 rounded-r-lg shadow-md">
        {filteredFeatures.map((feature) => (
          <Link key={feature.type} href={feature.href} className="group relative flex items-center">
            <div
              className={cn(
                "h-10 w-10 rounded-full flex items-center justify-center transition-colors",
                feature.bgColor,
                feature.hoverBgColor,
              )}
            >
              <span className={feature.textColor}>{feature.icon}</span>
            </div>
            <span className="absolute left-12 bg-white text-sm font-medium px-2 py-1 rounded shadow-md border border-gray-200 opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap">
              {feature.name}
            </span>
          </Link>
        ))}
      </div>
    </div>
  )
}

