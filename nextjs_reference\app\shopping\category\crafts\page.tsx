import Link from "next/link"
import Image from "next/image"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Search, ShoppingCart, Heart, Filter, Star } from "lucide-react"
import { Badge } from "@/components/ui/badge"
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion"
import { Checkbox } from "@/components/ui/checkbox"
import { Slider } from "@/components/ui/slider"
import { UserNav } from "@/components/user-nav"
import FeatureNavigation from "@/components/feature-navigation"

export default function CraftsPage() {
  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="sticky top-0 z-50 w-full bg-white border-b shadow-sm">
        <div className="container flex h-16 items-center justify-between px-4">
          <div className="flex items-center gap-8">
            <Link href="/" className="flex items-center space-x-2">
              <span className="text-2xl font-bold bg-gradient-to-r from-blue-600 to-teal-500 bg-clip-text text-transparent">
                Kominote
              </span>
            </Link>
            <div className="hidden md:flex relative w-full max-w-md">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <Input
                type="search"
                placeholder="Search products, vendors, categories..."
                className="pl-10 pr-4 py-2 w-full border-emerald-200 focus:border-emerald-500"
              />
            </div>
          </div>
          <div className="flex items-center gap-4">
            <Button variant="ghost" size="icon" className="text-gray-600 hover:text-emerald-600">
              <Heart className="h-5 w-5" />
              <span className="sr-only">Wishlist</span>
            </Button>
            <Button variant="ghost" size="icon" className="text-gray-600 hover:text-emerald-600 relative">
              <ShoppingCart className="h-5 w-5" />
              <span className="sr-only">Cart</span>
              <Badge className="absolute -top-1 -right-1 h-5 w-5 flex items-center justify-center p-0 bg-emerald-600">
                3
              </Badge>
            </Button>
            <UserNav />
          </div>
        </div>
        <div className="container px-4 py-2 border-t border-gray-100">
          <nav className="flex items-center gap-6 overflow-x-auto pb-2 md:pb-0">
            <Link
              href="/shopping/category/clothing"
              className="text-sm font-medium whitespace-nowrap text-gray-600 hover:text-emerald-600"
            >
              Clothing & Fashion
            </Link>
            <Link
              href="/shopping/category/crafts"
              className="text-sm font-medium whitespace-nowrap text-emerald-600 border-b border-emerald-600 pb-1"
            >
              Local Crafts
            </Link>
            <Link
              href="/shopping/category/food"
              className="text-sm font-medium whitespace-nowrap text-gray-600 hover:text-emerald-600"
            >
              Food & Groceries
            </Link>
            <Link
              href="/shopping/category/beauty"
              className="text-sm font-medium whitespace-nowrap text-gray-600 hover:text-emerald-600"
            >
              Beauty & Wellness
            </Link>
            <Link
              href="/shopping/category/home"
              className="text-sm font-medium whitespace-nowrap text-gray-600 hover:text-emerald-600"
            >
              Home & Garden
            </Link>
            <Link
              href="/shopping/category/electronics"
              className="text-sm font-medium whitespace-nowrap text-gray-600 hover:text-emerald-600"
            >
              Electronics
            </Link>
            <Link
              href="/shopping/category/services"
              className="text-sm font-medium whitespace-nowrap text-gray-600 hover:text-emerald-600"
            >
              Services
            </Link>
          </nav>
        </div>
      </header>

      <main className="container px-4 py-8 md:py-12">
        {/* Category Header */}
        <div className="flex flex-col md:flex-row md:items-center justify-between gap-4 mb-8">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Local Crafts</h1>
            <p className="text-gray-600 mt-1">Discover authentic handmade crafts from talented Seychellois artisans</p>
          </div>
          <div className="flex items-center gap-2">
            <span className="text-sm text-gray-500">Sort by:</span>
            <select className="text-sm border rounded-md px-2 py-1 bg-white">
              <option>Featured</option>
              <option>Newest</option>
              <option>Price: Low to High</option>
              <option>Price: High to Low</option>
              <option>Rating</option>
            </select>
          </div>
        </div>

        {/* Main Content */}
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
          {/* Filters Sidebar */}
          <div className="lg:col-span-1">
            <div className="bg-white rounded-lg shadow-sm p-4 border border-gray-200 sticky top-24">
              <div className="flex items-center justify-between mb-4">
                <h2 className="font-medium text-lg">Filters</h2>
                <Button variant="ghost" size="sm" className="text-emerald-600 hover:text-emerald-700 h-8 px-2">
                  Reset All
                </Button>
              </div>

              <Accordion type="single" collapsible className="w-full">
                <AccordionItem value="subcategory">
                  <AccordionTrigger className="text-base font-medium">Subcategory</AccordionTrigger>
                  <AccordionContent>
                    <div className="space-y-2">
                      {["Coconut Crafts", "Wooden Carvings", "Jewelry", "Textiles", "Pottery", "Paintings"].map(
                        (category, index) => (
                          <div key={index} className="flex items-center space-x-2">
                            <Checkbox id={`category-${index}`} />
                            <label
                              htmlFor={`category-${index}`}
                              className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                            >
                              {category}
                            </label>
                          </div>
                        ),
                      )}
                    </div>
                  </AccordionContent>
                </AccordionItem>

                <AccordionItem value="price">
                  <AccordionTrigger className="text-base font-medium">Price Range</AccordionTrigger>
                  <AccordionContent>
                    <div className="space-y-4">
                      <Slider defaultValue={[0, 500]} max={1000} step={10} />
                      <div className="flex items-center justify-between">
                        <div className="border rounded-md px-2 py-1 w-20">
                          <span className="text-xs text-gray-500">SCR</span>
                          <input
                            type="number"
                            className="w-full text-sm focus:outline-none"
                            placeholder="Min"
                            defaultValue={0}
                          />
                        </div>
                        <span className="text-gray-400">-</span>
                        <div className="border rounded-md px-2 py-1 w-20">
                          <span className="text-xs text-gray-500">SCR</span>
                          <input
                            type="number"
                            className="w-full text-sm focus:outline-none"
                            placeholder="Max"
                            defaultValue={500}
                          />
                        </div>
                      </div>
                    </div>
                  </AccordionContent>
                </AccordionItem>

                <AccordionItem value="rating">
                  <AccordionTrigger className="text-base font-medium">Rating</AccordionTrigger>
                  <AccordionContent>
                    <div className="space-y-2">
                      {[5, 4, 3, 2, 1].map((rating) => (
                        <div key={rating} className="flex items-center space-x-2">
                          <Checkbox id={`rating-${rating}`} />
                          <label
                            htmlFor={`rating-${rating}`}
                            className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 flex items-center"
                          >
                            {Array(rating)
                              .fill(0)
                              .map((_, i) => (
                                <Star key={i} className="h-4 w-4 text-yellow-400 fill-yellow-400" />
                              ))}
                            {Array(5 - rating)
                              .fill(0)
                              .map((_, i) => (
                                <Star key={i} className="h-4 w-4 text-gray-300" />
                              ))}
                            <span className="ml-1">& Up</span>
                          </label>
                        </div>
                      ))}
                    </div>
                  </AccordionContent>
                </AccordionItem>

                <AccordionItem value="vendor">
                  <AccordionTrigger className="text-base font-medium">Vendor</AccordionTrigger>
                  <AccordionContent>
                    <div className="space-y-2">
                      {[
                        "Island Crafts Co.",
                        "Seychelles Artisans",
                        "Creole Crafts",
                        "Mahé Handmade",
                        "Praslin Artistry",
                      ].map((vendor, index) => (
                        <div key={index} className="flex items-center space-x-2">
                          <Checkbox id={`vendor-${index}`} />
                          <label
                            htmlFor={`vendor-${index}`}
                            className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                          >
                            {vendor}
                          </label>
                        </div>
                      ))}
                    </div>
                  </AccordionContent>
                </AccordionItem>

                <AccordionItem value="material">
                  <AccordionTrigger className="text-base font-medium">Material</AccordionTrigger>
                  <AccordionContent>
                    <div className="space-y-2">
                      {["Coconut", "Wood", "Shell", "Fabric", "Clay", "Metal"].map((material, index) => (
                        <div key={index} className="flex items-center space-x-2">
                          <Checkbox id={`material-${index}`} />
                          <label
                            htmlFor={`material-${index}`}
                            className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                          >
                            {material}
                          </label>
                        </div>
                      ))}
                    </div>
                  </AccordionContent>
                </AccordionItem>
              </Accordion>

              <Button className="w-full mt-6 bg-emerald-600 hover:bg-emerald-700">
                <Filter className="mr-2 h-4 w-4" /> Apply Filters
              </Button>
            </div>
          </div>

          {/* Products Grid */}
          <div className="lg:col-span-3">
            {/* Active Filters */}
            <div className="flex flex-wrap gap-2 mb-6">
              <Badge className="bg-emerald-100 text-emerald-800 hover:bg-emerald-200 px-3 py-1">
                Coconut Crafts <X className="ml-1 h-3 w-3" />
              </Badge>
              <Badge className="bg-emerald-100 text-emerald-800 hover:bg-emerald-200 px-3 py-1">
                Under SCR 500 <X className="ml-1 h-3 w-3" />
              </Badge>
              <Badge className="bg-emerald-100 text-emerald-800 hover:bg-emerald-200 px-3 py-1">
                4+ Stars <X className="ml-1 h-3 w-3" />
              </Badge>
            </div>

            {/* Results Count */}
            <div className="mb-6">
              <p className="text-gray-600">Showing 1-24 of 86 products</p>
            </div>

            {/* Products */}
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
              {Array(12)
                .fill(0)
                .map((_, index) => (
                  <ProductCard key={index} />
                ))}
            </div>

            {/* Pagination */}
            <div className="flex justify-center mt-12">
              <nav className="flex items-center gap-1">
                <Button variant="outline" size="icon" className="h-8 w-8" disabled>
                  <ChevronLeft className="h-4 w-4" />
                  <span className="sr-only">Previous page</span>
                </Button>
                <Button variant="outline" size="sm" className="h-8 w-8 bg-emerald-600 text-white border-emerald-600">
                  1
                </Button>
                <Button variant="outline" size="sm" className="h-8 w-8">
                  2
                </Button>
                <Button variant="outline" size="sm" className="h-8 w-8">
                  3
                </Button>
                <Button variant="outline" size="sm" className="h-8 w-8">
                  4
                </Button>
                <Button variant="outline" size="sm" className="h-8 w-8">
                  5
                </Button>
                <Button variant="outline" size="icon" className="h-8 w-8">
                  <ChevronRight className="h-4 w-4" />
                  <span className="sr-only">Next page</span>
                </Button>
              </nav>
            </div>
          </div>
        </div>
      </main>

      {/* Footer */}
      <footer className="bg-white border-t py-12">
        <div className="container px-4 md:px-6">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            <div className="space-y-4">
              <Link href="/" className="flex items-center space-x-2">
                <span className="text-2xl font-bold bg-gradient-to-r from-blue-600 to-teal-500 bg-clip-text text-transparent">
                  Kominote
                </span>
              </Link>
              <p className="text-sm text-gray-600">
                Seychelles' premier multivendor marketplace connecting local vendors with customers.
              </p>
            </div>
            <div className="space-y-4">
              <h3 className="font-medium">Shopping</h3>
              <ul className="space-y-2">
                <li>
                  <Link href="#" className="text-sm text-gray-600 hover:text-emerald-600">
                    All Categories
                  </Link>
                </li>
                <li>
                  <Link href="#" className="text-sm text-gray-600 hover:text-emerald-600">
                    Deals & Promotions
                  </Link>
                </li>
                <li>
                  <Link href="#" className="text-sm text-gray-600 hover:text-emerald-600">
                    New Arrivals
                  </Link>
                </li>
                <li>
                  <Link href="#" className="text-sm text-gray-600 hover:text-emerald-600">
                    Best Sellers
                  </Link>
                </li>
              </ul>
            </div>
            <div className="space-y-4">
              <h3 className="font-medium">Vendors</h3>
              <ul className="space-y-2">
                <li>
                  <Link href="#" className="text-sm text-gray-600 hover:text-emerald-600">
                    Become a Vendor
                  </Link>
                </li>
                <li>
                  <Link href="#" className="text-sm text-gray-600 hover:text-emerald-600">
                    Vendor Directory
                  </Link>
                </li>
                <li>
                  <Link href="#" className="text-sm text-gray-600 hover:text-emerald-600">
                    Vendor Resources
                  </Link>
                </li>
                <li>
                  <Link href="#" className="text-sm text-gray-600 hover:text-emerald-600">
                    Success Stories
                  </Link>
                </li>
              </ul>
            </div>
            <div className="space-y-4">
              <h3 className="font-medium">Help & Support</h3>
              <ul className="space-y-2">
                <li>
                  <Link href="#" className="text-sm text-gray-600 hover:text-emerald-600">
                    Customer Service
                  </Link>
                </li>
                <li>
                  <Link href="#" className="text-sm text-gray-600 hover:text-emerald-600">
                    Shipping & Delivery
                  </Link>
                </li>
                <li>
                  <Link href="#" className="text-sm text-gray-600 hover:text-emerald-600">
                    Returns & Refunds
                  </Link>
                </li>
                <li>
                  <Link href="#" className="text-sm text-gray-600 hover:text-emerald-600">
                    FAQ
                  </Link>
                </li>
              </ul>
            </div>
          </div>
          <div className="border-t mt-12 pt-8 flex flex-col md:flex-row justify-between items-center">
            <p className="text-sm text-gray-600">
              &copy; {new Date().getFullYear()} Kominote Shopping. All rights reserved.
            </p>
            <div className="flex items-center gap-4 mt-4 md:mt-0">
              <Link href="#" className="text-gray-600 hover:text-emerald-600">
                Terms of Service
              </Link>
              <Link href="#" className="text-gray-600 hover:text-emerald-600">
                Privacy Policy
              </Link>
              <Link href="#" className="text-gray-600 hover:text-emerald-600">
                Cookie Policy
              </Link>
            </div>
          </div>
        </div>
      </footer>

      {/* Side Navigation */}
      <FeatureNavigation currentFeature="shopping" colorScheme="emerald" prominentSectionHeight={300} />
    </div>
  )
}

// Product Card Component
function ProductCard() {
  return (
    <Link href="/shopping/product/handmade-coconut-bowl" className="group">
      <div className="bg-white rounded-lg shadow-sm overflow-hidden transition-all duration-200 hover:shadow-md">
        <div className="relative">
          <div className="aspect-square overflow-hidden">
            <Image
              src="/placeholder.svg?height=300&width=300"
              width={300}
              height={300}
              alt="Product image"
              className="object-cover w-full h-full group-hover:scale-105 transition-transform duration-300"
            />
          </div>
          <Button
            variant="ghost"
            size="icon"
            className="absolute top-2 right-2 h-8 w-8 rounded-full bg-white/80 text-gray-600 hover:text-emerald-600 hover:bg-white"
          >
            <Heart className="h-4 w-4" />
          </Button>
          <Badge className="absolute top-2 left-2 bg-emerald-600">New</Badge>
        </div>
        <div className="p-3">
          <div className="flex items-center gap-1 mb-1">
            <Star className="h-3 w-3 text-yellow-400 fill-yellow-400" />
            <Star className="h-3 w-3 text-yellow-400 fill-yellow-400" />
            <Star className="h-3 w-3 text-yellow-400 fill-yellow-400" />
            <Star className="h-3 w-3 text-yellow-400 fill-yellow-400" />
            <Star className="h-3 w-3 text-yellow-400 fill-yellow-400" />
            <span className="text-xs text-gray-500 ml-1">(24)</span>
          </div>
          <h3 className="font-medium text-gray-800 line-clamp-1 group-hover:text-emerald-600 transition-colors">
            Handmade Coconut Bowl
          </h3>
          <p className="text-xs text-gray-500 mt-1 line-clamp-1">Island Crafts Co.</p>
          <div className="flex items-center justify-between mt-2">
            <span className="text-sm font-bold text-emerald-600">SCR 249.99</span>
            <Button
              variant="ghost"
              size="sm"
              className="h-8 w-8 p-0 rounded-full bg-emerald-100 text-emerald-600 hover:bg-emerald-200"
            >
              <ShoppingCart className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </div>
    </Link>
  )
}

function ChevronLeft(props) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
      {...props}
    >
      <path d="m15 18-6-6 6-6" />
    </svg>
  )
}

function ChevronRight(props) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
      {...props}
    >
      <path d="m9 18 6-6-6-6" />
    </svg>
  )
}

function X(props) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
      {...props}
    >
      <path d="M18 6 6 18" />
      <path d="m6 6 12 12" />
    </svg>
  )
}

