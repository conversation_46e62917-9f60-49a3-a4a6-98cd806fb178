"""
Models for events app.

Handles event management, bookings, venues, and event organization for the Kominote platform.
Designed specifically for Seychelles events with local features and multi-language support.
"""
from django.db import models
from django.contrib.auth import get_user_model
from django.core.validators import MinValueValidator, MaxValueValidator, RegexValidator
from django.utils import timezone
from django.urls import reverse
from decimal import Decimal

User = get_user_model()


class EventCategory(models.Model):
    """
    Categories for organizing events (Music, Food, Business, Arts, Sports, etc.).
    """
    name = models.CharField(
        max_length=100,
        unique=True,
        help_text="Category name (e.g., Music, Food & Drink, Business)"
    )
    name_fr = models.CharField(
        max_length=100,
        blank=True,
        help_text="Category name in French"
    )
    name_cr = models.CharField(
        max_length=100,
        blank=True,
        help_text="Category name in Creole"
    )
    slug = models.SlugField(
        max_length=100,
        unique=True,
        help_text="URL-friendly version of the name"
    )
    description = models.TextField(
        blank=True,
        help_text="Description of this event category"
    )
    icon = models.CharField(
        max_length=50,
        blank=True,
        help_text="Icon class name for display"
    )
    color = models.CharField(
        max_length=7,
        default='#f59e0b',
        help_text="Hex color code for category display"
    )
    is_active = models.BooleanField(
        default=True,
        help_text="Whether this category is active"
    )
    sort_order = models.PositiveIntegerField(
        default=0,
        help_text="Order for displaying categories"
    )

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'events_category'
        verbose_name = 'Event Category'
        verbose_name_plural = 'Event Categories'
        ordering = ['sort_order', 'name']

    def __str__(self):
        return self.name

    def get_absolute_url(self):
        return reverse('events:category', kwargs={'slug': self.slug})


class Venue(models.Model):
    """
    Event venues and locations in Seychelles.
    """
    ISLAND_CHOICES = [
        ('mahe', 'Mahé'),
        ('praslin', 'Praslin'),
        ('la_digue', 'La Digue'),
        ('silhouette', 'Silhouette'),
        ('fregate', 'Fregate'),
        ('bird', 'Bird Island'),
        ('denis', 'Denis Island'),
        ('other', 'Other'),
    ]

    VENUE_TYPE_CHOICES = [
        ('indoor', 'Indoor'),
        ('outdoor', 'Outdoor'),
        ('beach', 'Beach'),
        ('conference', 'Conference Center'),
        ('hotel', 'Hotel'),
        ('restaurant', 'Restaurant'),
        ('community', 'Community Center'),
        ('sports', 'Sports Facility'),
        ('cultural', 'Cultural Center'),
        ('other', 'Other'),
    ]

    name = models.CharField(
        max_length=200,
        help_text="Venue name"
    )
    slug = models.SlugField(
        max_length=200,
        unique=True,
        help_text="URL-friendly version of the name"
    )
    description = models.TextField(
        blank=True,
        help_text="Venue description and facilities"
    )
    venue_type = models.CharField(
        max_length=20,
        choices=VENUE_TYPE_CHOICES,
        default='other',
        help_text="Type of venue"
    )

    # Location
    address = models.TextField(
        help_text="Full address of the venue"
    )
    island = models.CharField(
        max_length=20,
        choices=ISLAND_CHOICES,
        help_text="Which island in Seychelles"
    )
    district = models.CharField(
        max_length=100,
        blank=True,
        help_text="District or area"
    )
    latitude = models.DecimalField(
        max_digits=10,
        decimal_places=8,
        null=True,
        blank=True,
        help_text="Latitude coordinate"
    )
    longitude = models.DecimalField(
        max_digits=11,
        decimal_places=8,
        null=True,
        blank=True,
        help_text="Longitude coordinate"
    )

    # Capacity and Features
    capacity = models.PositiveIntegerField(
        null=True,
        blank=True,
        help_text="Maximum capacity of the venue"
    )
    has_parking = models.BooleanField(
        default=False,
        help_text="Whether venue has parking available"
    )
    has_accessibility = models.BooleanField(
        default=False,
        help_text="Whether venue is wheelchair accessible"
    )
    has_wifi = models.BooleanField(
        default=False,
        help_text="Whether venue has WiFi available"
    )
    has_catering = models.BooleanField(
        default=False,
        help_text="Whether venue offers catering services"
    )

    # Contact Information
    contact_name = models.CharField(
        max_length=200,
        blank=True,
        help_text="Contact person name"
    )
    contact_phone = models.CharField(
        max_length=20,
        blank=True,
        help_text="Contact phone number"
    )
    contact_email = models.EmailField(
        blank=True,
        help_text="Contact email address"
    )
    website = models.URLField(
        blank=True,
        help_text="Venue website URL"
    )

    # Media
    image = models.ImageField(
        upload_to='venues/',
        null=True,
        blank=True,
        help_text="Main venue image"
    )

    # Status
    is_active = models.BooleanField(
        default=True,
        help_text="Whether this venue is active"
    )
    is_verified = models.BooleanField(
        default=False,
        help_text="Whether this venue is verified"
    )

    # Statistics
    total_events = models.PositiveIntegerField(
        default=0,
        help_text="Total number of events hosted"
    )
    average_rating = models.DecimalField(
        max_digits=3,
        decimal_places=2,
        default=Decimal('0.00'),
        help_text="Average rating from event reviews"
    )

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'events_venue'
        verbose_name = 'Venue'
        verbose_name_plural = 'Venues'
        ordering = ['name']
        indexes = [
            models.Index(fields=['island', 'district']),
            models.Index(fields=['venue_type']),
            models.Index(fields=['is_active', 'is_verified']),
        ]

    def __str__(self):
        return f"{self.name} ({self.get_island_display()})"

    def get_absolute_url(self):
        return reverse('events:venue', kwargs={'slug': self.slug})


class EventOrganizer(models.Model):
    """
    Event organizer profiles for managing events.
    """
    ORGANIZER_TYPE_CHOICES = [
        ('individual', 'Individual'),
        ('business', 'Business'),
        ('nonprofit', 'Non-Profit Organization'),
        ('government', 'Government Agency'),
        ('community', 'Community Group'),
        ('educational', 'Educational Institution'),
    ]

    user = models.OneToOneField(
        User,
        on_delete=models.CASCADE,
        related_name='event_organizer_profile'
    )
    organization_name = models.CharField(
        max_length=200,
        blank=True,
        help_text="Organization or business name"
    )
    organizer_type = models.CharField(
        max_length=20,
        choices=ORGANIZER_TYPE_CHOICES,
        default='individual',
        help_text="Type of organizer"
    )
    description = models.TextField(
        blank=True,
        help_text="Description of the organizer"
    )

    # Contact Information
    contact_phone = models.CharField(
        max_length=20,
        blank=True,
        help_text="Contact phone number"
    )
    contact_email = models.EmailField(
        blank=True,
        help_text="Contact email address"
    )
    website = models.URLField(
        blank=True,
        help_text="Organizer website URL"
    )

    # Social Media
    facebook_url = models.URLField(blank=True)
    instagram_url = models.URLField(blank=True)
    twitter_url = models.URLField(blank=True)

    # Verification and Status
    is_verified = models.BooleanField(
        default=False,
        help_text="Whether this organizer is verified"
    )
    verification_documents = models.FileField(
        upload_to='organizers/documents/',
        null=True,
        blank=True,
        help_text="Verification documents"
    )

    # Statistics
    total_events = models.PositiveIntegerField(
        default=0,
        help_text="Total number of events organized"
    )
    total_attendees = models.PositiveIntegerField(
        default=0,
        help_text="Total number of attendees across all events"
    )
    average_rating = models.DecimalField(
        max_digits=3,
        decimal_places=2,
        default=Decimal('0.00'),
        help_text="Average rating from event reviews"
    )

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'events_organizer'
        verbose_name = 'Event Organizer'
        verbose_name_plural = 'Event Organizers'
        ordering = ['-total_events', 'organization_name']

    def __str__(self):
        if self.organization_name:
            return f"{self.organization_name} ({self.user.get_display_name()})"
        return self.user.get_display_name()


class Event(models.Model):
    """
    Main event model with comprehensive features for Seychelles events.
    """
    EVENT_TYPE_CHOICES = [
        ('public', 'Public Event'),
        ('private', 'Private Event'),
        ('invite_only', 'Invite Only'),
    ]

    EVENT_STATUS_CHOICES = [
        ('draft', 'Draft'),
        ('published', 'Published'),
        ('cancelled', 'Cancelled'),
        ('postponed', 'Postponed'),
        ('completed', 'Completed'),
    ]

    PRICING_TYPE_CHOICES = [
        ('free', 'Free'),
        ('paid', 'Paid'),
        ('donation', 'Donation Based'),
    ]

    # Basic Information
    title = models.CharField(
        max_length=200,
        help_text="Event title"
    )
    title_fr = models.CharField(
        max_length=200,
        blank=True,
        help_text="Event title in French"
    )
    title_cr = models.CharField(
        max_length=200,
        blank=True,
        help_text="Event title in Creole"
    )
    slug = models.SlugField(
        max_length=200,
        unique=True,
        help_text="URL-friendly version of the title"
    )
    description = models.TextField(
        help_text="Detailed event description"
    )
    short_description = models.TextField(
        max_length=500,
        blank=True,
        help_text="Short description for listings"
    )

    # Categorization
    category = models.ForeignKey(
        EventCategory,
        on_delete=models.PROTECT,
        related_name='events',
        help_text="Event category"
    )
    tags = models.CharField(
        max_length=500,
        blank=True,
        help_text="Comma-separated tags for the event"
    )

    # Organization
    organizer = models.ForeignKey(
        EventOrganizer,
        on_delete=models.CASCADE,
        related_name='events',
        help_text="Event organizer"
    )
    venue = models.ForeignKey(
        Venue,
        on_delete=models.PROTECT,
        related_name='events',
        help_text="Event venue"
    )

    # Date and Time
    start_date = models.DateField(
        help_text="Event start date"
    )
    start_time = models.TimeField(
        help_text="Event start time"
    )
    end_date = models.DateField(
        help_text="Event end date"
    )
    end_time = models.TimeField(
        help_text="Event end time"
    )
    timezone = models.CharField(
        max_length=50,
        default='Indian/Mahe',
        help_text="Event timezone"
    )

    # Registration and Capacity
    event_type = models.CharField(
        max_length=20,
        choices=EVENT_TYPE_CHOICES,
        default='public',
        help_text="Type of event"
    )
    max_attendees = models.PositiveIntegerField(
        null=True,
        blank=True,
        help_text="Maximum number of attendees (leave blank for unlimited)"
    )
    registration_required = models.BooleanField(
        default=True,
        help_text="Whether registration is required"
    )
    registration_deadline = models.DateTimeField(
        null=True,
        blank=True,
        help_text="Registration deadline"
    )
    allow_waitlist = models.BooleanField(
        default=False,
        help_text="Allow waitlist when event is full"
    )

    # Pricing
    pricing_type = models.CharField(
        max_length=20,
        choices=PRICING_TYPE_CHOICES,
        default='free',
        help_text="Pricing type for the event"
    )
    price = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        default=Decimal('0.00'),
        help_text="Event price (if paid)"
    )
    currency = models.CharField(
        max_length=3,
        default='SCR',
        help_text="Currency code (SCR for Seychelles Rupee)"
    )

    # Media
    featured_image = models.ImageField(
        upload_to='events/',
        null=True,
        blank=True,
        help_text="Main event image"
    )

    # Status and Visibility
    status = models.CharField(
        max_length=20,
        choices=EVENT_STATUS_CHOICES,
        default='draft',
        help_text="Event status"
    )
    is_featured = models.BooleanField(
        default=False,
        help_text="Whether this event is featured"
    )
    is_recurring = models.BooleanField(
        default=False,
        help_text="Whether this is a recurring event"
    )

    # Seychelles-specific fields
    is_local_event = models.BooleanField(
        default=True,
        help_text="Whether this is a local Seychelles event"
    )
    supports_local_culture = models.BooleanField(
        default=False,
        help_text="Whether this event supports local Seychellois culture"
    )

    # Requirements and Restrictions
    age_restriction = models.CharField(
        max_length=50,
        blank=True,
        help_text="Age restrictions (e.g., '18+', 'Family Friendly')"
    )
    dress_code = models.CharField(
        max_length=200,
        blank=True,
        help_text="Dress code requirements"
    )
    special_requirements = models.TextField(
        blank=True,
        help_text="Special requirements or instructions"
    )

    # Contact Information
    contact_email = models.EmailField(
        blank=True,
        help_text="Contact email for event inquiries"
    )
    contact_phone = models.CharField(
        max_length=20,
        blank=True,
        help_text="Contact phone number"
    )

    # Statistics
    total_registrations = models.PositiveIntegerField(
        default=0,
        help_text="Total number of registrations"
    )
    total_attendees = models.PositiveIntegerField(
        default=0,
        help_text="Total number of actual attendees"
    )
    total_views = models.PositiveIntegerField(
        default=0,
        help_text="Total number of page views"
    )
    average_rating = models.DecimalField(
        max_digits=3,
        decimal_places=2,
        default=Decimal('0.00'),
        help_text="Average rating from reviews"
    )
    total_reviews = models.PositiveIntegerField(
        default=0,
        help_text="Total number of reviews"
    )

    # SEO
    meta_title = models.CharField(
        max_length=200,
        blank=True,
        help_text="SEO meta title"
    )
    meta_description = models.TextField(
        max_length=500,
        blank=True,
        help_text="SEO meta description"
    )

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    published_at = models.DateTimeField(
        null=True,
        blank=True,
        help_text="When the event was published"
    )

    class Meta:
        db_table = 'events_event'
        verbose_name = 'Event'
        verbose_name_plural = 'Events'
        ordering = ['-start_date', '-start_time']
        indexes = [
            models.Index(fields=['status', 'start_date']),
            models.Index(fields=['category', 'start_date']),
            models.Index(fields=['venue', 'start_date']),
            models.Index(fields=['organizer', 'start_date']),
            models.Index(fields=['is_featured', 'start_date']),
            models.Index(fields=['pricing_type', 'start_date']),
        ]

    def __str__(self):
        return f"{self.title} - {self.start_date}"

    def get_absolute_url(self):
        return reverse('events:detail', kwargs={'slug': self.slug})

    @property
    def is_upcoming(self):
        """Check if event is upcoming."""
        now = timezone.now()
        event_start = timezone.datetime.combine(self.start_date, self.start_time)
        return event_start > now

    @property
    def is_past(self):
        """Check if event is in the past."""
        now = timezone.now()
        event_end = timezone.datetime.combine(self.end_date, self.end_time)
        return event_end < now

    @property
    def is_ongoing(self):
        """Check if event is currently ongoing."""
        now = timezone.now()
        event_start = timezone.datetime.combine(self.start_date, self.start_time)
        event_end = timezone.datetime.combine(self.end_date, self.end_time)
        return event_start <= now <= event_end

    @property
    def is_full(self):
        """Check if event is at capacity."""
        if not self.max_attendees:
            return False
        return self.total_registrations >= self.max_attendees

    @property
    def available_spots(self):
        """Get number of available spots."""
        if not self.max_attendees:
            return None
        return max(0, self.max_attendees - self.total_registrations)

    @property
    def duration_hours(self):
        """Calculate event duration in hours."""
        start_datetime = timezone.datetime.combine(self.start_date, self.start_time)
        end_datetime = timezone.datetime.combine(self.end_date, self.end_time)
        duration = end_datetime - start_datetime
        return duration.total_seconds() / 3600

    def get_tags_list(self):
        """Return tags as a list."""
        if self.tags:
            return [tag.strip() for tag in self.tags.split(',')]
        return []

    def can_register(self):
        """Check if registration is still possible."""
        if not self.registration_required:
            return False
        if self.status != 'published':
            return False
        if self.is_past:
            return False
        if self.registration_deadline and timezone.now() > self.registration_deadline:
            return False
        if self.is_full and not self.allow_waitlist:
            return False
        return True


class EventRegistration(models.Model):
    """
    User registrations for events.
    """
    REGISTRATION_STATUS_CHOICES = [
        ('pending', 'Pending'),
        ('confirmed', 'Confirmed'),
        ('cancelled', 'Cancelled'),
        ('waitlist', 'Waitlist'),
        ('attended', 'Attended'),
        ('no_show', 'No Show'),
    ]

    event = models.ForeignKey(
        Event,
        on_delete=models.CASCADE,
        related_name='registrations',
        help_text="Event being registered for"
    )
    user = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='event_registrations',
        help_text="User registering for the event"
    )
    status = models.CharField(
        max_length=20,
        choices=REGISTRATION_STATUS_CHOICES,
        default='pending',
        help_text="Registration status"
    )

    # Registration Details
    registration_date = models.DateTimeField(
        auto_now_add=True,
        help_text="When the registration was made"
    )
    number_of_attendees = models.PositiveIntegerField(
        default=1,
        validators=[MinValueValidator(1)],
        help_text="Number of people attending"
    )
    attendee_names = models.TextField(
        blank=True,
        help_text="Names of additional attendees (if more than 1)"
    )

    # Contact Information
    contact_phone = models.CharField(
        max_length=20,
        blank=True,
        help_text="Contact phone for this registration"
    )
    special_requirements = models.TextField(
        blank=True,
        help_text="Special requirements or dietary restrictions"
    )

    # Payment Information (if paid event)
    amount_paid = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        default=Decimal('0.00'),
        help_text="Amount paid for registration"
    )
    payment_status = models.CharField(
        max_length=20,
        choices=[
            ('pending', 'Pending'),
            ('paid', 'Paid'),
            ('refunded', 'Refunded'),
            ('failed', 'Failed'),
        ],
        default='pending',
        help_text="Payment status"
    )
    payment_reference = models.CharField(
        max_length=100,
        blank=True,
        help_text="Payment reference or transaction ID"
    )

    # Check-in Information
    checked_in = models.BooleanField(
        default=False,
        help_text="Whether the user has checked in"
    )
    check_in_time = models.DateTimeField(
        null=True,
        blank=True,
        help_text="When the user checked in"
    )

    # Timestamps
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'events_registration'
        verbose_name = 'Event Registration'
        verbose_name_plural = 'Event Registrations'
        unique_together = ['event', 'user']
        ordering = ['-registration_date']
        indexes = [
            models.Index(fields=['event', 'status']),
            models.Index(fields=['user', 'status']),
            models.Index(fields=['registration_date']),
        ]

    def __str__(self):
        return f"{self.user.get_display_name()} - {self.event.title}"

    @property
    def total_cost(self):
        """Calculate total cost for this registration."""
        return self.event.price * self.number_of_attendees

    def can_cancel(self):
        """Check if registration can be cancelled."""
        if self.status in ['cancelled', 'attended']:
            return False
        if self.event.is_past:
            return False
        return True


class EventTicket(models.Model):
    """
    Tickets for paid events with different pricing tiers.
    """
    TICKET_TYPE_CHOICES = [
        ('general', 'General Admission'),
        ('vip', 'VIP'),
        ('early_bird', 'Early Bird'),
        ('student', 'Student'),
        ('senior', 'Senior'),
        ('group', 'Group'),
        ('sponsor', 'Sponsor'),
    ]

    event = models.ForeignKey(
        Event,
        on_delete=models.CASCADE,
        related_name='ticket_types',
        help_text="Event this ticket is for"
    )
    name = models.CharField(
        max_length=100,
        help_text="Ticket type name"
    )
    description = models.TextField(
        blank=True,
        help_text="Description of what's included"
    )
    ticket_type = models.CharField(
        max_length=20,
        choices=TICKET_TYPE_CHOICES,
        default='general',
        help_text="Type of ticket"
    )

    # Pricing
    price = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        help_text="Ticket price"
    )
    currency = models.CharField(
        max_length=3,
        default='SCR',
        help_text="Currency code"
    )

    # Availability
    quantity_available = models.PositiveIntegerField(
        null=True,
        blank=True,
        help_text="Number of tickets available (leave blank for unlimited)"
    )
    quantity_sold = models.PositiveIntegerField(
        default=0,
        help_text="Number of tickets sold"
    )

    # Sale Period
    sale_start_date = models.DateTimeField(
        null=True,
        blank=True,
        help_text="When ticket sales start"
    )
    sale_end_date = models.DateTimeField(
        null=True,
        blank=True,
        help_text="When ticket sales end"
    )

    # Settings
    is_active = models.BooleanField(
        default=True,
        help_text="Whether this ticket type is active"
    )
    min_quantity = models.PositiveIntegerField(
        default=1,
        help_text="Minimum quantity per purchase"
    )
    max_quantity = models.PositiveIntegerField(
        default=10,
        help_text="Maximum quantity per purchase"
    )

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'events_ticket'
        verbose_name = 'Event Ticket'
        verbose_name_plural = 'Event Tickets'
        ordering = ['price']

    def __str__(self):
        return f"{self.event.title} - {self.name} ({self.price} {self.currency})"

    @property
    def is_available(self):
        """Check if tickets are available for purchase."""
        if not self.is_active:
            return False
        if self.quantity_available and self.quantity_sold >= self.quantity_available:
            return False
        now = timezone.now()
        if self.sale_start_date and now < self.sale_start_date:
            return False
        if self.sale_end_date and now > self.sale_end_date:
            return False
        return True

    @property
    def remaining_quantity(self):
        """Get remaining ticket quantity."""
        if not self.quantity_available:
            return None
        return max(0, self.quantity_available - self.quantity_sold)


class EventImage(models.Model):
    """
    Additional images for events (gallery).
    """
    event = models.ForeignKey(
        Event,
        on_delete=models.CASCADE,
        related_name='images',
        help_text="Event this image belongs to"
    )
    image = models.ImageField(
        upload_to='events/gallery/',
        help_text="Event image"
    )
    caption = models.CharField(
        max_length=200,
        blank=True,
        help_text="Image caption"
    )
    alt_text = models.CharField(
        max_length=200,
        blank=True,
        help_text="Alt text for accessibility"
    )
    sort_order = models.PositiveIntegerField(
        default=0,
        help_text="Order for displaying images"
    )
    is_featured = models.BooleanField(
        default=False,
        help_text="Whether this is a featured image"
    )

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        db_table = 'events_image'
        verbose_name = 'Event Image'
        verbose_name_plural = 'Event Images'
        ordering = ['sort_order', 'created_at']

    def __str__(self):
        return f"{self.event.title} - Image {self.id}"


class EventReview(models.Model):
    """
    Reviews and ratings for events.
    """
    event = models.ForeignKey(
        Event,
        on_delete=models.CASCADE,
        related_name='reviews',
        help_text="Event being reviewed"
    )
    user = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='event_reviews',
        help_text="User writing the review"
    )
    registration = models.ForeignKey(
        EventRegistration,
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        help_text="Registration this review is for"
    )

    # Review Content
    rating = models.PositiveIntegerField(
        validators=[MinValueValidator(1), MaxValueValidator(5)],
        help_text="Rating from 1 to 5 stars"
    )
    title = models.CharField(
        max_length=200,
        help_text="Review title"
    )
    content = models.TextField(
        help_text="Review content"
    )

    # Review Categories
    organization_rating = models.PositiveIntegerField(
        validators=[MinValueValidator(1), MaxValueValidator(5)],
        null=True,
        blank=True,
        help_text="Rating for event organization"
    )
    venue_rating = models.PositiveIntegerField(
        validators=[MinValueValidator(1), MaxValueValidator(5)],
        null=True,
        blank=True,
        help_text="Rating for venue"
    )
    value_rating = models.PositiveIntegerField(
        validators=[MinValueValidator(1), MaxValueValidator(5)],
        null=True,
        blank=True,
        help_text="Rating for value for money"
    )

    # Verification
    is_verified = models.BooleanField(
        default=False,
        help_text="Whether this review is from a verified attendee"
    )

    # Moderation
    is_approved = models.BooleanField(
        default=True,
        help_text="Whether this review is approved"
    )
    moderated_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='moderated_event_reviews',
        help_text="Admin who moderated this review"
    )
    moderation_notes = models.TextField(
        blank=True,
        help_text="Notes from moderation"
    )

    # Helpful votes
    helpful_votes = models.PositiveIntegerField(
        default=0,
        help_text="Number of helpful votes"
    )

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'events_review'
        verbose_name = 'Event Review'
        verbose_name_plural = 'Event Reviews'
        unique_together = ['event', 'user']
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['event', 'is_approved']),
            models.Index(fields=['user', '-created_at']),
            models.Index(fields=['rating', '-created_at']),
        ]

    def __str__(self):
        return f"{self.user.get_display_name()} - {self.event.title} ({self.rating}★)"

    @property
    def average_category_rating(self):
        """Calculate average of category ratings."""
        ratings = [r for r in [self.organization_rating, self.venue_rating, self.value_rating] if r]
        if ratings:
            return sum(ratings) / len(ratings)
        return self.rating
