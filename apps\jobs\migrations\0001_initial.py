# Generated by Django 5.2.1 on 2025-05-30 14:35

import django.core.validators
import django.db.models.deletion
from decimal import Decimal
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='JobCategory',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(help_text='Category name (e.g., Technology, Tourism, Finance)', max_length=100, unique=True)),
                ('name_fr', models.CharField(blank=True, help_text='Category name in French', max_length=100)),
                ('name_cr', models.CharField(blank=True, help_text='Category name in Creole', max_length=100)),
                ('slug', models.SlugField(help_text='URL-friendly version of the name', max_length=100, unique=True)),
                ('description', models.TextField(blank=True, help_text='Description of this job category')),
                ('icon', models.CharField(blank=True, help_text='Icon class name for display', max_length=50)),
                ('color', models.CharField(default='#ef4444', help_text='Hex color code for category display', max_length=7)),
                ('is_active', models.BooleanField(default=True, help_text='Whether this category is active')),
                ('sort_order', models.PositiveIntegerField(default=0, help_text='Order for displaying categories')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'Job Category',
                'verbose_name_plural': 'Job Categories',
                'db_table': 'jobs_category',
                'ordering': ['sort_order', 'name'],
            },
        ),
        migrations.CreateModel(
            name='JobSkill',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(help_text='Skill name', max_length=100, unique=True)),
                ('slug', models.SlugField(help_text='URL-friendly version of the name', max_length=100, unique=True)),
                ('skill_type', models.CharField(choices=[('technical', 'Technical Skill'), ('soft', 'Soft Skill'), ('language', 'Language'), ('certification', 'Certification'), ('education', 'Education')], default='technical', help_text='Type of skill', max_length=20)),
                ('description', models.TextField(blank=True, help_text='Skill description')),
                ('is_active', models.BooleanField(default=True, help_text='Whether this skill is active')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'Job Skill',
                'verbose_name_plural': 'Job Skills',
                'db_table': 'jobs_skill',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='Company',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(help_text='Company name', max_length=200)),
                ('slug', models.SlugField(help_text='URL-friendly version of the name', max_length=200, unique=True)),
                ('description', models.TextField(help_text='Company description and overview')),
                ('short_description', models.TextField(blank=True, help_text='Short description for listings', max_length=500)),
                ('company_type', models.CharField(choices=[('private', 'Private Company'), ('public', 'Public Company'), ('government', 'Government Agency'), ('nonprofit', 'Non-Profit Organization'), ('startup', 'Startup'), ('multinational', 'Multinational Corporation')], default='private', help_text='Type of company', max_length=20)),
                ('company_size', models.CharField(choices=[('startup', '1-10 employees'), ('small', '11-50 employees'), ('medium', '51-200 employees'), ('large', '201-1000 employees'), ('enterprise', '1000+ employees')], default='small', help_text='Company size by employee count', max_length=20)),
                ('founded_year', models.PositiveIntegerField(blank=True, help_text='Year the company was founded', null=True, validators=[django.core.validators.MinValueValidator(1800), django.core.validators.MaxValueValidator(2030)])),
                ('headquarters', models.TextField(help_text='Company headquarters address')),
                ('island', models.CharField(choices=[('mahe', 'Mahé'), ('praslin', 'Praslin'), ('la_digue', 'La Digue'), ('silhouette', 'Silhouette'), ('fregate', 'Fregate'), ('bird', 'Bird Island'), ('denis', 'Denis Island'), ('other', 'Other')], help_text='Primary island location in Seychelles', max_length=20)),
                ('district', models.CharField(blank=True, help_text='District or area', max_length=100)),
                ('website', models.URLField(blank=True, help_text='Company website URL')),
                ('email', models.EmailField(help_text='Company contact email', max_length=254)),
                ('phone', models.CharField(blank=True, help_text='Company phone number', max_length=20)),
                ('linkedin_url', models.URLField(blank=True)),
                ('facebook_url', models.URLField(blank=True)),
                ('twitter_url', models.URLField(blank=True)),
                ('instagram_url', models.URLField(blank=True)),
                ('logo', models.ImageField(blank=True, help_text='Company logo', null=True, upload_to='companies/logos/')),
                ('cover_image', models.ImageField(blank=True, help_text='Company cover image', null=True, upload_to='companies/covers/')),
                ('is_verified', models.BooleanField(default=False, help_text='Whether this company is verified')),
                ('is_featured', models.BooleanField(default=False, help_text='Whether this company is featured')),
                ('is_hiring', models.BooleanField(default=True, help_text='Whether this company is actively hiring')),
                ('is_local_company', models.BooleanField(default=True, help_text='Whether this is a local Seychelles company')),
                ('supports_local_talent', models.BooleanField(default=False, help_text='Whether this company actively supports local talent development')),
                ('total_jobs', models.PositiveIntegerField(default=0, help_text='Total number of active job postings')),
                ('total_employees', models.PositiveIntegerField(default=0, help_text='Total number of employees')),
                ('average_rating', models.DecimalField(decimal_places=2, default=Decimal('0.00'), help_text='Average rating from employee reviews', max_digits=3)),
                ('total_reviews', models.PositiveIntegerField(default=0, help_text='Total number of company reviews')),
                ('meta_title', models.CharField(blank=True, help_text='SEO meta title', max_length=200)),
                ('meta_description', models.TextField(blank=True, help_text='SEO meta description', max_length=500)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'Company',
                'verbose_name_plural': 'Companies',
                'db_table': 'jobs_company',
                'ordering': ['name'],
                'indexes': [models.Index(fields=['island', 'district'], name='jobs_compan_island_cfd3ea_idx'), models.Index(fields=['company_type'], name='jobs_compan_company_0066e6_idx'), models.Index(fields=['is_verified', 'is_hiring'], name='jobs_compan_is_veri_5dc84d_idx'), models.Index(fields=['is_featured', '-created_at'], name='jobs_compan_is_feat_314c1c_idx')],
            },
        ),
        migrations.CreateModel(
            name='Job',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(help_text='Job title', max_length=200)),
                ('slug', models.SlugField(help_text='URL-friendly version of the title', max_length=200, unique=True)),
                ('description', models.TextField(help_text='Detailed job description')),
                ('summary', models.TextField(help_text='Brief job summary', max_length=500)),
                ('job_type', models.CharField(choices=[('full_time', 'Full-time'), ('part_time', 'Part-time'), ('contract', 'Contract'), ('temporary', 'Temporary'), ('internship', 'Internship'), ('freelance', 'Freelance'), ('remote', 'Remote')], default='full_time', help_text='Type of employment', max_length=20)),
                ('experience_level', models.CharField(choices=[('entry', 'Entry Level'), ('junior', 'Junior (1-3 years)'), ('mid', 'Mid Level (3-5 years)'), ('senior', 'Senior (5-10 years)'), ('lead', 'Lead (10+ years)'), ('executive', 'Executive')], default='mid', help_text='Required experience level', max_length=20)),
                ('education_level', models.CharField(choices=[('none', 'No formal education required'), ('high_school', 'High School'), ('diploma', 'Diploma'), ('bachelor', "Bachelor's Degree"), ('master', "Master's Degree"), ('phd', 'PhD'), ('professional', 'Professional Certification')], default='bachelor', help_text='Required education level', max_length=20)),
                ('location', models.CharField(help_text='Job location', max_length=200)),
                ('island', models.CharField(choices=[('mahe', 'Mahé'), ('praslin', 'Praslin'), ('la_digue', 'La Digue'), ('silhouette', 'Silhouette'), ('fregate', 'Fregate'), ('bird', 'Bird Island'), ('denis', 'Denis Island'), ('other', 'Other')], help_text='Island location in Seychelles', max_length=20)),
                ('district', models.CharField(blank=True, help_text='District or area', max_length=100)),
                ('is_remote', models.BooleanField(default=False, help_text='Whether this job can be done remotely')),
                ('salary_min', models.DecimalField(blank=True, decimal_places=2, help_text='Minimum salary', max_digits=10, null=True)),
                ('salary_max', models.DecimalField(blank=True, decimal_places=2, help_text='Maximum salary', max_digits=10, null=True)),
                ('salary_currency', models.CharField(default='SCR', help_text='Currency code (SCR for Seychelles Rupee)', max_length=3)),
                ('salary_period', models.CharField(choices=[('hourly', 'Per Hour'), ('daily', 'Per Day'), ('weekly', 'Per Week'), ('monthly', 'Per Month'), ('yearly', 'Per Year')], default='monthly', help_text='Salary payment period', max_length=20)),
                ('benefits', models.TextField(blank=True, help_text='Job benefits and perks')),
                ('requirements', models.TextField(help_text='Job requirements and qualifications')),
                ('responsibilities', models.TextField(help_text='Job responsibilities and duties')),
                ('application_deadline', models.DateField(blank=True, help_text='Application deadline', null=True)),
                ('application_email', models.EmailField(blank=True, help_text='Email for applications', max_length=254)),
                ('application_url', models.URLField(blank=True, help_text='External application URL')),
                ('how_to_apply', models.TextField(blank=True, help_text='Instructions on how to apply')),
                ('status', models.CharField(choices=[('draft', 'Draft'), ('published', 'Published'), ('paused', 'Paused'), ('closed', 'Closed'), ('filled', 'Filled')], default='draft', help_text='Job status', max_length=20)),
                ('is_featured', models.BooleanField(default=False, help_text='Whether this job is featured')),
                ('is_urgent', models.BooleanField(default=False, help_text='Whether this is an urgent hiring')),
                ('is_local_job', models.BooleanField(default=True, help_text='Whether this is a local Seychelles job')),
                ('supports_local_talent', models.BooleanField(default=False, help_text='Whether this job prioritizes local talent')),
                ('work_permit_sponsored', models.BooleanField(default=False, help_text='Whether work permit sponsorship is available')),
                ('total_applications', models.PositiveIntegerField(default=0, help_text='Total number of applications')),
                ('total_views', models.PositiveIntegerField(default=0, help_text='Total number of page views')),
                ('total_saves', models.PositiveIntegerField(default=0, help_text='Total number of saves/bookmarks')),
                ('meta_title', models.CharField(blank=True, help_text='SEO meta title', max_length=200)),
                ('meta_description', models.TextField(blank=True, help_text='SEO meta description', max_length=500)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('published_at', models.DateTimeField(blank=True, help_text='When the job was published', null=True)),
                ('company', models.ForeignKey(help_text='Hiring company', on_delete=django.db.models.deletion.CASCADE, related_name='jobs', to='jobs.company')),
                ('category', models.ForeignKey(help_text='Job category', on_delete=django.db.models.deletion.PROTECT, related_name='jobs', to='jobs.jobcategory')),
                ('skills', models.ManyToManyField(blank=True, help_text='Required skills', related_name='jobs', to='jobs.jobskill')),
            ],
            options={
                'verbose_name': 'Job',
                'verbose_name_plural': 'Jobs',
                'db_table': 'jobs_job',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='JobSeekerProfile',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('headline', models.CharField(blank=True, help_text='Professional headline or title', max_length=200)),
                ('summary', models.TextField(blank=True, help_text='Professional summary')),
                ('experience_years', models.PositiveIntegerField(blank=True, help_text='Years of professional experience', null=True)),
                ('current_position', models.CharField(blank=True, help_text='Current job title', max_length=200)),
                ('current_company', models.CharField(blank=True, help_text='Current company name', max_length=200)),
                ('availability', models.CharField(choices=[('immediately', 'Immediately'), ('2_weeks', 'Within 2 weeks'), ('1_month', 'Within 1 month'), ('3_months', 'Within 3 months'), ('not_looking', 'Not actively looking')], default='1_month', help_text='Availability for new opportunities', max_length=20)),
                ('desired_job_types', models.CharField(blank=True, help_text='Comma-separated desired job types', max_length=200)),
                ('desired_salary_min', models.DecimalField(blank=True, decimal_places=2, help_text='Minimum desired salary', max_digits=10, null=True)),
                ('desired_salary_max', models.DecimalField(blank=True, decimal_places=2, help_text='Maximum desired salary', max_digits=10, null=True)),
                ('preferred_locations', models.CharField(blank=True, help_text='Comma-separated preferred work locations', max_length=500)),
                ('willing_to_relocate', models.BooleanField(default=False, help_text='Willing to relocate for work')),
                ('open_to_remote', models.BooleanField(default=True, help_text='Open to remote work opportunities')),
                ('education', models.TextField(blank=True, help_text='Education background')),
                ('certifications', models.TextField(blank=True, help_text='Professional certifications')),
                ('resume', models.FileField(blank=True, help_text='Resume/CV file', null=True, upload_to='resumes/')),
                ('cover_letter_template', models.TextField(blank=True, help_text='Default cover letter template')),
                ('portfolio_url', models.URLField(blank=True, help_text='Portfolio website URL')),
                ('profile_visibility', models.CharField(choices=[('public', 'Public'), ('employers_only', 'Employers Only'), ('private', 'Private')], default='employers_only', help_text='Who can see your profile', max_length=20)),
                ('allow_contact', models.BooleanField(default=True, help_text='Allow employers to contact directly')),
                ('profile_views', models.PositiveIntegerField(default=0, help_text='Number of profile views')),
                ('applications_sent', models.PositiveIntegerField(default=0, help_text='Total applications sent')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='job_seeker_profile', to=settings.AUTH_USER_MODEL)),
                ('skills', models.ManyToManyField(blank=True, help_text='Professional skills', related_name='job_seekers', to='jobs.jobskill')),
            ],
            options={
                'verbose_name': 'Job Seeker Profile',
                'verbose_name_plural': 'Job Seeker Profiles',
                'db_table': 'jobs_jobseekerprofile',
            },
        ),
        migrations.CreateModel(
            name='SavedJob',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('notes', models.TextField(blank=True, help_text='Personal notes about this job')),
                ('saved_at', models.DateTimeField(auto_now_add=True)),
                ('job', models.ForeignKey(help_text='Job that was saved', on_delete=django.db.models.deletion.CASCADE, related_name='saved_by_users', to='jobs.job')),
                ('user', models.ForeignKey(help_text='User who saved the job', on_delete=django.db.models.deletion.CASCADE, related_name='saved_jobs', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Saved Job',
                'verbose_name_plural': 'Saved Jobs',
                'db_table': 'jobs_savedjob',
                'ordering': ['-saved_at'],
            },
        ),
        migrations.CreateModel(
            name='CompanyReview',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('overall_rating', models.PositiveIntegerField(help_text='Overall rating from 1 to 5 stars', validators=[django.core.validators.MinValueValidator(1), django.core.validators.MaxValueValidator(5)])),
                ('title', models.CharField(help_text='Review title', max_length=200)),
                ('pros', models.TextField(help_text='What you liked about working here')),
                ('cons', models.TextField(help_text='What could be improved')),
                ('advice_to_management', models.TextField(blank=True, help_text='Advice to management')),
                ('work_life_balance_rating', models.PositiveIntegerField(blank=True, help_text='Work-life balance rating', null=True, validators=[django.core.validators.MinValueValidator(1), django.core.validators.MaxValueValidator(5)])),
                ('salary_benefits_rating', models.PositiveIntegerField(blank=True, help_text='Salary and benefits rating', null=True, validators=[django.core.validators.MinValueValidator(1), django.core.validators.MaxValueValidator(5)])),
                ('career_opportunities_rating', models.PositiveIntegerField(blank=True, help_text='Career opportunities rating', null=True, validators=[django.core.validators.MinValueValidator(1), django.core.validators.MaxValueValidator(5)])),
                ('management_rating', models.PositiveIntegerField(blank=True, help_text='Management quality rating', null=True, validators=[django.core.validators.MinValueValidator(1), django.core.validators.MaxValueValidator(5)])),
                ('culture_values_rating', models.PositiveIntegerField(blank=True, help_text='Company culture and values rating', null=True, validators=[django.core.validators.MinValueValidator(1), django.core.validators.MaxValueValidator(5)])),
                ('job_title', models.CharField(help_text='Job title when working at this company', max_length=200)),
                ('employment_status', models.CharField(choices=[('current', 'Current Employee'), ('former', 'Former Employee'), ('intern', 'Former Intern')], default='former', help_text='Employment status', max_length=20)),
                ('employment_duration', models.CharField(blank=True, help_text='How long you worked there', max_length=100)),
                ('is_verified', models.BooleanField(default=False, help_text='Whether this review is from a verified employee')),
                ('is_approved', models.BooleanField(default=True, help_text='Whether this review is approved')),
                ('moderation_notes', models.TextField(blank=True, help_text='Notes from moderation')),
                ('helpful_votes', models.PositiveIntegerField(default=0, help_text='Number of helpful votes')),
                ('would_recommend', models.BooleanField(blank=True, help_text='Would recommend this company to a friend', null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('company', models.ForeignKey(help_text='Company being reviewed', on_delete=django.db.models.deletion.CASCADE, related_name='reviews', to='jobs.company')),
                ('moderated_by', models.ForeignKey(blank=True, help_text='Admin who moderated this review', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='moderated_company_reviews', to=settings.AUTH_USER_MODEL)),
                ('reviewer', models.ForeignKey(help_text='User writing the review', on_delete=django.db.models.deletion.CASCADE, related_name='company_reviews', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Company Review',
                'verbose_name_plural': 'Company Reviews',
                'db_table': 'jobs_companyreview',
                'ordering': ['-created_at'],
                'indexes': [models.Index(fields=['company', 'is_approved'], name='jobs_compan_company_f813bc_idx'), models.Index(fields=['overall_rating', '-created_at'], name='jobs_compan_overall_21dae4_idx'), models.Index(fields=['employment_status'], name='jobs_compan_employm_7fbebf_idx')],
                'unique_together': {('company', 'reviewer')},
            },
        ),
        migrations.CreateModel(
            name='JobApplication',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('status', models.CharField(choices=[('submitted', 'Submitted'), ('under_review', 'Under Review'), ('shortlisted', 'Shortlisted'), ('interview_scheduled', 'Interview Scheduled'), ('interviewed', 'Interviewed'), ('offer_made', 'Offer Made'), ('accepted', 'Accepted'), ('rejected', 'Rejected'), ('withdrawn', 'Withdrawn')], default='submitted', help_text='Application status', max_length=20)),
                ('cover_letter', models.TextField(help_text='Cover letter content')),
                ('resume', models.FileField(help_text='Resume/CV file for this application', upload_to='applications/resumes/')),
                ('additional_documents', models.FileField(blank=True, help_text='Additional documents (portfolio, certificates, etc.)', null=True, upload_to='applications/documents/')),
                ('expected_salary', models.DecimalField(blank=True, decimal_places=2, help_text='Expected salary for this position', max_digits=10, null=True)),
                ('available_start_date', models.DateField(blank=True, help_text='When can you start working', null=True)),
                ('additional_notes', models.TextField(blank=True, help_text='Additional notes or information')),
                ('employer_notes', models.TextField(blank=True, help_text='Internal notes from employer')),
                ('reviewed_at', models.DateTimeField(blank=True, help_text='When the application was reviewed', null=True)),
                ('interview_scheduled_date', models.DateTimeField(blank=True, help_text='Scheduled interview date and time', null=True)),
                ('interview_location', models.CharField(blank=True, help_text='Interview location or video call link', max_length=200)),
                ('interview_notes', models.TextField(blank=True, help_text='Interview notes and feedback')),
                ('applied_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('applicant', models.ForeignKey(help_text='User applying for the job', on_delete=django.db.models.deletion.CASCADE, related_name='job_applications', to=settings.AUTH_USER_MODEL)),
                ('job', models.ForeignKey(help_text='Job being applied for', on_delete=django.db.models.deletion.CASCADE, related_name='applications', to='jobs.job')),
                ('reviewed_by', models.ForeignKey(blank=True, help_text='Employer who reviewed this application', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='reviewed_applications', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Job Application',
                'verbose_name_plural': 'Job Applications',
                'db_table': 'jobs_application',
                'ordering': ['-applied_at'],
                'indexes': [models.Index(fields=['job', 'status'], name='jobs_applic_job_id_a25382_idx'), models.Index(fields=['applicant', 'status'], name='jobs_applic_applica_764d6a_idx'), models.Index(fields=['status', '-applied_at'], name='jobs_applic_status_ee8cc9_idx')],
                'unique_together': {('job', 'applicant')},
            },
        ),
        migrations.AddIndex(
            model_name='job',
            index=models.Index(fields=['status', '-created_at'], name='jobs_job_status_57b86b_idx'),
        ),
        migrations.AddIndex(
            model_name='job',
            index=models.Index(fields=['category', '-created_at'], name='jobs_job_categor_7d742a_idx'),
        ),
        migrations.AddIndex(
            model_name='job',
            index=models.Index(fields=['company', '-created_at'], name='jobs_job_company_f51e79_idx'),
        ),
        migrations.AddIndex(
            model_name='job',
            index=models.Index(fields=['job_type', '-created_at'], name='jobs_job_job_typ_88c366_idx'),
        ),
        migrations.AddIndex(
            model_name='job',
            index=models.Index(fields=['island', 'district'], name='jobs_job_island_409b45_idx'),
        ),
        migrations.AddIndex(
            model_name='job',
            index=models.Index(fields=['is_featured', '-created_at'], name='jobs_job_is_feat_e3be6a_idx'),
        ),
        migrations.AddIndex(
            model_name='job',
            index=models.Index(fields=['experience_level'], name='jobs_job_experie_09027f_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='savedjob',
            unique_together={('user', 'job')},
        ),
    ]
