"""
Models for shopping app.

Handles e-commerce functionality including products, vendors, orders, payments,
and shopping cart features for the Kominote marketplace with Seychelles focus.
"""
from django.db import models
from django.contrib.auth import get_user_model
from django.core.validators import MinValueValidator, MaxValueValidator
from django.urls import reverse
from decimal import Decimal

User = get_user_model()


class ProductCategory(models.Model):
    """
    Categories for products in the Seychelles marketplace.
    """
    name = models.CharField(
        max_length=100,
        unique=True,
        help_text="Category name"
    )
    slug = models.SlugField(
        max_length=100,
        unique=True,
        help_text="URL-friendly category name"
    )
    description = models.TextField(
        blank=True,
        help_text="Category description"
    )

    # Visual elements
    icon = models.ImageField(
        upload_to='shopping/categories/icons/',
        null=True,
        blank=True,
        help_text="Category icon"
    )
    banner_image = models.ImageField(
        upload_to='shopping/categories/banners/',
        null=True,
        blank=True,
        help_text="Category banner image"
    )

    # Seychelles-specific categories
    is_local_category = models.BooleanField(
        default=False,
        help_text="Whether this category focuses on Seychelles products"
    )

    # Hierarchy
    parent = models.ForeignKey(
        'self',
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name='subcategories',
        help_text="Parent category for hierarchical organization"
    )

    # Display settings
    is_active = models.BooleanField(
        default=True,
        help_text="Whether this category is active"
    )
    is_featured = models.BooleanField(
        default=False,
        help_text="Show in featured categories"
    )
    display_order = models.PositiveIntegerField(
        default=1,
        help_text="Display order"
    )

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'shopping_productcategory'
        verbose_name = 'Product Category'
        verbose_name_plural = 'Product Categories'
        ordering = ['display_order', 'name']

    def __str__(self):
        return self.name

    def get_absolute_url(self):
        return reverse('shopping:category', kwargs={'slug': self.slug})

    @property
    def product_count(self):
        """Return number of products in this category."""
        return self.products.filter(is_active=True).count()


class Vendor(models.Model):
    """
    Vendors/sellers in the Seychelles marketplace.
    """
    VENDOR_TYPES = [
        ('individual', 'Individual Seller'),
        ('business', 'Business'),
        ('cooperative', 'Cooperative'),
        ('government', 'Government Entity'),
        ('ngo', 'NGO/Non-Profit'),
    ]

    user = models.OneToOneField(
        User,
        on_delete=models.CASCADE,
        related_name='vendor_profile'
    )

    # Business Information
    business_name = models.CharField(
        max_length=200,
        help_text="Business or vendor name"
    )
    business_registration = models.CharField(
        max_length=100,
        blank=True,
        help_text="Business registration number"
    )
    vendor_type = models.CharField(
        max_length=20,
        choices=VENDOR_TYPES,
        default='individual',
        help_text="Type of vendor"
    )

    # Contact Information
    business_email = models.EmailField(
        blank=True,
        help_text="Business email address"
    )
    business_phone = models.CharField(
        max_length=20,
        blank=True,
        help_text="Business phone number"
    )

    # Location (Seychelles)
    island = models.CharField(
        max_length=20,
        choices=User.ISLAND_CHOICES,
        blank=True,
        help_text="Island location"
    )
    district = models.CharField(
        max_length=100,
        blank=True,
        help_text="District or area"
    )
    address = models.TextField(
        blank=True,
        help_text="Full business address"
    )

    # Business Details
    description = models.TextField(
        blank=True,
        help_text="Business description"
    )
    logo = models.ImageField(
        upload_to='shopping/vendors/logos/',
        null=True,
        blank=True,
        help_text="Business logo"
    )
    banner = models.ImageField(
        upload_to='shopping/vendors/banners/',
        null=True,
        blank=True,
        help_text="Business banner image"
    )

    # Verification and Status
    is_verified = models.BooleanField(
        default=False,
        help_text="Whether the vendor is verified"
    )
    is_active = models.BooleanField(
        default=True,
        help_text="Whether the vendor account is active"
    )
    is_featured = models.BooleanField(
        default=False,
        help_text="Featured vendor status"
    )

    # Business Hours
    business_hours = models.TextField(
        blank=True,
        help_text="Business operating hours"
    )

    # Statistics
    total_sales = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        default=0.00,
        help_text="Total sales amount"
    )
    total_orders = models.PositiveIntegerField(
        default=0,
        help_text="Total number of orders"
    )
    average_rating = models.DecimalField(
        max_digits=3,
        decimal_places=2,
        default=0.00,
        help_text="Average vendor rating"
    )
    rating_count = models.PositiveIntegerField(
        default=0,
        help_text="Number of ratings"
    )

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'shopping_vendor'
        verbose_name = 'Vendor'
        verbose_name_plural = 'Vendors'
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.business_name} ({self.user.get_display_name()})"

    def get_absolute_url(self):
        return reverse('shopping:vendor_detail', kwargs={'pk': self.pk})


class Product(models.Model):
    """
    Products in the Seychelles marketplace.
    """
    CONDITION_CHOICES = [
        ('new', 'New'),
        ('like_new', 'Like New'),
        ('good', 'Good'),
        ('fair', 'Fair'),
        ('poor', 'Poor'),
    ]

    # Basic Information
    name = models.CharField(
        max_length=200,
        help_text="Product name"
    )
    slug = models.SlugField(
        max_length=200,
        unique=True,
        help_text="URL-friendly product name"
    )
    description = models.TextField(
        help_text="Product description"
    )
    short_description = models.CharField(
        max_length=300,
        blank=True,
        help_text="Short description for listings"
    )

    # Vendor and Category
    vendor = models.ForeignKey(
        Vendor,
        on_delete=models.CASCADE,
        related_name='products'
    )
    category = models.ForeignKey(
        ProductCategory,
        on_delete=models.SET_NULL,
        null=True,
        related_name='products'
    )

    # Pricing
    price = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        validators=[MinValueValidator(Decimal('0.01'))],
        help_text="Product price in SCR"
    )
    original_price = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        null=True,
        blank=True,
        help_text="Original price (for discounts)"
    )

    # Inventory
    stock_quantity = models.PositiveIntegerField(
        default=0,
        help_text="Available stock quantity"
    )
    low_stock_threshold = models.PositiveIntegerField(
        default=5,
        help_text="Low stock warning threshold"
    )
    track_inventory = models.BooleanField(
        default=True,
        help_text="Whether to track inventory for this product"
    )

    # Product Details
    condition = models.CharField(
        max_length=20,
        choices=CONDITION_CHOICES,
        default='new',
        help_text="Product condition"
    )
    brand = models.CharField(
        max_length=100,
        blank=True,
        help_text="Product brand"
    )
    model = models.CharField(
        max_length=100,
        blank=True,
        help_text="Product model"
    )
    weight = models.DecimalField(
        max_digits=8,
        decimal_places=2,
        null=True,
        blank=True,
        help_text="Product weight in kg"
    )
    dimensions = models.CharField(
        max_length=100,
        blank=True,
        help_text="Product dimensions (L x W x H)"
    )

    # Seychelles Context
    is_local_product = models.BooleanField(
        default=False,
        help_text="Whether this is a locally made/sourced product"
    )
    origin_island = models.CharField(
        max_length=20,
        choices=User.ISLAND_CHOICES,
        blank=True,
        help_text="Island of origin/production"
    )
    local_materials = models.BooleanField(
        default=False,
        help_text="Made with local Seychelles materials"
    )

    # Media
    featured_image = models.ImageField(
        upload_to='shopping/products/',
        null=True,
        blank=True,
        help_text="Main product image"
    )

    # SEO and Marketing
    meta_title = models.CharField(
        max_length=200,
        blank=True,
        help_text="SEO meta title"
    )
    meta_description = models.CharField(
        max_length=300,
        blank=True,
        help_text="SEO meta description"
    )
    tags = models.CharField(
        max_length=500,
        blank=True,
        help_text="Product tags (comma-separated)"
    )

    # Status and Visibility
    is_active = models.BooleanField(
        default=True,
        help_text="Whether the product is active and visible"
    )
    is_featured = models.BooleanField(
        default=False,
        help_text="Featured product status"
    )
    is_digital = models.BooleanField(
        default=False,
        help_text="Whether this is a digital product"
    )

    # Statistics
    view_count = models.PositiveIntegerField(
        default=0,
        help_text="Number of product views"
    )
    sales_count = models.PositiveIntegerField(
        default=0,
        help_text="Number of times sold"
    )
    average_rating = models.DecimalField(
        max_digits=3,
        decimal_places=2,
        default=0.00,
        help_text="Average product rating"
    )
    rating_count = models.PositiveIntegerField(
        default=0,
        help_text="Number of ratings"
    )

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'shopping_product'
        verbose_name = 'Product'
        verbose_name_plural = 'Products'
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['category', '-created_at']),
            models.Index(fields=['vendor', '-created_at']),
            models.Index(fields=['is_featured', '-created_at']),
            models.Index(fields=['is_local_product', '-created_at']),
        ]

    def __str__(self):
        return self.name

    def get_absolute_url(self):
        return reverse('shopping:product_detail', kwargs={'slug': self.slug})

    @property
    def is_on_sale(self):
        """Check if product is on sale."""
        return self.original_price and self.original_price > self.price

    @property
    def discount_percentage(self):
        """Calculate discount percentage."""
        if self.is_on_sale:
            return int(((self.original_price - self.price) / self.original_price) * 100)
        return 0

    @property
    def is_in_stock(self):
        """Check if product is in stock."""
        if not self.track_inventory:
            return True
        return self.stock_quantity > 0

    @property
    def is_low_stock(self):
        """Check if product is low in stock."""
        if not self.track_inventory:
            return False
        return self.stock_quantity <= self.low_stock_threshold


class ProductImage(models.Model):
    """
    Additional images for products.
    """
    product = models.ForeignKey(
        Product,
        on_delete=models.CASCADE,
        related_name='images'
    )
    image = models.ImageField(
        upload_to='shopping/products/gallery/',
        help_text="Product image"
    )
    alt_text = models.CharField(
        max_length=200,
        blank=True,
        help_text="Alternative text for accessibility"
    )
    display_order = models.PositiveIntegerField(
        default=1,
        help_text="Display order"
    )

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        db_table = 'shopping_productimage'
        verbose_name = 'Product Image'
        verbose_name_plural = 'Product Images'
        ordering = ['display_order', 'created_at']

    def __str__(self):
        return f"Image for {self.product.name}"


class ShoppingCart(models.Model):
    """
    Shopping cart for users.
    """
    user = models.OneToOneField(
        User,
        on_delete=models.CASCADE,
        related_name='shopping_cart'
    )

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'shopping_shoppingcart'
        verbose_name = 'Shopping Cart'
        verbose_name_plural = 'Shopping Carts'

    def __str__(self):
        return f"{self.user.get_display_name()}'s Cart"

    @property
    def total_items(self):
        """Return total number of items in cart."""
        return sum(item.quantity for item in self.items.all())

    @property
    def total_amount(self):
        """Return total amount of items in cart."""
        return sum(item.total_price for item in self.items.all())


class CartItem(models.Model):
    """
    Items in a shopping cart.
    """
    cart = models.ForeignKey(
        ShoppingCart,
        on_delete=models.CASCADE,
        related_name='items'
    )
    product = models.ForeignKey(
        Product,
        on_delete=models.CASCADE,
        related_name='cart_items'
    )
    quantity = models.PositiveIntegerField(
        default=1,
        validators=[MinValueValidator(1)],
        help_text="Quantity of the product"
    )

    # Timestamps
    added_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'shopping_cartitem'
        verbose_name = 'Cart Item'
        verbose_name_plural = 'Cart Items'
        unique_together = ['cart', 'product']
        ordering = ['-added_at']

    def __str__(self):
        return f"{self.quantity}x {self.product.name}"

    @property
    def total_price(self):
        """Return total price for this cart item."""
        return self.product.price * self.quantity


class Order(models.Model):
    """
    Customer orders.
    """
    STATUS_CHOICES = [
        ('pending', 'Pending'),
        ('confirmed', 'Confirmed'),
        ('processing', 'Processing'),
        ('shipped', 'Shipped'),
        ('delivered', 'Delivered'),
        ('cancelled', 'Cancelled'),
        ('refunded', 'Refunded'),
    ]

    PAYMENT_STATUS_CHOICES = [
        ('pending', 'Pending'),
        ('paid', 'Paid'),
        ('failed', 'Failed'),
        ('refunded', 'Refunded'),
    ]

    # Order Identification
    order_number = models.CharField(
        max_length=20,
        unique=True,
        help_text="Unique order number"
    )

    # Customer Information
    customer = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='orders'
    )

    # Order Status
    status = models.CharField(
        max_length=20,
        choices=STATUS_CHOICES,
        default='pending',
        help_text="Order status"
    )
    payment_status = models.CharField(
        max_length=20,
        choices=PAYMENT_STATUS_CHOICES,
        default='pending',
        help_text="Payment status"
    )

    # Pricing
    subtotal = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        help_text="Subtotal amount"
    )
    shipping_cost = models.DecimalField(
        max_digits=8,
        decimal_places=2,
        default=0.00,
        help_text="Shipping cost"
    )
    tax_amount = models.DecimalField(
        max_digits=8,
        decimal_places=2,
        default=0.00,
        help_text="Tax amount"
    )
    total_amount = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        help_text="Total order amount"
    )

    # Shipping Information
    shipping_name = models.CharField(
        max_length=200,
        help_text="Shipping recipient name"
    )
    shipping_email = models.EmailField(
        help_text="Shipping email"
    )
    shipping_phone = models.CharField(
        max_length=20,
        help_text="Shipping phone number"
    )
    shipping_island = models.CharField(
        max_length=20,
        choices=User.ISLAND_CHOICES,
        help_text="Shipping island"
    )
    shipping_district = models.CharField(
        max_length=100,
        help_text="Shipping district"
    )
    shipping_address = models.TextField(
        help_text="Full shipping address"
    )

    # Order Notes
    customer_notes = models.TextField(
        blank=True,
        help_text="Customer notes for the order"
    )
    admin_notes = models.TextField(
        blank=True,
        help_text="Admin notes (internal)"
    )

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    shipped_at = models.DateTimeField(
        null=True,
        blank=True,
        help_text="When the order was shipped"
    )
    delivered_at = models.DateTimeField(
        null=True,
        blank=True,
        help_text="When the order was delivered"
    )

    class Meta:
        db_table = 'shopping_order'
        verbose_name = 'Order'
        verbose_name_plural = 'Orders'
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['customer', '-created_at']),
            models.Index(fields=['status', '-created_at']),
            models.Index(fields=['payment_status', '-created_at']),
        ]

    def __str__(self):
        return f"Order {self.order_number}"

    def save(self, *args, **kwargs):
        """Generate order number if not set."""
        if not self.order_number:
            self.order_number = self.generate_order_number()
        super().save(*args, **kwargs)

    def generate_order_number(self):
        """Generate unique order number."""
        import random
        import string
        while True:
            number = 'ORD-' + ''.join(random.choices(string.digits, k=8))
            if not Order.objects.filter(order_number=number).exists():
                return number


class OrderItem(models.Model):
    """
    Items within an order.
    """
    order = models.ForeignKey(
        Order,
        on_delete=models.CASCADE,
        related_name='items'
    )
    product = models.ForeignKey(
        Product,
        on_delete=models.CASCADE,
        related_name='order_items'
    )
    vendor = models.ForeignKey(
        Vendor,
        on_delete=models.CASCADE,
        related_name='order_items'
    )

    # Product details at time of order
    product_name = models.CharField(
        max_length=200,
        help_text="Product name at time of order"
    )
    product_price = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        help_text="Product price at time of order"
    )
    quantity = models.PositiveIntegerField(
        help_text="Quantity ordered"
    )

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        db_table = 'shopping_orderitem'
        verbose_name = 'Order Item'
        verbose_name_plural = 'Order Items'
        ordering = ['created_at']

    def __str__(self):
        return f"{self.quantity}x {self.product_name}"

    @property
    def total_price(self):
        """Return total price for this order item."""
        return self.product_price * self.quantity


class ProductReview(models.Model):
    """
    Product reviews and ratings.
    """
    product = models.ForeignKey(
        Product,
        on_delete=models.CASCADE,
        related_name='reviews'
    )
    customer = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='product_reviews'
    )
    order_item = models.ForeignKey(
        OrderItem,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='review',
        help_text="Order item this review is for (if applicable)"
    )

    # Review Content
    rating = models.PositiveIntegerField(
        validators=[MinValueValidator(1), MaxValueValidator(5)],
        help_text="Rating from 1 to 5 stars"
    )
    title = models.CharField(
        max_length=200,
        help_text="Review title"
    )
    content = models.TextField(
        help_text="Review content"
    )

    # Review Images
    image1 = models.ImageField(
        upload_to='shopping/reviews/',
        null=True,
        blank=True,
        help_text="Review image 1"
    )
    image2 = models.ImageField(
        upload_to='shopping/reviews/',
        null=True,
        blank=True,
        help_text="Review image 2"
    )
    image3 = models.ImageField(
        upload_to='shopping/reviews/',
        null=True,
        blank=True,
        help_text="Review image 3"
    )

    # Moderation
    is_approved = models.BooleanField(
        default=True,
        help_text="Whether the review is approved for display"
    )
    is_verified_purchase = models.BooleanField(
        default=False,
        help_text="Whether this is from a verified purchase"
    )

    # Helpfulness
    helpful_count = models.PositiveIntegerField(
        default=0,
        help_text="Number of users who found this helpful"
    )

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'shopping_productreview'
        verbose_name = 'Product Review'
        verbose_name_plural = 'Product Reviews'
        ordering = ['-created_at']
        unique_together = ['product', 'customer']

    def __str__(self):
        return f"{self.customer.get_display_name()}'s review of {self.product.name}"


class Wishlist(models.Model):
    """
    User wishlists for saving products.
    """
    user = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='wishlists'
    )
    name = models.CharField(
        max_length=100,
        help_text="Wishlist name"
    )
    description = models.TextField(
        blank=True,
        help_text="Wishlist description"
    )

    # Privacy
    is_public = models.BooleanField(
        default=False,
        help_text="Whether this wishlist is public"
    )

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'shopping_wishlist'
        verbose_name = 'Wishlist'
        verbose_name_plural = 'Wishlists'
        ordering = ['-updated_at']

    def __str__(self):
        return f"{self.user.get_display_name()}'s {self.name}"

    @property
    def item_count(self):
        """Return number of items in wishlist."""
        return self.items.count()


class WishlistItem(models.Model):
    """
    Items in a wishlist.
    """
    wishlist = models.ForeignKey(
        Wishlist,
        on_delete=models.CASCADE,
        related_name='items'
    )
    product = models.ForeignKey(
        Product,
        on_delete=models.CASCADE,
        related_name='wishlist_items'
    )

    # Timestamps
    added_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        db_table = 'shopping_wishlistitem'
        verbose_name = 'Wishlist Item'
        verbose_name_plural = 'Wishlist Items'
        unique_together = ['wishlist', 'product']
        ordering = ['-added_at']

    def __str__(self):
        return f"{self.product.name} in {self.wishlist.name}"
