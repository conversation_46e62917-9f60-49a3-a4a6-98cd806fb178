"""
URL configuration for landing app.

Handles landing page content, hero sections, features, and public-facing content.
"""
from django.urls import path
from . import views

app_name = 'landing'

urlpatterns = [
    # Landing page routes
    path('', views.LandingPageView.as_view(), name='home'),
    path('about/', views.AboutView.as_view(), name='about'),
    path('features/', views.FeaturesView.as_view(), name='features'),
    path('contact/', views.ContactView.as_view(), name='contact'),
    
    # API endpoints for landing content
    path('api/hero/', views.HeroContentAPIView.as_view(), name='hero-api'),
    path('api/features/', views.FeaturesAPIView.as_view(), name='features-api'),
    path('api/testimonials/', views.TestimonialsAPIView.as_view(), name='testimonials-api'),
]
