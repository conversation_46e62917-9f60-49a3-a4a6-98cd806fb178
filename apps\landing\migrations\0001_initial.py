# Generated by Django 4.2.21 on 2025-05-30 10:14

from django.conf import settings
import django.core.validators
from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Feature',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(help_text='Feature title', max_length=100)),
                ('description', models.TextField(help_text='Feature description', max_length=300)),
                ('category', models.CharField(choices=[('streaming', 'Streaming'), ('shopping', 'Shopping'), ('community', 'Community'), ('events', 'Events'), ('jobs', 'Jobs'), ('general', 'General')], default='general', help_text='Feature category', max_length=20)),
                ('icon', models.ImageField(blank=True, help_text='Feature icon', null=True, upload_to='landing/features/icons/')),
                ('image', models.ImageField(blank=True, help_text='Feature image', null=True, upload_to='landing/features/')),
                ('learn_more_url', models.URLField(blank=True, help_text='Learn more link')),
                ('is_featured', models.BooleanField(default=False, help_text='Show on main landing page')),
                ('is_active', models.BooleanField(default=True, help_text='Whether this feature is active')),
                ('display_order', models.PositiveIntegerField(default=1, help_text='Display order')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'Feature',
                'verbose_name_plural': 'Features',
                'db_table': 'landing_feature',
                'ordering': ['display_order', 'title'],
            },
        ),
        migrations.CreateModel(
            name='Testimonial',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(help_text='Testimonial author name', max_length=100)),
                ('title', models.CharField(blank=True, help_text='Author title or position', max_length=100)),
                ('company', models.CharField(blank=True, help_text='Author company', max_length=100)),
                ('content', models.TextField(help_text='Testimonial content', max_length=500)),
                ('rating', models.PositiveIntegerField(default=5, help_text='Rating out of 5 stars', validators=[django.core.validators.MinValueValidator(1), django.core.validators.MaxValueValidator(5)])),
                ('avatar', models.ImageField(blank=True, help_text='Author avatar', null=True, upload_to='landing/testimonials/')),
                ('location', models.CharField(blank=True, help_text='Author location (e.g., Mahé, Praslin)', max_length=100)),
                ('is_featured', models.BooleanField(default=False, help_text='Show on main landing page')),
                ('is_active', models.BooleanField(default=True, help_text='Whether this testimonial is active')),
                ('display_order', models.PositiveIntegerField(default=1, help_text='Display order')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('approved_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='approved_testimonials', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Testimonial',
                'verbose_name_plural': 'Testimonials',
                'db_table': 'landing_testimonial',
                'ordering': ['display_order', '-created_at'],
            },
        ),
        migrations.CreateModel(
            name='NewsUpdate',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(help_text='Update title', max_length=200)),
                ('content', models.TextField(help_text='Update content')),
                ('update_type', models.CharField(choices=[('feature', 'New Feature'), ('improvement', 'Improvement'), ('bugfix', 'Bug Fix'), ('announcement', 'Announcement'), ('event', 'Event'), ('maintenance', 'Maintenance')], default='announcement', help_text='Type of update', max_length=20)),
                ('featured_image', models.ImageField(blank=True, help_text='Featured image for the update', null=True, upload_to='landing/news/')),
                ('read_more_url', models.URLField(blank=True, help_text='Link to full article or details')),
                ('is_featured', models.BooleanField(default=False, help_text='Show on main landing page')),
                ('is_active', models.BooleanField(default=True, help_text='Whether this update is active')),
                ('publish_date', models.DateTimeField(default=django.utils.timezone.now, help_text='When to publish this update')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_news', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'News Update',
                'verbose_name_plural': 'News Updates',
                'db_table': 'landing_newsupdate',
                'ordering': ['-publish_date'],
            },
        ),
        migrations.CreateModel(
            name='HeroSection',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(help_text='Main hero title', max_length=200)),
                ('subtitle', models.CharField(blank=True, help_text='Hero subtitle or tagline', max_length=300)),
                ('description', models.TextField(blank=True, help_text='Hero description text', max_length=500)),
                ('cta_text', models.CharField(default='Get Started', help_text='Call-to-action button text', max_length=50)),
                ('cta_url', models.URLField(blank=True, help_text='Call-to-action button URL')),
                ('background_image', models.ImageField(blank=True, help_text='Hero background image', null=True, upload_to='landing/hero/')),
                ('background_video', models.FileField(blank=True, help_text='Hero background video (optional)', null=True, upload_to='landing/hero/videos/')),
                ('is_active', models.BooleanField(default=True, help_text='Whether this hero section is currently active')),
                ('display_order', models.PositiveIntegerField(default=1, help_text='Display order (lower numbers appear first)')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_hero_sections', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Hero Section',
                'verbose_name_plural': 'Hero Sections',
                'db_table': 'landing_herosection',
                'ordering': ['display_order', '-created_at'],
            },
        ),
        migrations.CreateModel(
            name='FAQ',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('question', models.CharField(help_text='FAQ question', max_length=200)),
                ('answer', models.TextField(help_text='FAQ answer')),
                ('category', models.CharField(choices=[('general', 'General'), ('streaming', 'Streaming'), ('shopping', 'Shopping'), ('community', 'Community'), ('events', 'Events'), ('jobs', 'Jobs'), ('account', 'Account'), ('technical', 'Technical')], default='general', help_text='FAQ category', max_length=20)),
                ('is_featured', models.BooleanField(default=False, help_text='Show on main landing page')),
                ('is_active', models.BooleanField(default=True, help_text='Whether this FAQ is active')),
                ('display_order', models.PositiveIntegerField(default=1, help_text='Display order')),
                ('view_count', models.PositiveIntegerField(default=0, help_text='Number of times this FAQ was viewed')),
                ('helpful_count', models.PositiveIntegerField(default=0, help_text='Number of times marked as helpful')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_faqs', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'FAQ',
                'verbose_name_plural': 'FAQs',
                'db_table': 'landing_faq',
                'ordering': ['display_order', 'question'],
            },
        ),
        migrations.CreateModel(
            name='ContactMessage',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(help_text='Sender name', max_length=100)),
                ('email', models.EmailField(help_text='Sender email', max_length=254)),
                ('phone', models.CharField(blank=True, help_text='Sender phone number', max_length=20)),
                ('message_type', models.CharField(choices=[('general', 'General Inquiry'), ('support', 'Support Request'), ('business', 'Business Inquiry'), ('partnership', 'Partnership'), ('feedback', 'Feedback'), ('bug_report', 'Bug Report')], default='general', help_text='Type of message', max_length=20)),
                ('subject', models.CharField(help_text='Message subject', max_length=200)),
                ('message', models.TextField(help_text='Message content')),
                ('is_read', models.BooleanField(default=False, help_text='Whether the message has been read')),
                ('is_replied', models.BooleanField(default=False, help_text='Whether the message has been replied to')),
                ('response', models.TextField(blank=True, help_text='Admin response to the message')),
                ('responded_at', models.DateTimeField(blank=True, help_text='When the response was sent', null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('responded_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='contact_responses', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Contact Message',
                'verbose_name_plural': 'Contact Messages',
                'db_table': 'landing_contactmessage',
                'ordering': ['-created_at'],
            },
        ),
    ]
