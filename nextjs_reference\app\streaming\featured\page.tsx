"use client"

import Link from "next/link"
import Image from "next/image"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Search, Filter, Play, Info } from "lucide-react"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { UserNav } from "@/components/user-nav"
import FeatureNavigation from "@/components/feature-navigation"

export default function FeaturedPage() {
  return (
    <div className="min-h-screen bg-gray-900 text-white">
      {/* Header */}
      <header className="sticky top-0 z-50 w-full bg-gradient-to-b from-black/80 to-transparent backdrop-blur-sm">
        <div className="container flex h-16 items-center justify-between px-4">
          <div className="flex items-center gap-8">
            <Link href="/" className="flex items-center space-x-2">
              <span className="text-2xl font-bold bg-gradient-to-r from-blue-600 to-teal-500 bg-clip-text text-transparent">
                Kominote
              </span>
            </Link>
            <nav className="hidden md:flex gap-6">
              <Link href="/streaming" className="text-sm font-medium text-gray-300 hover:text-white">
                Home
              </Link>
              <Link href="/streaming/series" className="text-sm font-medium text-gray-300 hover:text-white">
                Series
              </Link>
              <Link href="/streaming/movies" className="text-sm font-medium text-gray-300 hover:text-white">
                Movies
              </Link>
              <Link href="/streaming/new" className="text-sm font-medium text-gray-300 hover:text-white">
                New & Popular
              </Link>
              <Link href="/streaming/mylist" className="text-sm font-medium text-gray-300 hover:text-white">
                My List
              </Link>
            </nav>
          </div>
          <div className="flex items-center gap-4">
            <div className="relative hidden md:flex items-center">
              <Search className="absolute left-2 h-4 w-4 text-gray-400" />
              <Input
                type="search"
                placeholder="Search featured content..."
                className="w-[220px] pl-8 bg-black/20 border-gray-700 focus:border-blue-500 text-sm text-white placeholder:text-gray-400"
              />
            </div>
            <Button variant="ghost" size="icon" className="text-gray-300 hover:text-white md:hidden">
              <Search className="h-5 w-5" />
              <span className="sr-only">Search</span>
            </Button>
            <UserNav />
          </div>
        </div>
      </header>

      <main>
        {/* Hero Banner */}
        <section className="relative h-[50vh] w-full">
          <Image
            src="/placeholder.svg?height=1080&width=1920"
            fill
            alt="Featured Content"
            className="object-cover"
            priority
          />
          <div className="absolute inset-0 bg-gradient-to-t from-gray-900 via-gray-900/60 to-transparent" />
          <div className="absolute inset-0 bg-gradient-to-r from-gray-900/90 to-transparent" />
          <div className="absolute bottom-0 left-0 right-0 top-0 flex flex-col justify-center px-4 md:px-12 space-y-4">
            <div className="max-w-lg">
              <Badge className="mb-4 bg-blue-600">Featured</Badge>
              <h1 className="text-4xl md:text-5xl font-bold mb-4">Featured Content</h1>
              <p className="text-lg text-gray-200 mb-6">
                Discover our handpicked selection of the best Seychellois productions, showcasing the talent and
                creativity of our local filmmakers.
              </p>
            </div>
          </div>
        </section>

        {/* Filter and Search Section */}
        <section className="py-6 px-4 md:px-12 bg-gray-900">
          <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
            <div className="relative w-full md:w-auto">
              <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-gray-400" />
              <Input
                type="search"
                placeholder="Search featured content..."
                className="w-full md:w-[300px] pl-10 bg-gray-800 border-gray-700 focus:border-blue-500 text-sm text-white placeholder:text-gray-400"
              />
            </div>
            <div className="flex items-center gap-3 w-full md:w-auto">
              <Button variant="outline" size="sm" className="border-gray-700 text-gray-300 hover:text-white">
                <Filter className="mr-2 h-4 w-4" /> Filter
              </Button>
              <select className="bg-gray-800 border border-gray-700 rounded-md text-sm p-2 text-gray-300">
                <option>Sort By: Featured</option>
                <option>Sort By: Newest</option>
                <option>Sort By: Most Viewed</option>
                <option>Sort By: Highest Rated</option>
              </select>
            </div>
          </div>
        </section>

        {/* Featured Content */}
        <section className="py-8 px-4 md:px-12">
          <Tabs defaultValue="all" className="w-full">
            <TabsList className="bg-gray-800/60 mb-6">
              <TabsTrigger value="all" className="data-[state=active]:bg-blue-600">
                All Featured
              </TabsTrigger>
              <TabsTrigger value="series" className="data-[state=active]:bg-blue-600">
                Series
              </TabsTrigger>
              <TabsTrigger value="movies" className="data-[state=active]:bg-blue-600">
                Movies
              </TabsTrigger>
              <TabsTrigger value="documentaries" className="data-[state=active]:bg-blue-600">
                Documentaries
              </TabsTrigger>
            </TabsList>

            <TabsContent value="all" className="mt-0">
              {/* Editor's Picks */}
              <div className="mb-10">
                <div className="flex items-center justify-between mb-4">
                  <h2 className="text-xl font-bold">Editor's Picks</h2>
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {[
                    {
                      id: "seychelles-untold",
                      title: "Seychelles Untold",
                      type: "series",
                      genre: "Documentary",
                      description:
                        "Discover the hidden stories of Seychelles in this captivating documentary series. From ancient traditions to modern challenges, explore the authentic culture of our islands.",
                    },
                    {
                      id: "island-paradise",
                      title: "Island Paradise",
                      type: "movie",
                      genre: "Documentary",
                      description:
                        "Experience the breathtaking beauty of Seychelles in this stunning documentary showcasing the islands' natural wonders and vibrant culture.",
                    },
                  ].map((content) => (
                    <FeaturedLargeCard
                      key={content.id}
                      id={content.id}
                      title={content.title}
                      type={content.type}
                      genre={content.genre}
                      description={content.description}
                    />
                  ))}
                </div>
              </div>

              {/* Award Winners */}
              <div className="mb-10">
                <div className="flex items-center justify-between mb-4">
                  <h2 className="text-xl font-bold">Award Winners</h2>
                </div>
                <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-4">
                  {[
                    {
                      id: "coral-reef-chronicles",
                      title: "Coral Reef Chronicles",
                      type: "series",
                      genre: "Documentary",
                      award: "Best Documentary Series",
                    },
                    {
                      id: "mahé-mysteries",
                      title: "Mahé Mysteries",
                      type: "series",
                      genre: "Drama",
                      award: "Best Drama Series",
                    },
                    {
                      id: "ocean-guardians",
                      title: "Ocean Guardians",
                      type: "movie",
                      genre: "Documentary",
                      award: "Best Environmental Film",
                    },
                    {
                      id: "the-last-fisherman",
                      title: "The Last Fisherman",
                      type: "movie",
                      genre: "Drama",
                      award: "Best Screenplay",
                    },
                    {
                      id: "creole-kitchen",
                      title: "Creole Kitchen",
                      type: "series",
                      genre: "Cultural",
                      award: "Best Cultural Series",
                    },
                  ].map((content) => (
                    <AwardWinnerCard
                      key={content.id}
                      id={content.id}
                      title={content.title}
                      type={content.type}
                      genre={content.genre}
                      award={content.award}
                    />
                  ))}
                </div>
              </div>

              {/* Staff Picks */}
              <div className="mb-10">
                <div className="flex items-center justify-between mb-4">
                  <h2 className="text-xl font-bold">Staff Picks</h2>
                </div>
                <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-4">
                  {[
                    {
                      id: "island-beats",
                      title: "Island Beats",
                      type: "series",
                      genre: "Cultural",
                      staff: "Marie, Content Curator",
                    },
                    {
                      id: "seychelles-from-above",
                      title: "Seychelles From Above",
                      type: "movie",
                      genre: "Documentary",
                      staff: "Jean, Director",
                    },
                    {
                      id: "praslin-stories",
                      title: "Praslin Stories",
                      type: "series",
                      genre: "Drama",
                      staff: "Sarah, Producer",
                    },
                    {
                      id: "festival-of-lights",
                      title: "Festival of Lights",
                      type: "movie",
                      genre: "Cultural",
                      staff: "Thomas, Cinematographer",
                    },
                    {
                      id: "bird-island",
                      title: "Bird Island",
                      type: "series",
                      genre: "Documentary",
                      staff: "David, Wildlife Expert",
                    },
                  ].map((content) => (
                    <StaffPickCard
                      key={content.id}
                      id={content.id}
                      title={content.title}
                      type={content.type}
                      genre={content.genre}
                      staff={content.staff}
                    />
                  ))}
                </div>
              </div>
            </TabsContent>

            <TabsContent value="series" className="mt-0">
              <div className="mb-10">
                <div className="flex items-center justify-between mb-4">
                  <h2 className="text-xl font-bold">Featured Series</h2>
                </div>
                <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-4">
                  {[
                    { id: "seychelles-untold", title: "Seychelles Untold", type: "series", genre: "Documentary" },
                    {
                      id: "coral-reef-chronicles",
                      title: "Coral Reef Chronicles",
                      type: "series",
                      genre: "Documentary",
                    },
                    { id: "mahé-mysteries", title: "Mahé Mysteries", type: "series", genre: "Drama" },
                    { id: "creole-kitchen", title: "Creole Kitchen", type: "series", genre: "Cultural" },
                    { id: "island-beats", title: "Island Beats", type: "series", genre: "Cultural" },
                    { id: "praslin-stories", title: "Praslin Stories", type: "series", genre: "Drama" },
                    { id: "bird-island", title: "Bird Island", type: "series", genre: "Documentary" },
                    { id: "marine-life", title: "Marine Life of Seychelles", type: "series", genre: "Documentary" },
                    { id: "conservation-heroes", title: "Conservation Heroes", type: "series", genre: "Documentary" },
                    { id: "traditional-dance", title: "Traditional Dance", type: "series", genre: "Cultural" },
                  ].map((content) => (
                    <FeaturedCard
                      key={content.id}
                      id={content.id}
                      title={content.title}
                      type={content.type}
                      genre={content.genre}
                    />
                  ))}
                </div>
              </div>
            </TabsContent>

            <TabsContent value="movies" className="mt-0">
              <div className="mb-10">
                <div className="flex items-center justify-between mb-4">
                  <h2 className="text-xl font-bold">Featured Movies</h2>
                </div>
                <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-4">
                  {[
                    { id: "island-paradise", title: "Island Paradise", type: "movie", genre: "Documentary" },
                    {
                      id: "seychelles-from-above",
                      title: "Seychelles From Above",
                      type: "movie",
                      genre: "Documentary",
                    },
                    { id: "ocean-guardians", title: "Ocean Guardians", type: "movie", genre: "Documentary" },
                    { id: "the-last-fisherman", title: "The Last Fisherman", type: "movie", genre: "Drama" },
                    { id: "festival-of-lights", title: "Festival of Lights", type: "movie", genre: "Cultural" },
                    { id: "hidden-beaches", title: "Hidden Beaches", type: "movie", genre: "Documentary" },
                    { id: "aldabra-atoll", title: "Aldabra Atoll", type: "movie", genre: "Documentary" },
                    { id: "island-life", title: "Island Life", type: "movie", genre: "Comedy" },
                    { id: "island-dreams", title: "Island Dreams", type: "movie", genre: "Drama" },
                    { id: "creole-heritage", title: "Creole Heritage", type: "movie", genre: "Cultural" },
                  ].map((content) => (
                    <FeaturedCard
                      key={content.id}
                      id={content.id}
                      title={content.title}
                      type={content.type}
                      genre={content.genre}
                    />
                  ))}
                </div>
              </div>
            </TabsContent>

            <TabsContent value="documentaries" className="mt-0">
              <div className="mb-10">
                <div className="flex items-center justify-between mb-4">
                  <h2 className="text-xl font-bold">Featured Documentaries</h2>
                </div>
                <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-4">
                  {[
                    { id: "seychelles-untold", title: "Seychelles Untold", type: "series", genre: "Documentary" },
                    { id: "island-paradise", title: "Island Paradise", type: "movie", genre: "Documentary" },
                    {
                      id: "coral-reef-chronicles",
                      title: "Coral Reef Chronicles",
                      type: "series",
                      genre: "Documentary",
                    },
                    {
                      id: "seychelles-from-above",
                      title: "Seychelles From Above",
                      type: "movie",
                      genre: "Documentary",
                    },
                    { id: "ocean-guardians", title: "Ocean Guardians", type: "movie", genre: "Documentary" },
                    { id: "hidden-beaches", title: "Hidden Beaches", type: "movie", genre: "Documentary" },
                    { id: "bird-island", title: "Bird Island", type: "series", genre: "Documentary" },
                    { id: "marine-life", title: "Marine Life of Seychelles", type: "series", genre: "Documentary" },
                    { id: "aldabra-atoll", title: "Aldabra Atoll", type: "movie", genre: "Documentary" },
                    { id: "conservation-heroes", title: "Conservation Heroes", type: "series", genre: "Documentary" },
                  ].map((content) => (
                    <FeaturedCard
                      key={content.id}
                      id={content.id}
                      title={content.title}
                      type={content.type}
                      genre={content.genre}
                    />
                  ))}
                </div>
              </div>
            </TabsContent>
          </Tabs>
        </section>
      </main>

      {/* Footer */}
      <footer className="bg-gray-900 border-t border-gray-800 py-8">
        <div className="container px-4 md:px-12">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
            <div className="space-y-4">
              <Link href="/" className="flex items-center space-x-2">
                <span className="text-2xl font-bold bg-gradient-to-r from-blue-600 to-teal-500 bg-clip-text text-transparent">
                  Kominote
                </span>
              </Link>
              <p className="text-sm text-gray-400">Seychelles' premier streaming platform for local content</p>
            </div>
            <div className="space-y-2">
              <h3 className="text-sm font-medium text-gray-300">Browse</h3>
              <ul className="space-y-2">
                <li>
                  <Link href="/streaming/series" className="text-sm text-gray-400 hover:text-white">
                    Series
                  </Link>
                </li>
                <li>
                  <Link href="/streaming/movies" className="text-sm text-gray-400 hover:text-white">
                    Movies
                  </Link>
                </li>
                <li>
                  <Link href="/streaming/new" className="text-sm text-gray-400 hover:text-white">
                    New & Popular
                  </Link>
                </li>
                <li>
                  <Link href="/streaming/mylist" className="text-sm text-gray-400 hover:text-white">
                    My List
                  </Link>
                </li>
              </ul>
            </div>
            <div className="space-y-2">
              <h3 className="text-sm font-medium text-gray-300">Help</h3>
              <ul className="space-y-2">
                <li>
                  <Link href="#" className="text-sm text-gray-400 hover:text-white">
                    Account
                  </Link>
                </li>
                <li>
                  <Link href="#" className="text-sm text-gray-400 hover:text-white">
                    FAQ
                  </Link>
                </li>
                <li>
                  <Link href="#" className="text-sm text-gray-400 hover:text-white">
                    Contact Us
                  </Link>
                </li>
                <li>
                  <Link href="#" className="text-sm text-gray-400 hover:text-white">
                    Devices
                  </Link>
                </li>
              </ul>
            </div>
            <div className="space-y-2">
              <h3 className="text-sm font-medium text-gray-300">Legal</h3>
              <ul className="space-y-2">
                <li>
                  <Link href="#" className="text-sm text-gray-400 hover:text-white">
                    Terms of Use
                  </Link>
                </li>
                <li>
                  <Link href="#" className="text-sm text-gray-400 hover:text-white">
                    Privacy Policy
                  </Link>
                </li>
                <li>
                  <Link href="#" className="text-sm text-gray-400 hover:text-white">
                    Cookie Preferences
                  </Link>
                </li>
                <li>
                  <Link href="#" className="text-sm text-gray-400 hover:text-white">
                    Corporate Information
                  </Link>
                </li>
              </ul>
            </div>
          </div>
          <div className="mt-8 pt-8 border-t border-gray-800 text-center">
            <p className="text-xs text-gray-400">
              &copy; {new Date().getFullYear()} Kominote Streaming. All rights reserved.
            </p>
          </div>
        </div>
      </footer>

      {/* Side Navigation */}
      <FeatureNavigation currentFeature="streaming" colorScheme="blue" />
    </div>
  )
}

// Featured Large Card Component
function FeaturedLargeCard({ id, title, type, genre, description }) {
  return (
    <div className="group relative overflow-hidden rounded-lg cursor-pointer transition-transform duration-200 hover:scale-[1.02] hover:z-10 bg-gray-800/40">
      <div className="flex flex-col md:flex-row">
        <div className="relative w-full md:w-1/3 aspect-video md:aspect-square">
          <Image src="/placeholder.svg?height=300&width=300" fill alt={title} className="object-cover" />
          <Badge className="absolute top-2 left-2 bg-blue-600">FEATURED</Badge>
        </div>
        <div className="p-4 md:p-6 flex-1">
          <h3 className="text-xl font-bold mb-2">{title}</h3>
          <div className="flex items-center gap-2 mb-3">
            <Badge variant="outline" className="text-xs border-gray-600 text-gray-300 px-1">
              {type === "series" ? "SERIES" : "MOVIE"}
            </Badge>
            <span className="text-sm text-gray-400">{genre}</span>
          </div>
          <p className="text-gray-300 mb-4 line-clamp-3">{description}</p>
          <div className="flex gap-3">
            <Button
              className="bg-blue-600 hover:bg-blue-700"
              size="sm"
              onClick={() => (window.location.href = `/streaming/watch/${id}`)}
            >
              <Play className="mr-2 h-4 w-4" /> Play
            </Button>
            <Button
              variant="outline"
              className="border-gray-600 hover:bg-gray-700"
              size="sm"
              onClick={() => (window.location.href = `/streaming/details/${id}`)}
            >
              <Info className="mr-2 h-4 w-4" /> Details
            </Button>
          </div>
        </div>
      </div>
      <Link href={`/streaming/details/${id}`} className="absolute inset-0">
        <span className="sr-only">View details for {title}</span>
      </Link>
    </div>
  )
}

// Award Winner Card Component
function AwardWinnerCard({ id, title, type, genre, award }) {
  return (
    <div className="group relative overflow-hidden rounded-md cursor-pointer transition-transform duration-200 hover:scale-105 hover:z-10">
      <Image
        src="/placeholder.svg?height=270&width=180"
        width={180}
        height={270}
        alt={title}
        className="object-cover w-full h-full aspect-[2/3] transition-transform duration-300 group-hover:scale-110"
      />
      <Badge className="absolute top-2 left-2 bg-yellow-600">AWARD WINNER</Badge>
      <div className="absolute inset-0 bg-gradient-to-t from-black/90 via-black/50 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
      <div className="absolute bottom-0 left-0 right-0 p-3 transform translate-y-full group-hover:translate-y-0 transition-transform duration-300">
        <div className="flex items-center gap-2">
          <Button
            size="icon"
            className="h-8 w-8 rounded-full bg-blue-600 hover:bg-blue-700"
            onClick={(e) => {
              e.stopPropagation()
              window.location.href = `/streaming/watch/${id}`
            }}
          >
            <Play className="h-4 w-4" />
          </Button>
        </div>
        <h3 className="mt-2 text-sm font-medium line-clamp-1">{title}</h3>
        <div className="flex items-center gap-2 mt-1">
          <Badge variant="outline" className="text-[10px] border-gray-600 text-gray-400 px-1">
            {type === "series" ? "SERIES" : "MOVIE"}
          </Badge>
          <span className="text-xs text-gray-400">{genre}</span>
        </div>
        <div className="flex items-center mt-1">
          <span className="text-xs text-yellow-400">{award}</span>
        </div>
      </div>
      <Link href={`/streaming/details/${id}`} className="absolute inset-0">
        <span className="sr-only">View details for {title}</span>
      </Link>
    </div>
  )
}

// Staff Pick Card Component
function StaffPickCard({ id, title, type, genre, staff }) {
  return (
    <div className="group relative overflow-hidden rounded-md cursor-pointer transition-transform duration-200 hover:scale-105 hover:z-10">
      <Image
        src="/placeholder.svg?height=270&width=180"
        width={180}
        height={270}
        alt={title}
        className="object-cover w-full h-full aspect-[2/3] transition-transform duration-300 group-hover:scale-110"
      />
      <Badge className="absolute top-2 left-2 bg-purple-600">STAFF PICK</Badge>
      <div className="absolute inset-0 bg-gradient-to-t from-black/90 via-black/50 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
      <div className="absolute bottom-0 left-0 right-0 p-3 transform translate-y-full group-hover:translate-y-0 transition-transform duration-300">
        <div className="flex items-center gap-2">
          <Button
            size="icon"
            className="h-8 w-8 rounded-full bg-blue-600 hover:bg-blue-700"
            onClick={(e) => {
              e.stopPropagation()
              window.location.href = `/streaming/watch/${id}`
            }}
          >
            <Play className="h-4 w-4" />
          </Button>
        </div>
        <h3 className="mt-2 text-sm font-medium line-clamp-1">{title}</h3>
        <div className="flex items-center gap-2 mt-1">
          <Badge variant="outline" className="text-[10px] border-gray-600 text-gray-400 px-1">
            {type === "series" ? "SERIES" : "MOVIE"}
          </Badge>
          <span className="text-xs text-gray-400">{genre}</span>
        </div>
        <div className="flex items-center mt-1">
          <span className="text-xs text-purple-400">Picked by: {staff}</span>
        </div>
      </div>
      <Link href={`/streaming/details/${id}`} className="absolute inset-0">
        <span className="sr-only">View details for {title}</span>
      </Link>
    </div>
  )
}

// Featured Card Component
function FeaturedCard({ id, title, type, genre }) {
  return (
    <div className="group relative overflow-hidden rounded-md cursor-pointer transition-transform duration-200 hover:scale-105 hover:z-10">
      <Image
        src="/placeholder.svg?height=270&width=180"
        width={180}
        height={270}
        alt={title}
        className="object-cover w-full h-full aspect-[2/3] transition-transform duration-300 group-hover:scale-110"
      />
      <Badge className="absolute top-2 left-2 bg-blue-600">FEATURED</Badge>
      <div className="absolute inset-0 bg-gradient-to-t from-black/90 via-black/50 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
      <div className="absolute bottom-0 left-0 right-0 p-3 transform translate-y-full group-hover:translate-y-0 transition-transform duration-300">
        <div className="flex items-center gap-2">
          <Button
            size="icon"
            className="h-8 w-8 rounded-full bg-blue-600 hover:bg-blue-700"
            onClick={(e) => {
              e.stopPropagation()
              window.location.href = `/streaming/watch/${id}`
            }}
          >
            <Play className="h-4 w-4" />
          </Button>
        </div>
        <h3 className="mt-2 text-sm font-medium line-clamp-1">{title}</h3>
        <div className="flex items-center gap-2 mt-1">
          <Badge variant="outline" className="text-[10px] border-gray-600 text-gray-400 px-1">
            {type === "series" ? "SERIES" : "MOVIE"}
          </Badge>
          <span className="text-xs text-gray-400">{genre}</span>
        </div>
      </div>
      <Link href={`/streaming/details/${id}`} className="absolute inset-0">
        <span className="sr-only">View details for {title}</span>
      </Link>
    </div>
  )
}

