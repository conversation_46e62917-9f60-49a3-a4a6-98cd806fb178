# Generated by Django 4.2.21 on 2025-05-30 12:39

from decimal import Decimal
from django.conf import settings
import django.core.validators
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Event',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.Char<PERSON>ield(help_text='Event title', max_length=200)),
                ('title_fr', models.CharField(blank=True, help_text='Event title in French', max_length=200)),
                ('title_cr', models.CharField(blank=True, help_text='Event title in Creole', max_length=200)),
                ('slug', models.SlugField(help_text='URL-friendly version of the title', max_length=200, unique=True)),
                ('description', models.TextField(help_text='Detailed event description')),
                ('short_description', models.TextField(blank=True, help_text='Short description for listings', max_length=500)),
                ('tags', models.CharField(blank=True, help_text='Comma-separated tags for the event', max_length=500)),
                ('start_date', models.DateField(help_text='Event start date')),
                ('start_time', models.TimeField(help_text='Event start time')),
                ('end_date', models.DateField(help_text='Event end date')),
                ('end_time', models.TimeField(help_text='Event end time')),
                ('timezone', models.CharField(default='Indian/Mahe', help_text='Event timezone', max_length=50)),
                ('event_type', models.CharField(choices=[('public', 'Public Event'), ('private', 'Private Event'), ('invite_only', 'Invite Only')], default='public', help_text='Type of event', max_length=20)),
                ('max_attendees', models.PositiveIntegerField(blank=True, help_text='Maximum number of attendees (leave blank for unlimited)', null=True)),
                ('registration_required', models.BooleanField(default=True, help_text='Whether registration is required')),
                ('registration_deadline', models.DateTimeField(blank=True, help_text='Registration deadline', null=True)),
                ('allow_waitlist', models.BooleanField(default=False, help_text='Allow waitlist when event is full')),
                ('pricing_type', models.CharField(choices=[('free', 'Free'), ('paid', 'Paid'), ('donation', 'Donation Based')], default='free', help_text='Pricing type for the event', max_length=20)),
                ('price', models.DecimalField(decimal_places=2, default=Decimal('0.00'), help_text='Event price (if paid)', max_digits=10)),
                ('currency', models.CharField(default='SCR', help_text='Currency code (SCR for Seychelles Rupee)', max_length=3)),
                ('featured_image', models.ImageField(blank=True, help_text='Main event image', null=True, upload_to='events/')),
                ('status', models.CharField(choices=[('draft', 'Draft'), ('published', 'Published'), ('cancelled', 'Cancelled'), ('postponed', 'Postponed'), ('completed', 'Completed')], default='draft', help_text='Event status', max_length=20)),
                ('is_featured', models.BooleanField(default=False, help_text='Whether this event is featured')),
                ('is_recurring', models.BooleanField(default=False, help_text='Whether this is a recurring event')),
                ('is_local_event', models.BooleanField(default=True, help_text='Whether this is a local Seychelles event')),
                ('supports_local_culture', models.BooleanField(default=False, help_text='Whether this event supports local Seychellois culture')),
                ('age_restriction', models.CharField(blank=True, help_text="Age restrictions (e.g., '18+', 'Family Friendly')", max_length=50)),
                ('dress_code', models.CharField(blank=True, help_text='Dress code requirements', max_length=200)),
                ('special_requirements', models.TextField(blank=True, help_text='Special requirements or instructions')),
                ('contact_email', models.EmailField(blank=True, help_text='Contact email for event inquiries', max_length=254)),
                ('contact_phone', models.CharField(blank=True, help_text='Contact phone number', max_length=20)),
                ('total_registrations', models.PositiveIntegerField(default=0, help_text='Total number of registrations')),
                ('total_attendees', models.PositiveIntegerField(default=0, help_text='Total number of actual attendees')),
                ('total_views', models.PositiveIntegerField(default=0, help_text='Total number of page views')),
                ('average_rating', models.DecimalField(decimal_places=2, default=Decimal('0.00'), help_text='Average rating from reviews', max_digits=3)),
                ('total_reviews', models.PositiveIntegerField(default=0, help_text='Total number of reviews')),
                ('meta_title', models.CharField(blank=True, help_text='SEO meta title', max_length=200)),
                ('meta_description', models.TextField(blank=True, help_text='SEO meta description', max_length=500)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('published_at', models.DateTimeField(blank=True, help_text='When the event was published', null=True)),
            ],
            options={
                'verbose_name': 'Event',
                'verbose_name_plural': 'Events',
                'db_table': 'events_event',
                'ordering': ['-start_date', '-start_time'],
            },
        ),
        migrations.CreateModel(
            name='EventCategory',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(help_text='Category name (e.g., Music, Food & Drink, Business)', max_length=100, unique=True)),
                ('name_fr', models.CharField(blank=True, help_text='Category name in French', max_length=100)),
                ('name_cr', models.CharField(blank=True, help_text='Category name in Creole', max_length=100)),
                ('slug', models.SlugField(help_text='URL-friendly version of the name', max_length=100, unique=True)),
                ('description', models.TextField(blank=True, help_text='Description of this event category')),
                ('icon', models.CharField(blank=True, help_text='Icon class name for display', max_length=50)),
                ('color', models.CharField(default='#f59e0b', help_text='Hex color code for category display', max_length=7)),
                ('is_active', models.BooleanField(default=True, help_text='Whether this category is active')),
                ('sort_order', models.PositiveIntegerField(default=0, help_text='Order for displaying categories')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'Event Category',
                'verbose_name_plural': 'Event Categories',
                'db_table': 'events_category',
                'ordering': ['sort_order', 'name'],
            },
        ),
        migrations.CreateModel(
            name='EventRegistration',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('status', models.CharField(choices=[('pending', 'Pending'), ('confirmed', 'Confirmed'), ('cancelled', 'Cancelled'), ('waitlist', 'Waitlist'), ('attended', 'Attended'), ('no_show', 'No Show')], default='pending', help_text='Registration status', max_length=20)),
                ('registration_date', models.DateTimeField(auto_now_add=True, help_text='When the registration was made')),
                ('number_of_attendees', models.PositiveIntegerField(default=1, help_text='Number of people attending', validators=[django.core.validators.MinValueValidator(1)])),
                ('attendee_names', models.TextField(blank=True, help_text='Names of additional attendees (if more than 1)')),
                ('contact_phone', models.CharField(blank=True, help_text='Contact phone for this registration', max_length=20)),
                ('special_requirements', models.TextField(blank=True, help_text='Special requirements or dietary restrictions')),
                ('amount_paid', models.DecimalField(decimal_places=2, default=Decimal('0.00'), help_text='Amount paid for registration', max_digits=10)),
                ('payment_status', models.CharField(choices=[('pending', 'Pending'), ('paid', 'Paid'), ('refunded', 'Refunded'), ('failed', 'Failed')], default='pending', help_text='Payment status', max_length=20)),
                ('payment_reference', models.CharField(blank=True, help_text='Payment reference or transaction ID', max_length=100)),
                ('checked_in', models.BooleanField(default=False, help_text='Whether the user has checked in')),
                ('check_in_time', models.DateTimeField(blank=True, help_text='When the user checked in', null=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('event', models.ForeignKey(help_text='Event being registered for', on_delete=django.db.models.deletion.CASCADE, related_name='registrations', to='events.event')),
                ('user', models.ForeignKey(help_text='User registering for the event', on_delete=django.db.models.deletion.CASCADE, related_name='event_registrations', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Event Registration',
                'verbose_name_plural': 'Event Registrations',
                'db_table': 'events_registration',
                'ordering': ['-registration_date'],
            },
        ),
        migrations.CreateModel(
            name='Venue',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(help_text='Venue name', max_length=200)),
                ('slug', models.SlugField(help_text='URL-friendly version of the name', max_length=200, unique=True)),
                ('description', models.TextField(blank=True, help_text='Venue description and facilities')),
                ('venue_type', models.CharField(choices=[('indoor', 'Indoor'), ('outdoor', 'Outdoor'), ('beach', 'Beach'), ('conference', 'Conference Center'), ('hotel', 'Hotel'), ('restaurant', 'Restaurant'), ('community', 'Community Center'), ('sports', 'Sports Facility'), ('cultural', 'Cultural Center'), ('other', 'Other')], default='other', help_text='Type of venue', max_length=20)),
                ('address', models.TextField(help_text='Full address of the venue')),
                ('island', models.CharField(choices=[('mahe', 'Mahé'), ('praslin', 'Praslin'), ('la_digue', 'La Digue'), ('silhouette', 'Silhouette'), ('fregate', 'Fregate'), ('bird', 'Bird Island'), ('denis', 'Denis Island'), ('other', 'Other')], help_text='Which island in Seychelles', max_length=20)),
                ('district', models.CharField(blank=True, help_text='District or area', max_length=100)),
                ('latitude', models.DecimalField(blank=True, decimal_places=8, help_text='Latitude coordinate', max_digits=10, null=True)),
                ('longitude', models.DecimalField(blank=True, decimal_places=8, help_text='Longitude coordinate', max_digits=11, null=True)),
                ('capacity', models.PositiveIntegerField(blank=True, help_text='Maximum capacity of the venue', null=True)),
                ('has_parking', models.BooleanField(default=False, help_text='Whether venue has parking available')),
                ('has_accessibility', models.BooleanField(default=False, help_text='Whether venue is wheelchair accessible')),
                ('has_wifi', models.BooleanField(default=False, help_text='Whether venue has WiFi available')),
                ('has_catering', models.BooleanField(default=False, help_text='Whether venue offers catering services')),
                ('contact_name', models.CharField(blank=True, help_text='Contact person name', max_length=200)),
                ('contact_phone', models.CharField(blank=True, help_text='Contact phone number', max_length=20)),
                ('contact_email', models.EmailField(blank=True, help_text='Contact email address', max_length=254)),
                ('website', models.URLField(blank=True, help_text='Venue website URL')),
                ('image', models.ImageField(blank=True, help_text='Main venue image', null=True, upload_to='venues/')),
                ('is_active', models.BooleanField(default=True, help_text='Whether this venue is active')),
                ('is_verified', models.BooleanField(default=False, help_text='Whether this venue is verified')),
                ('total_events', models.PositiveIntegerField(default=0, help_text='Total number of events hosted')),
                ('average_rating', models.DecimalField(decimal_places=2, default=Decimal('0.00'), help_text='Average rating from event reviews', max_digits=3)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'Venue',
                'verbose_name_plural': 'Venues',
                'db_table': 'events_venue',
                'ordering': ['name'],
                'indexes': [models.Index(fields=['island', 'district'], name='events_venu_island_3721fc_idx'), models.Index(fields=['venue_type'], name='events_venu_venue_t_8565ea_idx'), models.Index(fields=['is_active', 'is_verified'], name='events_venu_is_acti_70ac37_idx')],
            },
        ),
        migrations.CreateModel(
            name='EventTicket',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(help_text='Ticket type name', max_length=100)),
                ('description', models.TextField(blank=True, help_text="Description of what's included")),
                ('ticket_type', models.CharField(choices=[('general', 'General Admission'), ('vip', 'VIP'), ('early_bird', 'Early Bird'), ('student', 'Student'), ('senior', 'Senior'), ('group', 'Group'), ('sponsor', 'Sponsor')], default='general', help_text='Type of ticket', max_length=20)),
                ('price', models.DecimalField(decimal_places=2, help_text='Ticket price', max_digits=10)),
                ('currency', models.CharField(default='SCR', help_text='Currency code', max_length=3)),
                ('quantity_available', models.PositiveIntegerField(blank=True, help_text='Number of tickets available (leave blank for unlimited)', null=True)),
                ('quantity_sold', models.PositiveIntegerField(default=0, help_text='Number of tickets sold')),
                ('sale_start_date', models.DateTimeField(blank=True, help_text='When ticket sales start', null=True)),
                ('sale_end_date', models.DateTimeField(blank=True, help_text='When ticket sales end', null=True)),
                ('is_active', models.BooleanField(default=True, help_text='Whether this ticket type is active')),
                ('min_quantity', models.PositiveIntegerField(default=1, help_text='Minimum quantity per purchase')),
                ('max_quantity', models.PositiveIntegerField(default=10, help_text='Maximum quantity per purchase')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('event', models.ForeignKey(help_text='Event this ticket is for', on_delete=django.db.models.deletion.CASCADE, related_name='ticket_types', to='events.event')),
            ],
            options={
                'verbose_name': 'Event Ticket',
                'verbose_name_plural': 'Event Tickets',
                'db_table': 'events_ticket',
                'ordering': ['price'],
            },
        ),
        migrations.CreateModel(
            name='EventOrganizer',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('organization_name', models.CharField(blank=True, help_text='Organization or business name', max_length=200)),
                ('organizer_type', models.CharField(choices=[('individual', 'Individual'), ('business', 'Business'), ('nonprofit', 'Non-Profit Organization'), ('government', 'Government Agency'), ('community', 'Community Group'), ('educational', 'Educational Institution')], default='individual', help_text='Type of organizer', max_length=20)),
                ('description', models.TextField(blank=True, help_text='Description of the organizer')),
                ('contact_phone', models.CharField(blank=True, help_text='Contact phone number', max_length=20)),
                ('contact_email', models.EmailField(blank=True, help_text='Contact email address', max_length=254)),
                ('website', models.URLField(blank=True, help_text='Organizer website URL')),
                ('facebook_url', models.URLField(blank=True)),
                ('instagram_url', models.URLField(blank=True)),
                ('twitter_url', models.URLField(blank=True)),
                ('is_verified', models.BooleanField(default=False, help_text='Whether this organizer is verified')),
                ('verification_documents', models.FileField(blank=True, help_text='Verification documents', null=True, upload_to='organizers/documents/')),
                ('total_events', models.PositiveIntegerField(default=0, help_text='Total number of events organized')),
                ('total_attendees', models.PositiveIntegerField(default=0, help_text='Total number of attendees across all events')),
                ('average_rating', models.DecimalField(decimal_places=2, default=Decimal('0.00'), help_text='Average rating from event reviews', max_digits=3)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='event_organizer_profile', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Event Organizer',
                'verbose_name_plural': 'Event Organizers',
                'db_table': 'events_organizer',
                'ordering': ['-total_events', 'organization_name'],
            },
        ),
        migrations.CreateModel(
            name='EventImage',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('image', models.ImageField(help_text='Event image', upload_to='events/gallery/')),
                ('caption', models.CharField(blank=True, help_text='Image caption', max_length=200)),
                ('alt_text', models.CharField(blank=True, help_text='Alt text for accessibility', max_length=200)),
                ('sort_order', models.PositiveIntegerField(default=0, help_text='Order for displaying images')),
                ('is_featured', models.BooleanField(default=False, help_text='Whether this is a featured image')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('event', models.ForeignKey(help_text='Event this image belongs to', on_delete=django.db.models.deletion.CASCADE, related_name='images', to='events.event')),
            ],
            options={
                'verbose_name': 'Event Image',
                'verbose_name_plural': 'Event Images',
                'db_table': 'events_image',
                'ordering': ['sort_order', 'created_at'],
            },
        ),
        migrations.AddField(
            model_name='event',
            name='category',
            field=models.ForeignKey(help_text='Event category', on_delete=django.db.models.deletion.PROTECT, related_name='events', to='events.eventcategory'),
        ),
        migrations.AddField(
            model_name='event',
            name='organizer',
            field=models.ForeignKey(help_text='Event organizer', on_delete=django.db.models.deletion.CASCADE, related_name='events', to='events.eventorganizer'),
        ),
        migrations.AddField(
            model_name='event',
            name='venue',
            field=models.ForeignKey(help_text='Event venue', on_delete=django.db.models.deletion.PROTECT, related_name='events', to='events.venue'),
        ),
        migrations.CreateModel(
            name='EventReview',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('rating', models.PositiveIntegerField(help_text='Rating from 1 to 5 stars', validators=[django.core.validators.MinValueValidator(1), django.core.validators.MaxValueValidator(5)])),
                ('title', models.CharField(help_text='Review title', max_length=200)),
                ('content', models.TextField(help_text='Review content')),
                ('organization_rating', models.PositiveIntegerField(blank=True, help_text='Rating for event organization', null=True, validators=[django.core.validators.MinValueValidator(1), django.core.validators.MaxValueValidator(5)])),
                ('venue_rating', models.PositiveIntegerField(blank=True, help_text='Rating for venue', null=True, validators=[django.core.validators.MinValueValidator(1), django.core.validators.MaxValueValidator(5)])),
                ('value_rating', models.PositiveIntegerField(blank=True, help_text='Rating for value for money', null=True, validators=[django.core.validators.MinValueValidator(1), django.core.validators.MaxValueValidator(5)])),
                ('is_verified', models.BooleanField(default=False, help_text='Whether this review is from a verified attendee')),
                ('is_approved', models.BooleanField(default=True, help_text='Whether this review is approved')),
                ('moderation_notes', models.TextField(blank=True, help_text='Notes from moderation')),
                ('helpful_votes', models.PositiveIntegerField(default=0, help_text='Number of helpful votes')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('event', models.ForeignKey(help_text='Event being reviewed', on_delete=django.db.models.deletion.CASCADE, related_name='reviews', to='events.event')),
                ('moderated_by', models.ForeignKey(blank=True, help_text='Admin who moderated this review', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='moderated_event_reviews', to=settings.AUTH_USER_MODEL)),
                ('registration', models.ForeignKey(blank=True, help_text='Registration this review is for', null=True, on_delete=django.db.models.deletion.CASCADE, to='events.eventregistration')),
                ('user', models.ForeignKey(help_text='User writing the review', on_delete=django.db.models.deletion.CASCADE, related_name='event_reviews', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Event Review',
                'verbose_name_plural': 'Event Reviews',
                'db_table': 'events_review',
                'ordering': ['-created_at'],
                'indexes': [models.Index(fields=['event', 'is_approved'], name='events_revi_event_i_e45cb7_idx'), models.Index(fields=['user', '-created_at'], name='events_revi_user_id_23014c_idx'), models.Index(fields=['rating', '-created_at'], name='events_revi_rating_e3ae38_idx')],
                'unique_together': {('event', 'user')},
            },
        ),
        migrations.AddIndex(
            model_name='eventregistration',
            index=models.Index(fields=['event', 'status'], name='events_regi_event_i_c98244_idx'),
        ),
        migrations.AddIndex(
            model_name='eventregistration',
            index=models.Index(fields=['user', 'status'], name='events_regi_user_id_0e863b_idx'),
        ),
        migrations.AddIndex(
            model_name='eventregistration',
            index=models.Index(fields=['registration_date'], name='events_regi_registr_07801a_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='eventregistration',
            unique_together={('event', 'user')},
        ),
        migrations.AddIndex(
            model_name='event',
            index=models.Index(fields=['status', 'start_date'], name='events_even_status_dfba18_idx'),
        ),
        migrations.AddIndex(
            model_name='event',
            index=models.Index(fields=['category', 'start_date'], name='events_even_categor_ed450e_idx'),
        ),
        migrations.AddIndex(
            model_name='event',
            index=models.Index(fields=['venue', 'start_date'], name='events_even_venue_i_0543fa_idx'),
        ),
        migrations.AddIndex(
            model_name='event',
            index=models.Index(fields=['organizer', 'start_date'], name='events_even_organiz_2f8b81_idx'),
        ),
        migrations.AddIndex(
            model_name='event',
            index=models.Index(fields=['is_featured', 'start_date'], name='events_even_is_feat_cec4db_idx'),
        ),
        migrations.AddIndex(
            model_name='event',
            index=models.Index(fields=['pricing_type', 'start_date'], name='events_even_pricing_5e26bf_idx'),
        ),
    ]
