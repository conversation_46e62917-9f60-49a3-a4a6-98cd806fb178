"use client"

import Link from "next/link"
import { usePathname } from "next/navigation"
import { cn } from "@/lib/utils"
import { Button } from "@/components/ui/button"
import { useAuth } from "@/components/auth-provider"

export function MainNav() {
  const pathname = usePathname()
  const { isAuthenticated } = useAuth()

  const routes = [
    {
      href: "/streaming",
      label: "Streaming",
      active: pathname?.startsWith("/streaming"),
    },
    {
      href: "/shopping",
      label: "Shopping",
      active: pathname?.startsWith("/shopping"),
    },
    {
      href: "/community",
      label: "Community",
      active: pathname?.startsWith("/community"),
    },
    {
      href: "/events",
      label: "Events",
      active: pathname?.startsWith("/events"),
    },
    {
      href: "/jobs",
      label: "Jobs",
      active: pathname?.startsWith("/jobs"),
    },
  ]

  return (
    <div className="flex items-center justify-between w-full">
      <nav className="hidden md:flex gap-6">
        {routes.map((route) => (
          <Link
            key={route.href}
            href={route.href}
            className={cn(
              "text-sm font-medium transition-colors hover:text-primary",
              route.active ? "text-primary" : "text-muted-foreground",
            )}
          >
            {route.label}
          </Link>
        ))}
      </nav>
      <div className="flex items-center gap-4">
        {!isAuthenticated ? (
          <>
            <Button variant="outline" size="sm" className="hidden md:flex" asChild>
              <Link href="/auth/login">Log In</Link>
            </Button>
            <Button size="sm" asChild>
              <Link href="/auth/signup">Sign Up</Link>
            </Button>
          </>
        ) : null}
      </div>
    </div>
  )
}

