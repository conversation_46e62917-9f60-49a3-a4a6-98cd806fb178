# Generated by Django 4.2.21 on 2025-05-30 10:09

from django.conf import settings
import django.contrib.auth.models
import django.contrib.auth.validators
import django.core.validators
from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('auth', '0012_alter_user_first_name_max_length'),
    ]

    operations = [
        migrations.CreateModel(
            name='User',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('password', models.CharField(max_length=128, verbose_name='password')),
                ('last_login', models.DateTimeField(blank=True, null=True, verbose_name='last login')),
                ('is_superuser', models.BooleanField(default=False, help_text='Designates that this user has all permissions without explicitly assigning them.', verbose_name='superuser status')),
                ('username', models.CharField(error_messages={'unique': 'A user with that username already exists.'}, help_text='Required. 150 characters or fewer. Letters, digits and @/./+/-/_ only.', max_length=150, unique=True, validators=[django.contrib.auth.validators.UnicodeUsernameValidator()], verbose_name='username')),
                ('first_name', models.CharField(blank=True, max_length=150, verbose_name='first name')),
                ('last_name', models.CharField(blank=True, max_length=150, verbose_name='last name')),
                ('email', models.EmailField(blank=True, max_length=254, verbose_name='email address')),
                ('is_staff', models.BooleanField(default=False, help_text='Designates whether the user can log into this admin site.', verbose_name='staff status')),
                ('is_active', models.BooleanField(default=True, help_text='Designates whether this user should be treated as active. Unselect this instead of deleting accounts.', verbose_name='active')),
                ('date_joined', models.DateTimeField(default=django.utils.timezone.now, verbose_name='date joined')),
                ('phone_number', models.CharField(blank=True, help_text='Seychelles phone number format: +248XXXXXXX', max_length=12, null=True, validators=[django.core.validators.RegexValidator(message='Phone number must be in format: +248XXXXXXX (Seychelles format)', regex='^\\+248\\d{7}$')])),
                ('date_of_birth', models.DateField(blank=True, null=True)),
                ('profile_picture', models.ImageField(blank=True, help_text='Profile picture for user avatar', null=True, upload_to='profiles/')),
                ('bio', models.TextField(blank=True, help_text='Short bio or description', max_length=500)),
                ('island', models.CharField(blank=True, choices=[('mahe', 'Mahé'), ('praslin', 'Praslin'), ('la_digue', 'La Digue'), ('silhouette', 'Silhouette'), ('fregate', 'Fregate'), ('bird', 'Bird Island'), ('denis', 'Denis Island'), ('other', 'Other')], help_text='Which island in Seychelles', max_length=20)),
                ('district', models.CharField(blank=True, help_text='District or area', max_length=100)),
                ('language_preference', models.CharField(choices=[('en', 'English'), ('fr', 'French'), ('cr', 'Creole')], default='en', help_text='Preferred language for the platform', max_length=10)),
                ('is_verified', models.BooleanField(default=False, help_text="Whether the user's email/phone is verified")),
                ('verification_token', models.CharField(blank=True, help_text='Token for email/phone verification', max_length=100, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('last_activity', models.DateTimeField(default=django.utils.timezone.now)),
                ('groups', models.ManyToManyField(blank=True, help_text='The groups this user belongs to. A user will get all permissions granted to each of their groups.', related_name='user_set', related_query_name='user', to='auth.group', verbose_name='groups')),
                ('user_permissions', models.ManyToManyField(blank=True, help_text='Specific permissions for this user.', related_name='user_set', related_query_name='user', to='auth.permission', verbose_name='user permissions')),
            ],
            options={
                'verbose_name': 'User',
                'verbose_name_plural': 'Users',
                'db_table': 'accounts_user',
                'ordering': ['-date_joined'],
            },
            managers=[
                ('objects', django.contrib.auth.models.UserManager()),
            ],
        ),
        migrations.CreateModel(
            name='UserProfile',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('facebook_url', models.URLField(blank=True, help_text='Facebook profile URL')),
                ('instagram_url', models.URLField(blank=True, help_text='Instagram profile URL')),
                ('twitter_url', models.URLField(blank=True, help_text='Twitter profile URL')),
                ('linkedin_url', models.URLField(blank=True, help_text='LinkedIn profile URL')),
                ('email_notifications', models.BooleanField(default=True, help_text='Receive email notifications')),
                ('sms_notifications', models.BooleanField(default=False, help_text='Receive SMS notifications')),
                ('marketing_emails', models.BooleanField(default=False, help_text='Receive marketing emails')),
                ('profile_visibility', models.CharField(choices=[('public', 'Public'), ('friends', 'Friends Only'), ('private', 'Private')], default='public', help_text='Who can see your profile', max_length=20)),
                ('show_online_status', models.BooleanField(default=True, help_text="Show when you're online")),
                ('interests', models.TextField(blank=True, help_text='Comma-separated list of interests')),
                ('total_logins', models.PositiveIntegerField(default=0)),
                ('total_posts', models.PositiveIntegerField(default=0)),
                ('total_purchases', models.PositiveIntegerField(default=0)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='profile', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'User Profile',
                'verbose_name_plural': 'User Profiles',
                'db_table': 'accounts_userprofile',
            },
        ),
        migrations.CreateModel(
            name='UserRole',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('role', models.CharField(choices=[('customer', 'Customer'), ('vendor', 'Vendor'), ('content_creator', 'Content Creator'), ('event_organizer', 'Event Organizer'), ('employer', 'Employer'), ('job_seeker', 'Job Seeker'), ('moderator', 'Moderator'), ('admin', 'Administrator')], max_length=20)),
                ('is_active', models.BooleanField(default=True)),
                ('granted_at', models.DateTimeField(auto_now_add=True)),
                ('granted_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='granted_roles', to=settings.AUTH_USER_MODEL)),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='roles', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'User Role',
                'verbose_name_plural': 'User Roles',
                'db_table': 'accounts_userrole',
                'unique_together': {('user', 'role')},
            },
        ),
        migrations.CreateModel(
            name='UserActivity',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('activity_type', models.CharField(choices=[('login', 'Login'), ('logout', 'Logout'), ('profile_update', 'Profile Update'), ('password_change', 'Password Change'), ('purchase', 'Purchase'), ('post_create', 'Post Created'), ('comment', 'Comment'), ('like', 'Like'), ('share', 'Share'), ('video_watch', 'Video Watch'), ('event_book', 'Event Booking'), ('job_apply', 'Job Application')], max_length=20)),
                ('description', models.TextField(blank=True)),
                ('ip_address', models.GenericIPAddressField(blank=True, null=True)),
                ('user_agent', models.TextField(blank=True)),
                ('timestamp', models.DateTimeField(auto_now_add=True)),
                ('content_type', models.CharField(blank=True, max_length=50)),
                ('object_id', models.PositiveIntegerField(blank=True, null=True)),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='activities', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'User Activity',
                'verbose_name_plural': 'User Activities',
                'db_table': 'accounts_useractivity',
                'ordering': ['-timestamp'],
                'indexes': [models.Index(fields=['user', '-timestamp'], name='accounts_us_user_id_5da0f6_idx'), models.Index(fields=['activity_type', '-timestamp'], name='accounts_us_activit_f5b46e_idx')],
            },
        ),
    ]
