import Link from "next/link"
import Image from "next/image"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Search, ShoppingCart, Heart, User, ChevronDown, Filter, Star, ArrowLeft, ArrowRight } from "lucide-react"
import FeatureNavigation from "@/components/feature-navigation"

export default function ClothingCategoryPage() {
  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="sticky top-0 z-50 w-full bg-white border-b shadow-sm">
        <div className="container flex h-16 items-center justify-between px-4">
          <div className="flex items-center gap-8">
            <Link href="/" className="flex items-center space-x-2">
              <span className="text-2xl font-bold bg-gradient-to-r from-blue-600 to-teal-500 bg-clip-text text-transparent">
                Kominote
              </span>
            </Link>
            <div className="hidden md:flex relative w-full max-w-md">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <Input
                type="search"
                placeholder="Search products, vendors, categories..."
                className="pl-10 pr-4 py-2 w-full border-emerald-200 focus:border-emerald-500"
              />
            </div>
          </div>
          <div className="flex items-center gap-4">
            <Button variant="ghost" size="icon" className="text-gray-600 hover:text-emerald-600">
              <Heart className="h-5 w-5" />
              <span className="sr-only">Wishlist</span>
            </Button>
            <Button variant="ghost" size="icon" className="text-gray-600 hover:text-emerald-600 relative">
              <ShoppingCart className="h-5 w-5" />
              <span className="sr-only">Cart</span>
              <Badge className="absolute -top-1 -right-1 h-5 w-5 flex items-center justify-center p-0 bg-emerald-600">
                3
              </Badge>
            </Button>
            <div className="flex items-center gap-2">
              <Button variant="ghost" className="text-gray-600 hover:text-emerald-600">
                <User className="h-5 w-5 mr-2" />
                <span className="hidden md:inline">Account</span>
                <ChevronDown className="h-4 w-4 ml-1" />
              </Button>
            </div>
          </div>
        </div>
        <div className="container px-4 py-2 border-t border-gray-100">
          <nav className="flex items-center gap-6 overflow-x-auto pb-2 md:pb-0">
            <Link
              href="/shopping/category/clothing"
              className="text-sm font-medium whitespace-nowrap text-emerald-600 border-b-2 border-emerald-600 pb-1"
            >
              Clothing & Fashion
            </Link>
            <Link
              href="/shopping/category/crafts"
              className="text-sm font-medium whitespace-nowrap text-gray-600 hover:text-emerald-600"
            >
              Local Crafts
            </Link>
            <Link
              href="/shopping/category/food"
              className="text-sm font-medium whitespace-nowrap text-gray-600 hover:text-emerald-600"
            >
              Food & Groceries
            </Link>
            <Link
              href="/shopping/category/beauty"
              className="text-sm font-medium whitespace-nowrap text-gray-600 hover:text-emerald-600"
            >
              Beauty & Wellness
            </Link>
            <Link
              href="/shopping/category/home"
              className="text-sm font-medium whitespace-nowrap text-gray-600 hover:text-emerald-600"
            >
              Home & Garden
            </Link>
            <Link
              href="/shopping/category/electronics"
              className="text-sm font-medium whitespace-nowrap text-gray-600 hover:text-emerald-600"
            >
              Electronics
            </Link>
            <Link
              href="/shopping/category/services"
              className="text-sm font-medium whitespace-nowrap text-gray-600 hover:text-emerald-600"
            >
              Services
            </Link>
          </nav>
        </div>
      </header>

      <main>
        {/* Breadcrumb */}
        <div className="bg-white border-b">
          <div className="container px-4 py-3">
            <div className="flex items-center text-sm text-gray-500">
              <Link href="/shopping" className="hover:text-emerald-600">
                Home
              </Link>
              <span className="mx-2">/</span>
              <span className="text-emerald-600 font-medium">Clothing & Fashion</span>
            </div>
          </div>
        </div>

        {/* Category Header */}
        <section className="bg-white py-8 border-b">
          <div className="container px-4 md:px-6">
            <div className="flex flex-col md:flex-row md:items-center justify-between gap-4">
              <div>
                <h1 className="text-3xl font-bold mb-2">Clothing & Fashion</h1>
                <p className="text-gray-600 max-w-2xl">
                  Discover authentic Seychellois fashion, from traditional island wear to modern designs inspired by our
                  vibrant culture. Support local designers and artisans.
                </p>
              </div>
              <div className="flex items-center gap-3">
                <Button
                  variant="outline"
                  size="sm"
                  className="gap-2 border-emerald-200 text-emerald-600 hover:bg-emerald-50"
                >
                  <Filter className="h-4 w-4" /> Filter
                </Button>
                <select className="text-sm border rounded-md px-2 py-1 bg-white border-gray-200">
                  <option>Sort: Featured</option>
                  <option>Price: Low to High</option>
                  <option>Price: High to Low</option>
                  <option>Newest</option>
                  <option>Rating</option>
                </select>
              </div>
            </div>
          </div>
        </section>

        {/* Subcategories */}
        <section className="py-8 bg-gray-50">
          <div className="container px-4 md:px-6">
            <h2 className="text-xl font-bold mb-6">Shop by Category</h2>
            <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-4">
              <SubcategoryCard title="Women's Wear" count={42} image="/placeholder.svg?height=150&width=150" />
              <SubcategoryCard title="Men's Wear" count={38} image="/placeholder.svg?height=150&width=150" />
              <SubcategoryCard title="Traditional Attire" count={15} image="/placeholder.svg?height=150&width=150" />
              <SubcategoryCard title="Beachwear" count={24} image="/placeholder.svg?height=150&width=150" />
              <SubcategoryCard title="Accessories" count={31} image="/placeholder.svg?height=150&width=150" />
              <SubcategoryCard title="Footwear" count={19} image="/placeholder.svg?height=150&width=150" />
            </div>
          </div>
        </section>

        {/* Featured Products */}
        <section className="py-8 bg-white">
          <div className="container px-4 md:px-6">
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-xl font-bold">Featured Products</h2>
              <Button variant="link" className="text-emerald-600 hover:text-emerald-700 p-0">
                View All <ArrowRight className="ml-1 h-4 w-4" />
              </Button>
            </div>
            <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-4 md:gap-6">
              {[
                {
                  name: "Seychelles Print Sundress",
                  price: 89.99,
                  rating: 4.8,
                  reviews: 24,
                  image: "/placeholder.svg?height=300&width=300",
                  vendor: "Island Fashion Co.",
                  isNew: true,
                },
                {
                  name: "Linen Beach Shirt",
                  price: 65.0,
                  rating: 4.6,
                  reviews: 18,
                  image: "/placeholder.svg?height=300&width=300",
                  vendor: "Coastal Threads",
                },
                {
                  name: "Traditional Creole Blouse",
                  price: 120.0,
                  rating: 5.0,
                  reviews: 12,
                  image: "/placeholder.svg?height=300&width=300",
                  vendor: "Heritage Designs",
                },
                {
                  name: "Coconut Shell Necklace",
                  price: 45.0,
                  rating: 4.7,
                  reviews: 32,
                  image: "/placeholder.svg?height=300&width=300",
                  vendor: "Island Crafts Co.",
                },
                {
                  name: "Handwoven Straw Hat",
                  price: 38.5,
                  rating: 4.5,
                  reviews: 15,
                  image: "/placeholder.svg?height=300&width=300",
                  vendor: "Praslin Artisans",
                },
              ].map((product, index) => (
                <ProductCard
                  key={index}
                  name={product.name}
                  price={product.price}
                  rating={product.rating}
                  reviews={product.reviews}
                  image={product.image}
                  vendor={product.vendor}
                  isNew={product.isNew}
                />
              ))}
            </div>
          </div>
        </section>

        {/* Filter and Products */}
        <section className="py-8 bg-gray-50">
          <div className="container px-4 md:px-6">
            <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
              {/* Filters Sidebar */}
              <div className="lg:col-span-1">
                <div className="bg-white rounded-lg shadow-sm p-6">
                  <div className="flex items-center justify-between mb-4">
                    <h3 className="font-bold text-lg">Filters</h3>
                    <Button variant="ghost" size="sm" className="text-gray-500 hover:text-emerald-600 p-0 h-auto">
                      Clear All
                    </Button>
                  </div>

                  {/* Price Range */}
                  <div className="py-4 border-t border-gray-100">
                    <h4 className="font-medium mb-3">Price Range</h4>
                    <div className="space-y-2">
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-gray-500">SCR 0</span>
                        <span className="text-sm text-gray-500">SCR 500+</span>
                      </div>
                      <div className="relative h-1 bg-gray-200 rounded-full">
                        <div className="absolute h-1 bg-emerald-500 rounded-full left-0 right-1/2"></div>
                        <div className="absolute w-4 h-4 bg-white border-2 border-emerald-500 rounded-full top-1/2 transform -translate-y-1/2 -translate-x-1/2 left-1/4"></div>
                        <div className="absolute w-4 h-4 bg-white border-2 border-emerald-500 rounded-full top-1/2 transform -translate-y-1/2 -translate-x-1/2 left-3/4"></div>
                      </div>
                      <div className="flex gap-2 mt-4">
                        <Input type="number" placeholder="Min" className="h-8 text-sm" />
                        <span className="flex items-center text-gray-400">-</span>
                        <Input type="number" placeholder="Max" className="h-8 text-sm" />
                        <Button size="sm" className="h-8 bg-emerald-600 hover:bg-emerald-700">
                          Go
                        </Button>
                      </div>
                    </div>
                  </div>

                  {/* Vendor */}
                  <div className="py-4 border-t border-gray-100">
                    <h4 className="font-medium mb-3">Vendor</h4>
                    <div className="space-y-2">
                      {[
                        "Island Fashion Co.",
                        "Coastal Threads",
                        "Heritage Designs",
                        "Praslin Artisans",
                        "Victoria Boutique",
                      ].map((vendor, index) => (
                        <div key={index} className="flex items-center">
                          <input
                            type="checkbox"
                            id={`vendor-${index}`}
                            className="h-4 w-4 rounded border-gray-300 text-emerald-600 focus:ring-emerald-500"
                          />
                          <label htmlFor={`vendor-${index}`} className="ml-2 text-sm text-gray-700">
                            {vendor}
                          </label>
                        </div>
                      ))}
                      <Button variant="link" size="sm" className="text-emerald-600 hover:text-emerald-700 p-0 h-auto">
                        Show More
                      </Button>
                    </div>
                  </div>

                  {/* Size */}
                  <div className="py-4 border-t border-gray-100">
                    <h4 className="font-medium mb-3">Size</h4>
                    <div className="flex flex-wrap gap-2">
                      {["XS", "S", "M", "L", "XL", "XXL"].map((size, index) => (
                        <div
                          key={index}
                          className="border border-gray-200 rounded-md px-3 py-1 text-sm cursor-pointer hover:border-emerald-500 hover:bg-emerald-50"
                        >
                          {size}
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Color */}
                  <div className="py-4 border-t border-gray-100">
                    <h4 className="font-medium mb-3">Color</h4>
                    <div className="flex flex-wrap gap-2">
                      {[
                        { name: "White", color: "bg-white border border-gray-200" },
                        { name: "Black", color: "bg-black" },
                        { name: "Blue", color: "bg-blue-500" },
                        { name: "Red", color: "bg-red-500" },
                        { name: "Green", color: "bg-green-500" },
                        { name: "Yellow", color: "bg-yellow-400" },
                      ].map((color, index) => (
                        <div
                          key={index}
                          className={`h-6 w-6 rounded-full ${color.color} cursor-pointer`}
                          title={color.name}
                        ></div>
                      ))}
                    </div>
                  </div>

                  {/* Rating */}
                  <div className="py-4 border-t border-gray-100">
                    <h4 className="font-medium mb-3">Rating</h4>
                    <div className="space-y-2">
                      {[5, 4, 3, 2, 1].map((rating) => (
                        <div key={rating} className="flex items-center">
                          <input
                            type="radio"
                            id={`rating-${rating}`}
                            name="rating"
                            className="h-4 w-4 border-gray-300 text-emerald-600 focus:ring-emerald-500"
                          />
                          <label htmlFor={`rating-${rating}`} className="ml-2 flex items-center">
                            {Array(5)
                              .fill(0)
                              .map((_, i) => (
                                <Star
                                  key={i}
                                  className={`h-4 w-4 ${i < rating ? "text-yellow-400 fill-yellow-400" : "text-gray-300"}`}
                                />
                              ))}
                            <span className="ml-1 text-sm text-gray-700">{rating === 5 ? "& up" : "& up"}</span>
                          </label>
                        </div>
                      ))}
                    </div>
                  </div>

                  <Button className="w-full mt-4 bg-emerald-600 hover:bg-emerald-700">Apply Filters</Button>
                </div>
              </div>

              {/* Products Grid */}
              <div className="lg:col-span-3">
                <div className="bg-white rounded-lg shadow-sm p-6">
                  <div className="flex items-center justify-between mb-6">
                    <h3 className="font-bold text-lg">All Products (156)</h3>
                    <div className="flex items-center gap-3">
                      <span className="text-sm text-gray-500">View:</span>
                      <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          width="24"
                          height="24"
                          viewBox="0 0 24 24"
                          fill="none"
                          stroke="currentColor"
                          strokeWidth="2"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          className="h-4 w-4"
                        >
                          <rect width="7" height="7" x="3" y="3" rx="1" />
                          <rect width="7" height="7" x="14" y="3" rx="1" />
                          <rect width="7" height="7" x="14" y="14" rx="1" />
                          <rect width="7" height="7" x="3" y="14" rx="1" />
                        </svg>
                      </Button>
                      <Button variant="ghost" size="sm" className="h-8 w-8 p-0 text-emerald-600">
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          width="24"
                          height="24"
                          viewBox="0 0 24 24"
                          fill="none"
                          stroke="currentColor"
                          strokeWidth="2"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          className="h-4 w-4"
                        >
                          <line x1="8" x2="21" y1="6" y2="6" />
                          <line x1="8" x2="21" y1="12" y2="12" />
                          <line x1="8" x2="21" y1="18" y2="18" />
                          <line x1="3" x2="3.01" y1="6" y2="6" />
                          <line x1="3" x2="3.01" y1="12" y2="12" />
                          <line x1="3" x2="3.01" y1="18" y2="18" />
                        </svg>
                      </Button>
                    </div>
                  </div>

                  <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
                    {Array(9)
                      .fill(0)
                      .map((_, index) => (
                        <ProductCard
                          key={index}
                          name={
                            [
                              "Tropical Print Maxi Dress",
                              "Linen Beach Shirt",
                              "Handwoven Straw Hat",
                              "Seychelles Flag T-Shirt",
                              "Coconut Shell Earrings",
                              "Batik Print Sarong",
                              "Coral Pattern Swimsuit",
                              "Embroidered Cotton Blouse",
                              "Woven Leather Sandals",
                            ][index]
                          }
                          price={[79.99, 65.0, 38.5, 29.99, 25.0, 45.0, 85.0, 58.5, 72.0][index]}
                          rating={[4.5, 4.8, 4.2, 4.7, 4.9, 4.6, 4.3, 4.7, 4.8][index]}
                          reviews={[18, 24, 12, 32, 15, 8, 22, 19, 27][index]}
                          image="/placeholder.svg?height=300&width=300"
                          vendor={
                            [
                              "Island Fashion Co.",
                              "Coastal Threads",
                              "Praslin Artisans",
                              "Seychelles Souvenirs",
                              "Island Crafts Co.",
                              "Heritage Designs",
                              "Beach Boutique",
                              "Victoria Textiles",
                              "Mahé Footwear",
                            ][index]
                          }
                          isNew={index === 3 || index === 6}
                        />
                      ))}
                  </div>

                  {/* Pagination */}
                  <div className="flex items-center justify-between mt-8 pt-6 border-t border-gray-100">
                    <Button
                      variant="outline"
                      size="sm"
                      className="gap-1 border-emerald-200 text-emerald-600 hover:bg-emerald-50"
                    >
                      <ArrowLeft className="h-4 w-4" /> Previous
                    </Button>
                    <div className="flex items-center gap-1">
                      {[1, 2, 3, 4, 5].map((page) => (
                        <Button
                          key={page}
                          variant={page === 1 ? "default" : "outline"}
                          size="sm"
                          className={
                            page === 1
                              ? "bg-emerald-600 hover:bg-emerald-700 h-8 w-8 p-0"
                              : "border-gray-200 h-8 w-8 p-0"
                          }
                        >
                          {page}
                        </Button>
                      ))}
                      <span className="mx-1">...</span>
                      <Button variant="outline" size="sm" className="border-gray-200 h-8 w-8 p-0">
                        12
                      </Button>
                    </div>
                    <Button
                      variant="outline"
                      size="sm"
                      className="gap-1 border-emerald-200 text-emerald-600 hover:bg-emerald-50"
                    >
                      Next <ArrowRight className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Recently Viewed */}
        <section className="py-8 bg-white">
          <div className="container px-4 md:px-6">
            <h2 className="text-xl font-bold mb-6">Recently Viewed</h2>
            <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-4">
              {[
                {
                  name: "Seychelles Print Sundress",
                  price: 89.99,
                  image: "/placeholder.svg?height=200&width=200",
                },
                {
                  name: "Linen Beach Shirt",
                  price: 65.0,
                  image: "/placeholder.svg?height=200&width=200",
                },
                {
                  name: "Coconut Shell Necklace",
                  price: 45.0,
                  image: "/placeholder.svg?height=200&width=200",
                },
                {
                  name: "Handwoven Straw Hat",
                  price: 38.5,
                  image: "/placeholder.svg?height=200&width=200",
                },
                {
                  name: "Batik Print Sarong",
                  price: 45.0,
                  image: "/placeholder.svg?height=200&width=200",
                },
                {
                  name: "Coral Pattern Swimsuit",
                  price: 85.0,
                  image: "/placeholder.svg?height=200&width=200",
                },
              ].map((product, index) => (
                <Link
                  href={`/shopping/product/${product.name.toLowerCase().replace(/\s+/g, "-")}`}
                  key={index}
                  className="group"
                >
                  <div className="bg-white rounded-lg shadow-sm overflow-hidden transition-all duration-200 hover:shadow-md">
                    <div className="relative aspect-square overflow-hidden">
                      <Image
                        src={product.image || "/placeholder.svg"}
                        width={200}
                        height={200}
                        alt={product.name}
                        className="object-cover w-full h-full group-hover:scale-105 transition-transform duration-300"
                      />
                    </div>
                    <div className="p-3">
                      <h3 className="font-medium text-gray-800 line-clamp-1 group-hover:text-emerald-600 transition-colors text-sm">
                        {product.name}
                      </h3>
                      <span className="text-sm font-bold text-emerald-600">${product.price.toFixed(2)}</span>
                    </div>
                  </div>
                </Link>
              ))}
            </div>
          </div>
        </section>
      </main>

      {/* Footer */}
      <footer className="bg-white border-t py-12">
        <div className="container px-4 md:px-6">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            <div className="space-y-4">
              <Link href="/" className="flex items-center space-x-2">
                <span className="text-2xl font-bold bg-gradient-to-r from-blue-600 to-teal-500 bg-clip-text text-transparent">
                  Kominote
                </span>
              </Link>
              <p className="text-sm text-gray-600">
                Seychelles' premier multivendor marketplace connecting local vendors with customers.
              </p>
            </div>
            <div className="space-y-4">
              <h3 className="font-medium">Shopping</h3>
              <ul className="space-y-2">
                <li>
                  <Link href="#" className="text-sm text-gray-600 hover:text-emerald-600">
                    All Categories
                  </Link>
                </li>
                <li>
                  <Link href="#" className="text-sm text-gray-600 hover:text-emerald-600">
                    Deals & Promotions
                  </Link>
                </li>
                <li>
                  <Link href="#" className="text-sm text-gray-600 hover:text-emerald-600">
                    New Arrivals
                  </Link>
                </li>
                <li>
                  <Link href="#" className="text-sm text-gray-600 hover:text-emerald-600">
                    Best Sellers
                  </Link>
                </li>
              </ul>
            </div>
            <div className="space-y-4">
              <h3 className="font-medium">Vendors</h3>
              <ul className="space-y-2">
                <li>
                  <Link href="#" className="text-sm text-gray-600 hover:text-emerald-600">
                    Become a Vendor
                  </Link>
                </li>
                <li>
                  <Link href="#" className="text-sm text-gray-600 hover:text-emerald-600">
                    Vendor Directory
                  </Link>
                </li>
                <li>
                  <Link href="#" className="text-sm text-gray-600 hover:text-emerald-600">
                    Vendor Resources
                  </Link>
                </li>
                <li>
                  <Link href="#" className="text-sm text-gray-600 hover:text-emerald-600">
                    Success Stories
                  </Link>
                </li>
              </ul>
            </div>
            <div className="space-y-4">
              <h3 className="font-medium">Help & Support</h3>
              <ul className="space-y-2">
                <li>
                  <Link href="#" className="text-sm text-gray-600 hover:text-emerald-600">
                    Customer Service
                  </Link>
                </li>
                <li>
                  <Link href="#" className="text-sm text-gray-600 hover:text-emerald-600">
                    Shipping & Delivery
                  </Link>
                </li>
                <li>
                  <Link href="#" className="text-sm text-gray-600 hover:text-emerald-600">
                    Returns & Refunds
                  </Link>
                </li>
                <li>
                  <Link href="#" className="text-sm text-gray-600 hover:text-emerald-600">
                    FAQ
                  </Link>
                </li>
              </ul>
            </div>
          </div>
          <div className="border-t mt-12 pt-8 flex flex-col md:flex-row justify-between items-center">
            <p className="text-sm text-gray-600">
              &copy; {new Date().getFullYear()} Kominote Shopping. All rights reserved.
            </p>
            <div className="flex items-center gap-4 mt-4 md:mt-0">
              <Link href="#" className="text-gray-600 hover:text-emerald-600">
                Terms of Service
              </Link>
              <Link href="#" className="text-gray-600 hover:text-emerald-600">
                Privacy Policy
              </Link>
              <Link href="#" className="text-gray-600 hover:text-emerald-600">
                Cookie Policy
              </Link>
            </div>
          </div>
        </div>
      </footer>

      {/* Side Navigation */}
      <FeatureNavigation currentFeature="shopping" colorScheme="emerald" />
    </div>
  )
}

// Subcategory Card Component
function SubcategoryCard({ title, count, image }) {
  return (
    <Link href={`/shopping/category/clothing/${title.toLowerCase().replace(/\s+/g, "-")}`} className="group">
      <div className="bg-white rounded-lg shadow-sm overflow-hidden transition-all duration-200 hover:shadow-md">
        <div className="relative h-32 overflow-hidden">
          <Image
            src={image || "/placeholder.svg"}
            fill
            alt={title}
            className="object-cover group-hover:scale-105 transition-transform duration-300"
          />
        </div>
        <div className="p-3 text-center">
          <h3 className="font-medium text-gray-800 group-hover:text-emerald-600 transition-colors">{title}</h3>
          <span className="text-xs text-gray-500">{count} products</span>
        </div>
      </div>
    </Link>
  )
}

// Product Card Component
function ProductCard({ name, price, rating, reviews, image, vendor, isNew = false }) {
  return (
    <Link href={`/shopping/product/${name.toLowerCase().replace(/\s+/g, "-")}`} className="group">
      <div className="bg-white rounded-lg shadow-sm overflow-hidden transition-all duration-200 hover:shadow-md">
        <div className="relative">
          <div className="aspect-square overflow-hidden">
            <Image
              src={image || "/placeholder.svg"}
              width={300}
              height={300}
              alt={name}
              className="object-cover w-full h-full group-hover:scale-105 transition-transform duration-300"
            />
          </div>
          <Button
            variant="ghost"
            size="icon"
            className="absolute top-2 right-2 h-8 w-8 rounded-full bg-white/80 text-gray-600 hover:text-emerald-600 hover:bg-white"
          >
            <Heart className="h-4 w-4" />
          </Button>
          {isNew && <Badge className="absolute top-2 left-2 bg-emerald-600">New</Badge>}
        </div>
        <div className="p-3">
          <div className="flex items-center gap-1 mb-1">
            <Star className="h-3 w-3 text-yellow-400 fill-yellow-400" />
            <span className="text-xs text-gray-500">
              {rating} ({reviews})
            </span>
          </div>
          <h3 className="font-medium text-gray-800 line-clamp-1 group-hover:text-emerald-600 transition-colors">
            {name}
          </h3>
          <p className="text-xs text-gray-500 mt-1 line-clamp-1">{vendor}</p>
          <div className="flex items-center justify-between mt-2">
            <span className="text-sm font-bold text-emerald-600">${price.toFixed(2)}</span>
            <Button
              variant="ghost"
              size="sm"
              className="h-8 w-8 p-0 rounded-full bg-emerald-100 text-emerald-600 hover:bg-emerald-200"
            >
              <ShoppingCart className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </div>
    </Link>
  )
}

