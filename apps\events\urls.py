"""
URL configuration for events app.

Handles event management, bookings, calendar, and event-related features.
"""
from django.urls import path
from . import views

app_name = 'events'

urlpatterns = [
    # Events main views
    path('', views.EventsHomeView.as_view(), name='events-home'),
    path('events/', views.EventListView.as_view(), name='event-list'),
    path('events/<int:pk>/', views.EventDetailView.as_view(), name='event-detail'),
    path('events/create/', views.EventCreateView.as_view(), name='event-create'),
    path('events/<int:pk>/edit/', views.EventEditView.as_view(), name='event-edit'),
    
    # Event categories and filtering
    path('categories/', views.EventCategoryListView.as_view(), name='category-list'),
    path('categories/<int:pk>/', views.EventCategoryDetailView.as_view(), name='category-detail'),
    path('calendar/', views.EventCalendarView.as_view(), name='event-calendar'),
    
    # Bookings and attendance
    path('events/<int:pk>/book/', views.EventBookingView.as_view(), name='event-book'),
    path('bookings/', views.BookingListView.as_view(), name='booking-list'),
    path('bookings/<int:pk>/', views.BookingDetailView.as_view(), name='booking-detail'),
    path('events/<int:pk>/attendees/', views.EventAttendeesView.as_view(), name='event-attendees'),
    
    # Event organizer features
    path('organizer/dashboard/', views.OrganizerDashboardView.as_view(), name='organizer-dashboard'),
    path('organizer/events/', views.OrganizerEventsView.as_view(), name='organizer-events'),
]
