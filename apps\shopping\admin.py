"""
Admin interface for shopping app.

Provides comprehensive admin interface for managing e-commerce functionality
including products, vendors, orders, and marketplace features.
"""
from django.contrib import admin
from django.utils.html import format_html
from django.urls import reverse
from .models import (
    ProductCategory, Vendor, Product, ProductImage, ShoppingCart, CartItem,
    Order, OrderItem, ProductReview, Wishlist, WishlistItem
)


@admin.register(ProductCategory)
class ProductCategoryAdmin(admin.ModelAdmin):
    """Admin interface for product categories."""
    list_display = ['name', 'slug', 'is_local_category', 'is_featured', 'is_active', 'product_count', 'display_order']
    list_filter = ['is_local_category', 'is_featured', 'is_active', 'parent', 'created_at']
    search_fields = ['name', 'description']
    prepopulated_fields = {'slug': ('name',)}
    ordering = ['display_order', 'name']

    fieldsets = (
        ('Basic Information', {
            'fields': ('name', 'slug', 'description', 'parent')
        }),
        ('Visual Elements', {
            'fields': ('icon', 'banner_image'),
            'classes': ('collapse',)
        }),
        ('Settings', {
            'fields': ('is_local_category', 'is_featured', 'is_active', 'display_order')
        }),
    )

    def product_count(self, obj):
        """Display number of products in this category."""
        count = obj.product_count
        if count > 0:
            url = reverse('admin:shopping_product_changelist') + f'?category__id__exact={obj.id}'
            return format_html('<a href="{}">{} products</a>', url, count)
        return '0 products'
    product_count.short_description = 'Products'


@admin.register(Vendor)
class VendorAdmin(admin.ModelAdmin):
    """Admin interface for vendors."""
    list_display = [
        'business_name', 'user', 'vendor_type', 'island', 'is_verified',
        'is_active', 'total_orders', 'total_sales', 'average_rating'
    ]
    list_filter = [
        'vendor_type', 'island', 'is_verified', 'is_active', 'is_featured', 'created_at'
    ]
    search_fields = ['business_name', 'user__username', 'user__email', 'business_email']
    autocomplete_fields = ['user']
    readonly_fields = ['total_sales', 'total_orders', 'average_rating', 'rating_count']

    fieldsets = (
        ('User & Business Info', {
            'fields': ('user', 'business_name', 'business_registration', 'vendor_type')
        }),
        ('Contact Information', {
            'fields': ('business_email', 'business_phone')
        }),
        ('Location', {
            'fields': ('island', 'district', 'address')
        }),
        ('Business Details', {
            'fields': ('description', 'logo', 'banner', 'business_hours')
        }),
        ('Status & Verification', {
            'fields': ('is_verified', 'is_active', 'is_featured')
        }),
        ('Statistics', {
            'fields': ('total_sales', 'total_orders', 'average_rating', 'rating_count'),
            'classes': ('collapse',)
        }),
    )

    actions = ['verify_vendors', 'unverify_vendors', 'activate_vendors', 'deactivate_vendors']

    def verify_vendors(self, request, queryset):
        """Mark selected vendors as verified."""
        updated = queryset.update(is_verified=True)
        self.message_user(request, f'{updated} vendors marked as verified.')
    verify_vendors.short_description = 'Mark selected vendors as verified'

    def unverify_vendors(self, request, queryset):
        """Mark selected vendors as unverified."""
        updated = queryset.update(is_verified=False)
        self.message_user(request, f'{updated} vendors marked as unverified.')
    unverify_vendors.short_description = 'Mark selected vendors as unverified'

    def activate_vendors(self, request, queryset):
        """Activate selected vendors."""
        updated = queryset.update(is_active=True)
        self.message_user(request, f'{updated} vendors activated.')
    activate_vendors.short_description = 'Activate selected vendors'

    def deactivate_vendors(self, request, queryset):
        """Deactivate selected vendors."""
        updated = queryset.update(is_active=False)
        self.message_user(request, f'{updated} vendors deactivated.')
    deactivate_vendors.short_description = 'Deactivate selected vendors'


class ProductImageInline(admin.TabularInline):
    """Inline admin for product images."""
    model = ProductImage
    extra = 1
    fields = ['image', 'alt_text', 'display_order']


@admin.register(Product)
class ProductAdmin(admin.ModelAdmin):
    """Admin interface for products."""
    list_display = [
        'name', 'vendor', 'category', 'price', 'stock_quantity', 'is_local_product',
        'is_active', 'is_featured', 'sales_count', 'average_rating', 'created_at'
    ]
    list_filter = [
        'category', 'vendor', 'condition', 'is_local_product', 'is_active',
        'is_featured', 'is_digital', 'origin_island', 'created_at'
    ]
    search_fields = ['name', 'description', 'brand', 'model', 'tags']
    prepopulated_fields = {'slug': ('name',)}
    autocomplete_fields = ['vendor', 'category']
    readonly_fields = ['view_count', 'sales_count', 'average_rating', 'rating_count']
    date_hierarchy = 'created_at'

    fieldsets = (
        ('Basic Information', {
            'fields': ('name', 'slug', 'description', 'short_description', 'vendor', 'category')
        }),
        ('Pricing & Inventory', {
            'fields': ('price', 'original_price', 'stock_quantity', 'low_stock_threshold', 'track_inventory')
        }),
        ('Product Details', {
            'fields': ('condition', 'brand', 'model', 'weight', 'dimensions'),
            'classes': ('collapse',)
        }),
        ('Seychelles Context', {
            'fields': ('is_local_product', 'origin_island', 'local_materials'),
            'classes': ('collapse',)
        }),
        ('Media', {
            'fields': ('featured_image',)
        }),
        ('SEO & Marketing', {
            'fields': ('meta_title', 'meta_description', 'tags'),
            'classes': ('collapse',)
        }),
        ('Status & Visibility', {
            'fields': ('is_active', 'is_featured', 'is_digital')
        }),
        ('Statistics', {
            'fields': ('view_count', 'sales_count', 'average_rating', 'rating_count'),
            'classes': ('collapse',)
        }),
    )

    inlines = [ProductImageInline]

    actions = ['activate_products', 'deactivate_products', 'feature_products', 'unfeature_products']

    def activate_products(self, request, queryset):
        """Activate selected products."""
        updated = queryset.update(is_active=True)
        self.message_user(request, f'{updated} products activated.')
    activate_products.short_description = 'Activate selected products'

    def deactivate_products(self, request, queryset):
        """Deactivate selected products."""
        updated = queryset.update(is_active=False)
        self.message_user(request, f'{updated} products deactivated.')
    deactivate_products.short_description = 'Deactivate selected products'

    def feature_products(self, request, queryset):
        """Feature selected products."""
        updated = queryset.update(is_featured=True)
        self.message_user(request, f'{updated} products featured.')
    feature_products.short_description = 'Feature selected products'

    def unfeature_products(self, request, queryset):
        """Unfeature selected products."""
        updated = queryset.update(is_featured=False)
        self.message_user(request, f'{updated} products unfeatured.')
    unfeature_products.short_description = 'Unfeature selected products'


class CartItemInline(admin.TabularInline):
    """Inline admin for cart items."""
    model = CartItem
    extra = 0
    readonly_fields = ['total_price']
    autocomplete_fields = ['product']


@admin.register(ShoppingCart)
class ShoppingCartAdmin(admin.ModelAdmin):
    """Admin interface for shopping carts."""
    list_display = ['user', 'total_items', 'total_amount', 'updated_at']
    search_fields = ['user__username', 'user__email']
    autocomplete_fields = ['user']
    readonly_fields = ['total_items', 'total_amount']

    inlines = [CartItemInline]

    fieldsets = (
        ('Cart Information', {
            'fields': ('user', 'total_items', 'total_amount')
        }),
    )


class OrderItemInline(admin.TabularInline):
    """Inline admin for order items."""
    model = OrderItem
    extra = 0
    readonly_fields = ['total_price']
    autocomplete_fields = ['product', 'vendor']


@admin.register(Order)
class OrderAdmin(admin.ModelAdmin):
    """Admin interface for orders."""
    list_display = [
        'order_number', 'customer', 'status', 'payment_status', 'total_amount',
        'shipping_island', 'created_at'
    ]
    list_filter = [
        'status', 'payment_status', 'shipping_island', 'created_at', 'updated_at'
    ]
    search_fields = [
        'order_number', 'customer__username', 'customer__email',
        'shipping_name', 'shipping_email'
    ]
    autocomplete_fields = ['customer']
    readonly_fields = ['order_number']
    date_hierarchy = 'created_at'

    fieldsets = (
        ('Order Information', {
            'fields': ('order_number', 'customer', 'status', 'payment_status')
        }),
        ('Pricing', {
            'fields': ('subtotal', 'shipping_cost', 'tax_amount', 'total_amount')
        }),
        ('Shipping Information', {
            'fields': (
                'shipping_name', 'shipping_email', 'shipping_phone',
                'shipping_island', 'shipping_district', 'shipping_address'
            )
        }),
        ('Notes', {
            'fields': ('customer_notes', 'admin_notes'),
            'classes': ('collapse',)
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at', 'shipped_at', 'delivered_at'),
            'classes': ('collapse',)
        }),
    )

    inlines = [OrderItemInline]

    actions = ['mark_confirmed', 'mark_processing', 'mark_shipped', 'mark_delivered']

    def mark_confirmed(self, request, queryset):
        """Mark orders as confirmed."""
        updated = queryset.update(status='confirmed')
        self.message_user(request, f'{updated} orders marked as confirmed.')
    mark_confirmed.short_description = 'Mark as confirmed'

    def mark_processing(self, request, queryset):
        """Mark orders as processing."""
        updated = queryset.update(status='processing')
        self.message_user(request, f'{updated} orders marked as processing.')
    mark_processing.short_description = 'Mark as processing'

    def mark_shipped(self, request, queryset):
        """Mark orders as shipped."""
        from django.utils import timezone
        updated = queryset.update(status='shipped', shipped_at=timezone.now())
        self.message_user(request, f'{updated} orders marked as shipped.')
    mark_shipped.short_description = 'Mark as shipped'

    def mark_delivered(self, request, queryset):
        """Mark orders as delivered."""
        from django.utils import timezone
        updated = queryset.update(status='delivered', delivered_at=timezone.now())
        self.message_user(request, f'{updated} orders marked as delivered.')
    mark_delivered.short_description = 'Mark as delivered'


@admin.register(OrderItem)
class OrderItemAdmin(admin.ModelAdmin):
    """Admin interface for order items."""
    list_display = ['order', 'product', 'vendor', 'quantity', 'product_price', 'total_price', 'created_at']
    list_filter = ['vendor', 'created_at']
    search_fields = ['order__order_number', 'product__name', 'product_name']
    autocomplete_fields = ['order', 'product', 'vendor']
    readonly_fields = ['total_price']

    fieldsets = (
        ('Order Information', {
            'fields': ('order', 'product', 'vendor')
        }),
        ('Product Details', {
            'fields': ('product_name', 'product_price', 'quantity', 'total_price')
        }),
    )


@admin.register(ProductReview)
class ProductReviewAdmin(admin.ModelAdmin):
    """Admin interface for product reviews."""
    list_display = [
        'product', 'customer', 'rating', 'title', 'is_approved',
        'is_verified_purchase', 'helpful_count', 'created_at'
    ]
    list_filter = [
        'rating', 'is_approved', 'is_verified_purchase', 'created_at'
    ]
    search_fields = ['product__name', 'customer__username', 'title', 'content']
    autocomplete_fields = ['product', 'customer', 'order_item']
    readonly_fields = ['helpful_count']

    fieldsets = (
        ('Review Information', {
            'fields': ('product', 'customer', 'order_item', 'rating')
        }),
        ('Review Content', {
            'fields': ('title', 'content')
        }),
        ('Review Images', {
            'fields': ('image1', 'image2', 'image3'),
            'classes': ('collapse',)
        }),
        ('Moderation', {
            'fields': ('is_approved', 'is_verified_purchase', 'helpful_count')
        }),
    )

    actions = ['approve_reviews', 'unapprove_reviews']

    def approve_reviews(self, request, queryset):
        """Approve selected reviews."""
        updated = queryset.update(is_approved=True)
        self.message_user(request, f'{updated} reviews approved.')
    approve_reviews.short_description = 'Approve selected reviews'

    def unapprove_reviews(self, request, queryset):
        """Unapprove selected reviews."""
        updated = queryset.update(is_approved=False)
        self.message_user(request, f'{updated} reviews unapproved.')
    unapprove_reviews.short_description = 'Unapprove selected reviews'


class WishlistItemInline(admin.TabularInline):
    """Inline admin for wishlist items."""
    model = WishlistItem
    extra = 0
    autocomplete_fields = ['product']


@admin.register(Wishlist)
class WishlistAdmin(admin.ModelAdmin):
    """Admin interface for wishlists."""
    list_display = ['name', 'user', 'item_count', 'is_public', 'updated_at']
    list_filter = ['is_public', 'created_at']
    search_fields = ['name', 'description', 'user__username']
    autocomplete_fields = ['user']
    readonly_fields = ['item_count']

    inlines = [WishlistItemInline]

    fieldsets = (
        ('Wishlist Information', {
            'fields': ('user', 'name', 'description', 'item_count')
        }),
        ('Privacy', {
            'fields': ('is_public',)
        }),
    )


# Customize admin site header for shopping
admin.site.site_header = "Kominote Shopping Administration"
admin.site.site_title = "Kominote Shopping Admin"
admin.site.index_title = "Welcome to Kominote Shopping Administration"
