"""
Views for streaming app.

Handles video content, streaming services, playlists, and media management.
"""
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework.permissions import AllowAny, IsAuthenticated


class StreamingHomeView(APIView):
    permission_classes = [AllowAny]
    def get(self, request):
        return Response({'message': 'Streaming Home', 'status': 'Phase 2 Complete'})

class VideoListView(APIView):
    permission_classes = [AllowAny]
    def get(self, request):
        return Response({'message': 'Video List', 'status': 'Coming in Phase 3'})

class VideoDetailView(APIView):
    permission_classes = [AllowAny]
    def get(self, request, pk):
        return Response({'message': f'Video Detail {pk}', 'status': 'Coming in Phase 3'})

class VideoWatchView(APIView):
    permission_classes = [AllowAny]
    def get(self, request, pk):
        return Response({'message': f'Watch Video {pk}', 'status': 'Coming in Phase 3'})

class CategoryListView(APIView):
    permission_classes = [AllowAny]
    def get(self, request):
        return Response({'message': 'Category List', 'status': 'Coming in Phase 3'})

class CategoryDetailView(APIView):
    permission_classes = [AllowAny]
    def get(self, request, pk):
        return Response({'message': f'Category Detail {pk}', 'status': 'Coming in Phase 3'})

class PlaylistListView(APIView):
    permission_classes = [IsAuthenticated]
    def get(self, request):
        return Response({'message': 'Playlist List', 'status': 'Coming in Phase 3'})

class PlaylistDetailView(APIView):
    permission_classes = [IsAuthenticated]
    def get(self, request, pk):
        return Response({'message': f'Playlist Detail {pk}', 'status': 'Coming in Phase 3'})

class FavoriteVideosView(APIView):
    permission_classes = [IsAuthenticated]
    def get(self, request):
        return Response({'message': 'Favorite Videos', 'status': 'Coming in Phase 3'})

class WatchHistoryView(APIView):
    permission_classes = [IsAuthenticated]
    def get(self, request):
        return Response({'message': 'Watch History', 'status': 'Coming in Phase 3'})
